<template>
  <view class="loading-container" v-if="visible">
    <view class="loading-overlay" v-if="overlay" @tap="handleOverlayTap"></view>
    <view class="loading-content" :class="{ 'with-overlay': overlay }">
      <view class="spinner-container">
        <view class="spinner" :class="type">
          <view class="dot" v-if="type === 'dots'"></view>
          <view class="dot" v-if="type === 'dots'"></view>
          <view class="dot" v-if="type === 'dots'"></view>
          <view class="ring" v-if="type === 'ring'"></view>
          <view class="pulse" v-if="type === 'pulse'"></view>
        </view>
      </view>
      
      <text class="loading-text" v-if="text">{{ text }}</text>
      
      <view class="loading-progress" v-if="showProgress">
        <view class="progress-bar">
          <view class="progress-fill" :style="{ width: progress + '%' }"></view>
        </view>
        <text class="progress-text">{{ progress }}%</text>
      </view>
      
      <text class="cancel-btn" v-if="showCancel" @tap="handleCancel">取消</text>
    </view>
  </view>
</template>

<script>
export default {
  name: 'LoadingSpinner',
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    text: {
      type: String,
      default: ''
    },
    type: {
      type: String,
      default: 'ring' // ring, dots, pulse
    },
    overlay: {
      type: Boolean,
      default: true
    },
    showProgress: {
      type: Boolean,
      default: false
    },
    progress: {
      type: Number,
      default: 0
    },
    showCancel: {
      type: Boolean,
      default: false
    }
  },
  emits: ['cancel', 'overlay-tap'],
  setup(props, { emit }) {
    const handleCancel = () => {
      emit('cancel')
    }
    
    const handleOverlayTap = () => {
      emit('overlay-tap')
    }
    
    return {
      handleCancel,
      handleOverlayTap
    }
  }
}
</script>

<style scoped>
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 80rpx 40rpx;
  min-height: 300rpx;
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(20px);
  border-radius: 24rpx;
  border: 1px solid rgba(226, 232, 240, 0.8);
  box-shadow: 0 4rpx 12rpx rgba(15, 23, 42, 0.06);
  position: relative;
  overflow: hidden;
}

.loading-container::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 3rpx;
  background: linear-gradient(90deg, #8b5cf6, #7c3aed, #00b4d8, #0096c7);
  border-radius: 24rpx 24rpx 0 0;
}

.spinner {
  width: 80rpx;
  height: 80rpx;
  position: relative;
  margin-bottom: 32rpx;
}

.spinner-ring {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  border: 6rpx solid rgba(139, 92, 246, 0.1);
  border-radius: 50%;
  border-top-color: #8b5cf6;
  animation: spin 1.2s cubic-bezier(0.68, -0.55, 0.265, 1.55) infinite;
}

.spinner-ring:nth-child(2) {
  width: 60rpx;
  height: 60rpx;
  top: 10rpx;
  left: 10rpx;
  border-width: 4rpx;
  border-top-color: #00b4d8;
  animation-delay: -0.3s;
  animation-duration: 1s;
}

.spinner-ring:nth-child(3) {
  width: 40rpx;
  height: 40rpx;
  top: 20rpx;
  left: 20rpx;
  border-width: 3rpx;
  border-top-color: #10b981;
  animation-delay: -0.6s;
  animation-duration: 0.8s;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
    opacity: 1;
  }
  50% {
    opacity: 0.8;
  }
  100% {
    transform: rotate(360deg);
    opacity: 1;
  }
}

.loading-text {
  font-size: 24rpx;
  color: #475569;
  font-weight: 600;
  text-align: center;
  line-height: 1.5;
  margin-bottom: 16rpx;
}

.loading-progress {
  width: 200rpx;
  height: 6rpx;
  background: rgba(226, 232, 240, 0.8);
  border-radius: 3rpx;
  overflow: hidden;
  position: relative;
}

.progress-bar {
  height: 100%;
  background: linear-gradient(90deg, #8b5cf6, #00b4d8, #10b981);
  border-radius: 3rpx;
  animation: progress 2s ease-in-out infinite;
  width: 0%;
  transition: width 0.3s ease;
}

@keyframes progress {
  0% {
    width: 0%;
    transform: translateX(-100%);
  }
  50% {
    width: 60%;
    transform: translateX(0%);
  }
  100% {
    width: 100%;
    transform: translateX(100%);
  }
}

.loading-dots {
  display: flex;
  gap: 8rpx;
  margin-top: 20rpx;
}

.dot {
  width: 12rpx;
  height: 12rpx;
  border-radius: 50%;
  background: #8b5cf6;
  animation: dotPulse 1.5s ease-in-out infinite;
}

.dot:nth-child(2) {
  background: #00b4d8;
  animation-delay: 0.2s;
}

.dot:nth-child(3) {
  background: #10b981;
  animation-delay: 0.4s;
}

.dot:nth-child(4) {
  background: #f59e0b;
  animation-delay: 0.6s;
}

@keyframes dotPulse {
  0%, 80%, 100% {
    transform: scale(0.8);
    opacity: 0.5;
  }
  40% {
    transform: scale(1.2);
    opacity: 1;
  }
}

/* 简化版加载器 */
.simple-spinner {
  width: 60rpx;
  height: 60rpx;
  border: 4rpx solid rgba(139, 92, 246, 0.2);
  border-top: 4rpx solid #8b5cf6;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

/* 小尺寸加载器 */
.loading-container.small {
  padding: 40rpx 20rpx;
  min-height: 200rpx;
}

.loading-container.small .spinner {
  width: 60rpx;
  height: 60rpx;
  margin-bottom: 20rpx;
}

.loading-container.small .loading-text {
  font-size: 20rpx;
}

/* 大尺寸加载器 */
.loading-container.large {
  padding: 120rpx 60rpx;
  min-height: 400rpx;
}

.loading-container.large .spinner {
  width: 120rpx;
  height: 120rpx;
  margin-bottom: 40rpx;
}

.loading-container.large .loading-text {
  font-size: 28rpx;
}

/* 全屏加载遮罩 */
.fullscreen-loading {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(248, 250, 252, 0.8);
  backdrop-filter: blur(10px);
  z-index: 9999;
  display: flex;
  align-items: center;
  justify-content: center;
  animation: fadeIn 0.3s ease;
}

.fullscreen-content {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  border-radius: 24rpx;
  padding: 60rpx 40rpx;
  border: 1px solid rgba(226, 232, 240, 0.8);
  box-shadow: 0 8rpx 32rpx rgba(15, 23, 42, 0.15);
  text-align: center;
  min-width: 300rpx;
  animation: scaleIn 0.3s cubic-bezier(0.68, -0.55, 0.265, 1.55);
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes scaleIn {
  from {
    opacity: 0;
    transform: scale(0.8);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

/* 骨架屏加载效果 */
.skeleton-loader {
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(20px);
  border-radius: 24rpx;
  padding: 32rpx;
  border: 1px solid rgba(226, 232, 240, 0.8);
  box-shadow: 0 4rpx 12rpx rgba(15, 23, 42, 0.06);
}

.skeleton-item {
  height: 24rpx;
  background: linear-gradient(90deg, 
    rgba(226, 232, 240, 0.6) 25%, 
    rgba(255, 255, 255, 0.8) 50%, 
    rgba(226, 232, 240, 0.6) 75%
  );
  background-size: 200% 100%;
  border-radius: 12rpx;
  margin-bottom: 16rpx;
  animation: shimmer 1.5s infinite;
}

.skeleton-item:last-child {
  margin-bottom: 0;
}

.skeleton-item.title {
  height: 32rpx;
  width: 60%;
}

.skeleton-item.content {
  height: 20rpx;
  width: 100%;
}

.skeleton-item.short {
  width: 40%;
}

@keyframes shimmer {
  0% { background-position: -200% 0; }
  100% { background-position: 200% 0; }
}

/* 响应式设计 */
@media (max-width: 750rpx) {
  .loading-container {
    padding: 60rpx 30rpx;
    min-height: 250rpx;
  }
  
  .spinner {
    width: 60rpx;
    height: 60rpx;
  }
  
  .loading-text {
    font-size: 22rpx;
  }
  
  .fullscreen-content {
    padding: 40rpx 30rpx;
    min-width: 280rpx;
  }
}

/* 主题变体 */
.loading-container.primary {
  background: linear-gradient(135deg, rgba(139, 92, 246, 0.05), rgba(124, 58, 237, 0.05));
  border-color: rgba(139, 92, 246, 0.2);
}

.loading-container.success {
  background: linear-gradient(135deg, rgba(16, 185, 129, 0.05), rgba(5, 150, 105, 0.05));
  border-color: rgba(16, 185, 129, 0.2);
}

.loading-container.warning {
  background: linear-gradient(135deg, rgba(245, 158, 11, 0.05), rgba(217, 119, 6, 0.05));
  border-color: rgba(245, 158, 11, 0.2);
}

.loading-container.error {
  background: linear-gradient(135deg, rgba(239, 68, 68, 0.05), rgba(220, 38, 38, 0.05));
  border-color: rgba(239, 68, 68, 0.2);
}

/* 暗色模式适配 */
@media (prefers-color-scheme: dark) {
  .loading-container {
    background: rgba(15, 23, 42, 0.9);
    border-color: rgba(71, 85, 105, 0.8);
    color: #e2e8f0;
  }
  
  .loading-text {
    color: #cbd5e1;
  }
  
  .skeleton-item {
    background: linear-gradient(90deg, 
      rgba(71, 85, 105, 0.6) 25%, 
      rgba(100, 116, 139, 0.8) 50%, 
      rgba(71, 85, 105, 0.6) 75%
    );
  }
}
</style> 