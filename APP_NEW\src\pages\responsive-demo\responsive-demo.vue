<template>
  <view class="responsive-demo" :class="responsiveClasses">
    <!-- 响应式信息展示 -->
    <view class="device-info-card">
      <view class="card-header">
        <text class="card-title">设备信息</text>
        <text class="breakpoint-badge">{{ currentBreakpoint.toUpperCase() }}</text>
      </view>
      <view class="device-details">
        <view class="detail-item">
          <text class="detail-label">屏幕宽度:</text>
          <text class="detail-value">{{ deviceInfo?.windowWidth || 'N/A' }}px</text>
        </view>
        <view class="detail-item">
          <text class="detail-label">屏幕高度:</text>
          <text class="detail-value">{{ deviceInfo?.windowHeight || 'N/A' }}px</text>
        </view>
        <view class="detail-item">
          <text class="detail-label">设备平台:</text>
          <text class="detail-value">{{ deviceInfo?.platform || 'N/A' }}</text>
        </view>
        <view class="detail-item">
          <text class="detail-label">像素比:</text>
          <text class="detail-value">{{ deviceInfo?.pixelRatio || 'N/A' }}</text>
        </view>
      </view>
    </view>

    <!-- 响应式网格演示 -->
    <view class="demo-section">
      <text class="section-title">响应式网格</text>
      <view class="responsive-grid" :style="getGridStyles(4)">
        <view class="grid-item" v-for="n in 8" :key="n">
          <text class="item-text">项目 {{ n }}</text>
        </view>
      </view>
    </view>

    <!-- 响应式字体演示 */
    <view class="demo-section">
      <text class="section-title">响应式字体</text>
      <view class="font-demo">
        <text class="font-xs" :style="getTextStyles('xs')">超小字体 (XS)</text>
        <text class="font-sm" :style="getTextStyles('sm')">小字体 (SM)</text>
        <text class="font-base" :style="getTextStyles('base')">基础字体 (BASE)</text>
        <text class="font-lg" :style="getTextStyles('lg')">大字体 (LG)</text>
        <text class="font-xl" :style="getTextStyles('xl')">超大字体 (XL)</text>
        <text class="font-2xl" :style="getTextStyles('2xl')">2XL字体</text>
      </view>
    </view>

    <!-- 响应式间距演示 */
    <view class="demo-section">
      <text class="section-title">响应式间距</text>
      <view class="spacing-demo">
        <view class="spacing-item" :style="getSpacingStyles('xs')">
          <text>XS间距</text>
        </view>
        <view class="spacing-item" :style="getSpacingStyles('sm')">
          <text>SM间距</text>
        </view>
        <view class="spacing-item" :style="getSpacingStyles('base')">
          <text>基础间距</text>
        </view>
        <view class="spacing-item" :style="getSpacingStyles('lg')">
          <text>LG间距</text>
        </view>
        <view class="spacing-item" :style="getSpacingStyles('xl')">
          <text>XL间距</text>
        </view>
      </view>
    </view>

    <!-- 响应式卡片演示 */
    <view class="demo-section">
      <text class="section-title">响应式卡片</text>
      <view class="cards-demo">
        <view class="demo-card" :style="getCardStyles()" v-for="n in 3" :key="n">
          <view class="card-icon" :style="{ width: getIconSize('lg') + 'rpx', height: getIconSize('lg') + 'rpx' }">
            <text>{{ n }}</text>
          </view>
          <text class="card-title">卡片标题 {{ n }}</text>
          <text class="card-desc">这是一个响应式卡片的描述文本，会根据屏幕大小自动调整。</text>
        </view>
      </view>
    </view>

    <!-- 响应式按钮演示 -->
    <view class="demo-section">
      <text class="section-title">响应式按钮</text>
      <view class="buttons-demo">
        <view class="demo-button primary" :style="getButtonStyles()">
          <text>主要按钮</text>
        </view>
        <view class="demo-button secondary" :style="getButtonStyles()">
          <text>次要按钮</text>
        </view>
        <view class="demo-button outline" :style="getButtonStyles()">
          <text>边框按钮</text>
        </view>
      </view>
    </view>

    <!-- 断点信息 -->
    <view class="breakpoint-info">
      <text class="info-title">当前断点: {{ currentBreakpoint.toUpperCase() }}</text>
      <text class="info-desc">
        {{ isSmallScreen ? '小屏模式' : isLargeScreen ? '大屏模式' : '标准模式' }}
      </text>
      <view class="breakpoint-indicators">
        <view 
          class="indicator" 
          :class="{ active: isBreakpoint('xs') }"
        >XS</view>
        <view 
          class="indicator" 
          :class="{ active: isBreakpoint('sm') }"
        >SM</view>
        <view 
          class="indicator" 
          :class="{ active: isBreakpoint('md') }"
        >MD</view>
        <view 
          class="indicator" 
          :class="{ active: isBreakpoint('lg') }"
        >LG</view>
        <view 
          class="indicator" 
          :class="{ active: isBreakpoint('xl') }"
        >XL</view>
        <view 
          class="indicator" 
          :class="{ active: isBreakpoint('xxl') }"
        >XXL</view>
      </view>
    </view>
  </view>
</template>

<script>
import responsiveMixin from '../../mixins/responsiveMixin.js'

export default {
  name: 'ResponsiveDemo',
  mixins: [responsiveMixin],
  
  onLoad() {
    uni.setNavigationBarTitle({
      title: '响应式适配演示'
    })
  }
}
</script>

<style scoped>
.responsive-demo {
  min-height: 100vh;
  background: var(--bg-primary);
  padding: var(--space-base);
}

.device-info-card {
  background: var(--bg-card);
  border-radius: var(--radius-xl);
  padding: var(--space-lg);
  margin: 64px 0 var(--space-xl);
  box-shadow: var(--shadow-lg);
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--space-lg);
}

.card-title {
  font-size: var(--font-xl);
  font-weight: 700;
  color: var(--text-primary);
}

.breakpoint-badge {
  background: var(--primary-color);
  color: white;
  padding: var(--space-xs) var(--space-sm);
  border-radius: var(--radius-base);
  font-size: var(--font-sm);
  font-weight: 600;
}

.device-details {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: var(--space-base);
}

.detail-item {
  display: flex;
  flex-direction: column;
  gap: var(--space-xs);
}

.detail-label {
  font-size: var(--font-sm);
  color: var(--text-secondary);
  font-weight: 500;
}

.detail-value {
  font-size: var(--font-base);
  color: var(--text-primary);
  font-weight: 600;
}

.demo-section {
  margin-bottom: var(--space-2xl);
}

.section-title {
  font-size: var(--font-lg);
  font-weight: 700;
  color: var(--text-primary);
  margin-bottom: var(--space-lg);
  display: block;
}

.responsive-grid {
  display: grid;
  gap: var(--space-base);
}

.grid-item {
  background: var(--bg-card);
  border-radius: var(--radius-lg);
  padding: var(--space-lg);
  text-align: center;
  box-shadow: var(--shadow-base);
}

.item-text {
  font-size: var(--font-base);
  color: var(--text-primary);
  font-weight: 500;
}

.font-demo {
  display: flex;
  flex-direction: column;
  gap: var(--space-base);
}

.spacing-demo {
  display: flex;
  flex-direction: column;
  gap: var(--space-sm);
}

.spacing-item {
  background: var(--bg-card);
  border-radius: var(--radius-base);
  border: 2rpx solid var(--primary-color);
}

.spacing-item text {
  font-size: var(--font-sm);
  color: var(--text-primary);
}

.cards-demo {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200rpx, 1fr));
  gap: var(--space-lg);
}

.demo-card {
  background: var(--bg-card);
  border-radius: var(--radius-xl);
  text-align: center;
  box-shadow: var(--shadow-lg);
  transition: all 0.3s ease;
}

.demo-card:hover {
  transform: translateY(-4rpx);
  box-shadow: var(--shadow-xl);
}

.card-icon {
  background: var(--primary-color);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto var(--space-base);
  color: white;
  font-weight: 700;
}

.card-desc {
  font-size: var(--font-sm);
  color: var(--text-secondary);
  line-height: 1.5;
}

.buttons-demo {
  display: flex;
  flex-wrap: wrap;
  gap: var(--space-base);
}

.demo-button {
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: var(--radius-lg);
  font-weight: 600;
  transition: all 0.3s ease;
  cursor: pointer;
}

.demo-button.primary {
  background: var(--primary-color);
  color: white;
}

.demo-button.secondary {
  background: var(--gray-200);
  color: var(--text-primary);
}

.demo-button.outline {
  background: transparent;
  border: 2rpx solid var(--primary-color);
  color: var(--primary-color);
}

.breakpoint-info {
  background: var(--bg-card);
  border-radius: var(--radius-xl);
  padding: var(--space-lg);
  text-align: center;
  box-shadow: var(--shadow-lg);
}

.info-title {
  font-size: var(--font-xl);
  font-weight: 700;
  color: var(--text-primary);
  margin-bottom: var(--space-sm);
  display: block;
}

.info-desc {
  font-size: var(--font-base);
  color: var(--text-secondary);
  margin-bottom: var(--space-lg);
  display: block;
}

.breakpoint-indicators {
  display: flex;
  justify-content: center;
  gap: var(--space-sm);
}

.indicator {
  width: 60rpx;
  height: 60rpx;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  background: var(--gray-200);
  color: var(--text-secondary);
  font-size: var(--font-sm);
  font-weight: 600;
  transition: all 0.3s ease;
}

.indicator.active {
  background: var(--primary-color);
  color: white;
  transform: scale(1.1);
}

/* 响应式调整 */
.breakpoint-xs .device-details {
  grid-template-columns: 1fr;
}

.breakpoint-xs .cards-demo {
  grid-template-columns: 1fr;
}

.breakpoint-xs .buttons-demo {
  flex-direction: column;
}

.breakpoint-sm .device-details {
  grid-template-columns: 1fr;
}

.breakpoint-xl .device-details,
.breakpoint-xxl .device-details {
  grid-template-columns: repeat(4, 1fr);
}
</style>
