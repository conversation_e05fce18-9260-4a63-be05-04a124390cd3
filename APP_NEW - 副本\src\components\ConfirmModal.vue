<template>
  <view v-if="visible" class="modal-overlay" @tap="closeModal">
    <view class="modal-container" @tap.stop :class="animationClass">
      <view class="modal-header">
        <view class="modal-icon" :class="iconClass">
          <text class="icon-text">{{ iconText }}</text>
        </view>
        <text class="modal-title">{{ title }}</text>
      </view>
      
      <view class="modal-content">
        <text class="modal-message">{{ message }}</text>
      </view>
      
      <view class="modal-footer">
        <view class="modal-button cancel" @tap="onCancel">
          <text class="button-text">{{ cancelText }}</text>
        </view>
        <view class="modal-button confirm" @tap="onConfirm" :class="confirmClass">
          <text class="button-text">{{ confirmText }}</text>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
import { ref, watch } from 'vue'

export default {
  name: 'ConfirmModal',
  props: {
    modelValue: {
      type: Boolean,
      default: false
    },
    title: {
      type: String,
      default: '确认操作'
    },
    message: {
      type: String,
      default: '确定要执行此操作吗？'
    },
    iconText: {
      type: String,
      default: '⚠️'
    },
    iconClass: {
      type: String,
      default: 'warning'
    },
    cancelText: {
      type: String,
      default: '取消'
    },
    confirmText: {
      type: String,
      default: '确定'
    },
    confirmClass: {
      type: String,
      default: 'danger'
    }
  },
  emits: ['update:modelValue', 'confirm', 'cancel'],
  setup(props, { emit }) {
    const visible = ref(false)
    const animationClass = ref('')
    
    watch(() => props.modelValue, (newVal) => {
      if (newVal) {
        show()
      } else {
        hide()
      }
    }, { immediate: true })
    
    const show = () => {
      visible.value = true
      animationClass.value = 'modal-enter'
      // 触发震动反馈
      uni.vibrateShort()
    }
    
    const hide = () => {
      animationClass.value = 'modal-leave'
      setTimeout(() => {
        visible.value = false
        animationClass.value = ''
      }, 300)
    }
    
    const closeModal = () => {
      emit('update:modelValue', false)
    }
    
    const onConfirm = () => {
      emit('confirm')
      closeModal()
    }
    
    const onCancel = () => {
      emit('cancel')
      closeModal()
    }
    
    return {
      visible,
      animationClass,
      closeModal,
      onConfirm,
      onCancel
    }
  }
}
</script>

<style scoped>
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  z-index: 10001;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20px;
  animation: fadeIn 0.3s ease;
  box-sizing: border-box;
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

.modal-container {
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.98) 0%, rgba(248, 250, 252, 0.95) 100%);
  border-radius: 24px;
  max-width: 380px;
  width: 100%;
  overflow: hidden;
  box-shadow:
    0 25px 80px rgba(0, 0, 0, 0.2),
    0 12px 40px rgba(0, 0, 0, 0.15),
    inset 0 1px 0 rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.4);
  position: relative;
  margin: auto;
}

.modal-enter {
  animation: slideInScale 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}

.modal-leave {
  animation: slideOutScale 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

@keyframes slideInScale {
  from {
    opacity: 0;
    transform: translateY(30px) scale(0.9);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

@keyframes slideOutScale {
  from {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
  to {
    opacity: 0;
    transform: translateY(30px) scale(0.9);
  }
}

.modal-header {
  display: flex;
  align-items: center;
  padding: 28px 24px 20px;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.9) 0%, rgba(248, 250, 252, 0.7) 100%);
  border-bottom: 1px solid rgba(226, 232, 240, 0.3);
}

.modal-icon {
  width: 48px;
  height: 48px;
  border-radius: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 16px;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
}

.modal-icon.warning {
  background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
}

.modal-icon.info {
  background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
}

.modal-icon.success {
  background: linear-gradient(135deg, #10b981 0%, #059669 100%);
}

.modal-icon.error {
  background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
}

.icon-text {
  font-size: 24px;
  color: white;
}

.modal-title {
  flex: 1;
  font-size: 20px;
  font-weight: 700;
  color: #0f172a;
  line-height: 1.3;
}

.modal-content {
  padding: 24px;
}

.modal-message {
  font-size: 16px;
  color: #374151;
  line-height: 1.6;
  text-align: left;
}

.modal-footer {
  display: flex;
  gap: 12px;
  padding: 16px 24px 28px;
  background: linear-gradient(135deg, rgba(248, 250, 252, 0.8) 0%, rgba(255, 255, 255, 0.6) 100%);
}

.modal-button {
  flex: 1;
  padding: 16px 20px;
  border-radius: 14px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
  font-weight: 600;
  border: 1px solid transparent;
  position: relative;
  overflow: hidden;
}

.modal-button::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
  transition: left 0.5s;
}

.modal-button:active::before {
  left: 100%;
}

.modal-button.cancel {
  background: rgba(248, 250, 252, 0.9);
  border-color: rgba(226, 232, 240, 0.8);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.modal-button.cancel .button-text {
  color: #64748b;
}

.modal-button.confirm.danger {
  background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
  box-shadow: 0 4px 16px rgba(239, 68, 68, 0.3);
}

.modal-button.confirm.primary {
  background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
  box-shadow: 0 4px 16px rgba(59, 130, 246, 0.3);
}

.modal-button.confirm .button-text {
  color: white;
}

.modal-button:active {
  transform: scale(0.98);
}

.modal-button.confirm:active {
  box-shadow: 0 2px 8px rgba(239, 68, 68, 0.4);
}

.button-text {
  font-size: 16px;
  font-weight: 600;
  position: relative;
  z-index: 1;
}
</style>
