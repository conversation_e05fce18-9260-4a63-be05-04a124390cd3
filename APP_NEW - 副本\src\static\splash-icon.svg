<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 200 200" fill="none">
  <defs>
    <!-- 渐变定义 -->
    <radialGradient id="iconBg" cx="50%" cy="50%" r="50%">
      <stop offset="0%" style="stop-color:#ffffff;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#f0f9ff;stop-opacity:1" />
    </radialGradient>
    
    <radialGradient id="glowEffect" cx="50%" cy="50%" r="70%">
      <stop offset="0%" style="stop-color:#00b4d8;stop-opacity:0.4" />
      <stop offset="100%" style="stop-color:#00b4d8;stop-opacity:0" />
    </radialGradient>
    
    <linearGradient id="ringGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#00b4d8;stop-opacity:1" />
      <stop offset="50%" style="stop-color:#0096c7;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#667eea;stop-opacity:1" />
    </linearGradient>
    
    <!-- 滤镜效果 -->
    <filter id="dropShadow">
      <feDropShadow dx="0" dy="8" stdDeviation="12" flood-opacity="0.25"/>
    </filter>
    
    <filter id="innerShadow">
      <feOffset dx="0" dy="2"/>
      <feGaussianBlur stdDeviation="4" result="offset-blur"/>
      <feFlood flood-color="#000000" flood-opacity="0.1"/>
      <feComposite in2="offset-blur" operator="in"/>
      <feMerge> 
        <feMergeNode/>
        <feMergeNode in="SourceGraphic"/> 
      </feMerge>
    </filter>
  </defs>
  
  <!-- 外层发光效果 -->
  <circle cx="100" cy="100" r="90" fill="url(#glowEffect)" opacity="0.8">
    <animate attributeName="r" values="85;95;85" dur="3s" repeatCount="indefinite"/>
    <animate attributeName="opacity" values="0.6;0.8;0.6" dur="3s" repeatCount="indefinite"/>
  </circle>
  
  <!-- 主背景圆 -->
  <circle cx="100" cy="100" r="70" fill="url(#iconBg)" filter="url(#dropShadow)"/>
  
  <!-- 内层阴影 -->
  <circle cx="100" cy="100" r="70" fill="none" stroke="rgba(0,0,0,0.05)" stroke-width="1" filter="url(#innerShadow)"/>
  
  <!-- 辐射监测图标 -->
  <g transform="translate(100, 100)">
    <!-- 外圈 - 动画旋转 -->
    <circle cx="0" cy="0" r="45" fill="none" stroke="url(#ringGradient)" stroke-width="3" opacity="0.6" stroke-dasharray="10 5">
      <animateTransform attributeName="transform" type="rotate" values="0;360" dur="8s" repeatCount="indefinite"/>
      <animate attributeName="stroke-opacity" values="0.4;0.6;0.4" dur="2s" repeatCount="indefinite"/>
    </circle>
    
    <!-- 中圈 - 反向旋转 -->
    <circle cx="0" cy="0" r="30" fill="none" stroke="url(#ringGradient)" stroke-width="4" opacity="0.8" stroke-dasharray="8 4">
      <animateTransform attributeName="transform" type="rotate" values="360;0" dur="6s" repeatCount="indefinite"/>
      <animate attributeName="stroke-opacity" values="0.6;0.8;0.6" dur="2s" repeatCount="indefinite" begin="0.5s"/>
    </circle>
    
    <!-- 内圈 - 脉冲效果 -->
    <circle cx="0" cy="0" r="15" fill="url(#ringGradient)" opacity="0.9">
      <animate attributeName="r" values="12;18;12" dur="2s" repeatCount="indefinite"/>
      <animate attributeName="opacity" values="0.7;0.9;0.7" dur="2s" repeatCount="indefinite"/>
    </circle>
    
    <!-- 中心核心 -->
    <circle cx="0" cy="0" r="6" fill="#ffffff" opacity="1">
      <animate attributeName="opacity" values="0.8;1;0.8" dur="1.5s" repeatCount="indefinite"/>
    </circle>
    
    <!-- 辐射线条 -->
    <g stroke="url(#ringGradient)" stroke-width="2" stroke-linecap="round" opacity="0.7">
      <!-- 垂直线 -->
      <line x1="0" y1="-25" x2="0" y2="-35">
        <animate attributeName="opacity" values="0.5;0.7;0.5" dur="1s" repeatCount="indefinite"/>
      </line>
      <line x1="0" y1="25" x2="0" y2="35">
        <animate attributeName="opacity" values="0.5;0.7;0.5" dur="1s" repeatCount="indefinite" begin="0.25s"/>
      </line>
      
      <!-- 水平线 -->
      <line x1="-25" y1="0" x2="-35" y2="0">
        <animate attributeName="opacity" values="0.5;0.7;0.5" dur="1s" repeatCount="indefinite" begin="0.5s"/>
      </line>
      <line x1="25" y1="0" x2="35" y2="0">
        <animate attributeName="opacity" values="0.5;0.7;0.5" dur="1s" repeatCount="indefinite" begin="0.75s"/>
      </line>
      
      <!-- 对角线 -->
      <line x1="-18" y1="-18" x2="-25" y2="-25">
        <animate attributeName="opacity" values="0.3;0.7;0.3" dur="1.2s" repeatCount="indefinite"/>
      </line>
      <line x1="18" y1="18" x2="25" y2="25">
        <animate attributeName="opacity" values="0.3;0.7;0.3" dur="1.2s" repeatCount="indefinite" begin="0.3s"/>
      </line>
      <line x1="18" y1="-18" x2="25" y2="-25">
        <animate attributeName="opacity" values="0.3;0.7;0.3" dur="1.2s" repeatCount="indefinite" begin="0.6s"/>
      </line>
      <line x1="-18" y1="18" x2="-25" y2="25">
        <animate attributeName="opacity" values="0.3;0.7;0.3" dur="1.2s" repeatCount="indefinite" begin="0.9s"/>
      </line>
    </g>
  </g>
  
  <!-- 装饰性粒子 -->
  <g opacity="0.6">
    <circle cx="40" cy="60" r="2" fill="#00b4d8">
      <animate attributeName="opacity" values="0.3;0.6;0.3" dur="2s" repeatCount="indefinite"/>
    </circle>
    <circle cx="160" cy="80" r="1.5" fill="#0096c7">
      <animate attributeName="opacity" values="0.2;0.5;0.2" dur="3s" repeatCount="indefinite"/>
    </circle>
    <circle cx="50" cy="150" r="1" fill="#667eea">
      <animate attributeName="opacity" values="0.1;0.4;0.1" dur="4s" repeatCount="indefinite"/>
    </circle>
    <circle cx="150" cy="140" r="2.5" fill="#00b4d8">
      <animate attributeName="opacity" values="0.4;0.6;0.4" dur="2.5s" repeatCount="indefinite"/>
    </circle>
  </g>
  
  <!-- 外边框高光 -->
  <circle cx="100" cy="100" r="70" fill="none" stroke="rgba(255,255,255,0.3)" stroke-width="1" opacity="0.8"/>
</svg>
