<template>
  <view class="bottom-navigation">
    <view class="nav-background"></view>
    <view class="nav-content">
      <view 
        class="nav-tab" 
        :class="{ active: currentPage === 'dashboard' }"
        @tap="navigateTo('dashboard')"
      >
        <view class="tab-icon-container">
          <svg class="tab-icon" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2.5" stroke-linecap="round" stroke-linejoin="round">
            <rect x="3" y="3" width="18" height="18" rx="2" ry="2"></rect>
            <line x1="9" y1="9" x2="9" y2="15"></line>
            <line x1="15" y1="9" x2="15" y2="15"></line>
          </svg>
          <view class="tab-glow" v-if="currentPage === 'dashboard'"></view>
        </view>
        <text class="tab-label">仪表盘</text>
      </view>

      <view 
        class="nav-tab" 
        :class="{ active: currentPage === 'charts' }"
        @tap="navigateTo('charts')"
      >
        <view class="tab-icon-container">
          <svg class="tab-icon" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2.5" stroke-linecap="round" stroke-linejoin="round">
            <path d="M22 12h-4l-3 9L9 3l-3 9H2"></path>
          </svg>
          <view class="tab-glow" v-if="currentPage === 'charts'"></view>
        </view>
        <text class="tab-label">数据</text>
      </view>

      <view
        class="nav-tab"
        :class="{ active: currentPage === 'health' }"
        @tap="navigateTo('health')"
      >
        <view class="tab-icon-container">
          <svg class="tab-icon" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" :fill="currentPage === 'health' ? 'currentColor' : 'none'" stroke="currentColor" stroke-width="2.5" stroke-linecap="round" stroke-linejoin="round">
            <path d="M19 14c1.49-1.46 3-3.21 3-5.5A5.5 5.5 0 0 0 16.5 3c-1.76 0-3 .5-4.5 2-1.5-1.5-2.74-2-4.5-2A5.5 5.5 0 0 0 2 8.5c0 2.29 1.51 4.04 3 5.5l7 7z"></path>
          </svg>
          <view class="tab-glow" v-if="currentPage === 'health'"></view>
        </view>
        <text class="tab-label">健康</text>
      </view>

      <view 
        class="nav-tab" 
        :class="{ active: currentPage === 'map' }"
        @tap="navigateTo('map')"
      >
        <view class="tab-icon-container">
          <svg class="tab-icon" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2.5" stroke-linecap="round" stroke-linejoin="round">
            <path d="M21 10c0 7-9 13-9 13s-9-6-9-13a9 9 0 0 1 18 0z"></path>
            <circle cx="12" cy="10" r="3"></circle>
          </svg>
          <view class="tab-glow" v-if="currentPage === 'map'"></view>
        </view>
        <text class="tab-label">地图</text>
      </view>

      <view 
        class="nav-tab" 
        :class="{ active: currentPage === 'settings' }"
        @tap="navigateTo('settings')"
      >
        <view class="tab-icon-container">
          <svg class="tab-icon" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2.5" stroke-linecap="round" stroke-linejoin="round">
            <circle cx="12" cy="12" r="3"></circle>
            <path d="M12 1v6m0 6v6m11-7h-6m-6 0H1m15.5-6.5l-4.24 4.24M7.76 7.76L3.52 3.52m12.96 12.96l-4.24-4.24M7.76 16.24l-4.24 4.24"></path>
          </svg>
          <view class="tab-glow" v-if="currentPage === 'settings'"></view>
        </view>
        <text class="tab-label">设置</text>
      </view>
    </view>
  </view>
</template>

<script setup>
import { ref } from 'vue'

const props = defineProps({
  currentPage: {
    type: String,
    required: true
  }
})

// 统一的导航函数
const navigateTo = (page) => {
  const routes = {
    'dashboard': '/pages/dashboard/dashboard',
    'charts': '/pages/charts/charts',
    'health': '/pages/health/health',
    'map': '/pages/map/map',
    'settings': '/pages/settings/settings'
  }

  if (routes[page] && page !== props.currentPage) {
    uni.navigateTo({
      url: routes[page],
      fail: (err) => {
        console.error('导航失败:', err)
        // 如果navigateTo失败，尝试使用redirectTo
        uni.redirectTo({
          url: routes[page],
          fail: (redirectErr) => {
            console.error('重定向失败:', redirectErr)
            uni.showToast({
              title: '页面跳转失败',
              icon: 'none'
            })
          }
        })
      }
    })
  }
}
</script>

<style scoped>
.bottom-navigation {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  z-index: 9999; /* 提高z-index确保始终在最顶层 */
  padding: 0 16px 12px; /* 减少底部内边距 */
}

.nav-background {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 100px; /* 减少背景高度 */
  background: linear-gradient(180deg,
    rgba(255, 255, 255, 0) 0%,
    rgba(255, 255, 255, 0.95) 40%,
    rgba(255, 255, 255, 1) 100%);
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
}

.nav-content {
  position: relative;
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: rgba(255, 255, 255, 0.95);
  border-radius: 24px; /* 减少圆角 */
  padding: 8px 6px; /* 减少内边距 */
  margin: 0 4px;
  box-shadow:
    0 8px 32px rgba(0, 0, 0, 0.12),
    0 4px 16px rgba(0, 0, 0, 0.08),
    inset 0 1px 0 rgba(255, 255, 255, 0.8);
  border: 1px solid rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
}

.nav-tab {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 8px 12px; /* 减少内边距 */
  border-radius: 16px; /* 减少圆角 */
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  cursor: pointer;
  position: relative;
  min-width: 48px; /* 减少最小宽度 */
  overflow: hidden;
}

.nav-tab::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, #2196F3 0%, #1976D2 100%); /* 默认蓝色渐变 */
  opacity: 0;
  border-radius: 16px; /* 匹配nav-tab的圆角 */
  transition: opacity 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}

/* 默认激活状态背景 */
.nav-tab.active::before {
  opacity: 1;
  background: linear-gradient(135deg, #2196F3 0%, #1976D2 100%); /* 默认蓝色渐变 */
}

/* 健康页面特殊的红色背景 */
.nav-tab:nth-child(3).active::before {
  background: linear-gradient(135deg, #F44336 0%, #E53935 100%); /* 红色渐变 */
}

.nav-tab:active {
  transform: scale(0.95);
}

.tab-icon-container {
  position: relative;
  width: 28px; /* 减少图标容器尺寸 */
  height: 28px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 4px; /* 减少底部间距 */
  z-index: 1;
}

.tab-icon {
  width: 20px; /* 减少图标尺寸 */
  height: 20px;
  color: #6b7280;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  transform-origin: center;
}

.nav-tab.active .tab-icon {
  color: #ffffff;
  transform: scale(1.1);
  animation: iconBounce 0.6s cubic-bezier(0.68, -0.55, 0.265, 1.55);
}

.tab-glow {
  position: absolute;
  top: 50%;
  left: 50%;
  width: 40px;
  height: 40px;
  background: radial-gradient(circle, rgba(255, 255, 255, 0.3) 0%, transparent 70%);
  border-radius: 50%;
  transform: translate(-50%, -50%) scale(0);
  animation: glowPulse 2s ease-in-out infinite;
}

@keyframes glowPulse {
  0%, 100% {
    transform: translate(-50%, -50%) scale(0);
    opacity: 0;
  }
  50% {
    transform: translate(-50%, -50%) scale(1);
    opacity: 1;
  }
}

.tab-label {
  font-size: 10px; /* 减少字体大小 */
  font-weight: 500;
  color: #6b7280;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  text-align: center;
  line-height: 1.2;
  z-index: 1;
  position: relative;
}

.nav-tab.active .tab-label {
  color: #ffffff;
  font-weight: 600;
  transform: translateY(-1px);
  animation: labelSlide 0.6s cubic-bezier(0.68, -0.55, 0.265, 1.55);
}

/* 悬停效果 */
.nav-tab:hover:not(.active) {
  background: rgba(33, 150, 243, 0.08); /* 默认蓝色 */
  transform: translateY(-2px);
}

.nav-tab:hover:not(.active) .tab-icon {
  color: #2196F3; /* 默认蓝色 */
  transform: scale(1.05);
}

.nav-tab:hover:not(.active) .tab-label {
  color: #2196F3; /* 默认蓝色 */
}

/* 健康页面特殊的红色悬停效果 */
.nav-tab:nth-child(3):hover:not(.active) {
  background: rgba(244, 67, 54, 0.08); /* 红色背景 */
}

.nav-tab:nth-child(3):hover:not(.active) .tab-icon {
  color: #F44336; /* 红色图标 */
}

.nav-tab:nth-child(3):hover:not(.active) .tab-label {
  color: #F44336; /* 红色标签 */
}

/* 涟漪效果 */
.nav-tab::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 0;
  height: 0;
  background: rgba(255, 255, 255, 0.3);
  border-radius: 50%;
  transform: translate(-50%, -50%);
  transition: width 0.3s ease, height 0.3s ease;
}



/* 响应式设计 */
@media (max-width: 375px) {
  .nav-content {
    padding: 10px 4px;
    margin: 0 2px;
  }
  
  .nav-tab {
    padding: 10px 12px;
    min-width: 48px;
  }
  
  .tab-icon {
    width: 20px;
    height: 20px;
  }
  
  .tab-label {
    font-size: 10px;
  }
}

/* 加载动画 */
.nav-content {
  animation: slideUp 0.8s cubic-bezier(0.4, 0, 0.2, 1);
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(100px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 图标弹性动画 */
@keyframes iconBounce {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.3);
  }
  100% {
    transform: scale(1.1);
  }
}

/* 标签滑动动画 */
@keyframes labelSlide {
  0% {
    transform: translateY(0);
    opacity: 0.7;
  }
  50% {
    transform: translateY(-3px);
    opacity: 0.9;
  }
  100% {
    transform: translateY(-1px);
    opacity: 1;
  }
}

/* 增强的涟漪效果 */
.nav-tab:active::after {
  width: 50px; /* 减少涟漪大小以适应新的尺寸 */
  height: 50px;
  background: rgba(33, 150, 243, 0.3); /* 默认蓝色涟漪 */
}

/* 健康页面特殊的红色涟漪效果 */
.nav-tab:nth-child(3):active::after {
  background: rgba(244, 67, 54, 0.3); /* 红色涟漪 */
}
</style> 