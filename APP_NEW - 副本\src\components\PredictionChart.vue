<template>
  <view class="prediction-chart-container">
    <!-- 图表标题和控制 -->
    <view class="chart-header">
      <view class="title-section">
        <text class="chart-title">{{ title }}</text>
        <text class="chart-subtitle">{{ subtitle }}</text>
      </view>
      <view class="chart-controls">
        <view class="prediction-indicator" :class="{ active: showPrediction }">
          <view class="indicator-dot"></view>
          <text class="indicator-text">AI预测</text>
        </view>
        <view class="confidence-badge" v-if="confidence">
          <text class="confidence-text">置信度 {{ (confidence * 100).toFixed(0) }}%</text>
        </view>
      </view>
    </view>

    <!-- 图表画布容器 -->
    <view class="chart-canvas-container">
      <canvas 
        :canvas-id="canvasId" 
        class="chart-canvas"
        :style="{ width: chartWidth + 'px', height: chartHeight + 'px' }"
      ></canvas>
      
      <!-- 误差带图例 -->
      <view class="error-band-legend" v-if="showErrorBand">
        <view class="legend-item">
          <view class="legend-color prediction"></view>
          <text class="legend-text">预测值</text>
        </view>
        <view class="legend-item">
          <view class="legend-color error-band"></view>
          <text class="legend-text">±5% 误差带</text>
        </view>
        <view class="legend-item">
          <view class="legend-color historical"></view>
          <text class="legend-text">历史数据</text>
        </view>
      </view>
      
      <!-- 加载状态 -->
      <view class="chart-loading" v-if="isLoading">
        <view class="loading-spinner"></view>
        <text class="loading-text">AI分析中...</text>
      </view>
    </view>

    <!-- 预测洞察 -->
    <view class="prediction-insights" v-if="insights">
      <view class="insights-header">
        <text class="insights-icon">🔮</text>
        <text class="insights-title">AI预测洞察</text>
      </view>
      <view class="insights-content">
        <text class="insights-text">{{ insights }}</text>
      </view>
    </view>
  </view>
</template>

<script>
import { ref, onMounted, watch, nextTick, computed } from 'vue'
import { createPredictionModel } from '@/utils/predictionAlgorithm.js'

export default {
  name: 'PredictionChart',
  props: {
    title: {
      type: String,
      default: '辐射预测分析'
    },
    subtitle: {
      type: String,
      default: '基于AI机器学习的智能预测'
    },
    canvasId: {
      type: String,
      required: true
    },
    chartWidth: {
      type: Number,
      default: 350
    },
    chartHeight: {
      type: Number,
      default: 280
    },
    historicalData: {
      type: Array,
      default: () => []
    },
    showErrorBand: {
      type: Boolean,
      default: true
    },
    showPrediction: {
      type: Boolean,
      default: true
    },
    predictionHours: {
      type: Number,
      default: 168 // 7天
    }
  },
  emits: ['prediction-ready', 'error'],
  setup(props, { emit }) {
    const isLoading = ref(false)
    const chartContext = ref(null)
    const predictionModel = ref(null)
    const predictionData = ref(null)
    const confidence = ref(0)
    const insights = ref('')

    // 计算属性
    const canvasStyle = computed(() => ({
      width: props.chartWidth + 'px',
      height: props.chartHeight + 'px'
    }))

    // 初始化图表
    const initChart = async () => {
      await nextTick()
      try {
        chartContext.value = uni.createCanvasContext(props.canvasId)
        if (chartContext.value) {
          console.log('预测图表初始化成功')
          await generatePrediction()
        }
      } catch (error) {
        console.error('图表初始化失败:', error)
        emit('error', error)
      }
    }

    // 生成预测数据（优化版 - 支持实时更新）
    const generatePrediction = async () => {
      if (!props.historicalData || props.historicalData.length < 5) {
        console.warn('历史数据不足，无法生成预测')
        return
      }

      isLoading.value = true

      try {
        // 创建预测模型
        if (!predictionModel.value) {
          predictionModel.value = createPredictionModel()
          // 初始化模型
          predictionModel.value.initializeModel(props.historicalData)
        } else {
          // 实时更新模型
          const latestData = props.historicalData.slice(0, 5) // 最新5个数据点
          const result = predictionModel.value.updateModel(latestData)
          if (result) {
            predictionData.value = result
            confidence.value = result.confidence
            generateInsights(result)
            await drawChart()
            emit('prediction-ready', result)
            isLoading.value = false
            return
          }
        }

        // 生成预测（减少预测时长到3.5天）
        const result = predictionModel.value.generatePrediction(84) // 84个2小时间隔 = 7天
        predictionData.value = result
        confidence.value = result.confidence

        // 生成洞察文本
        generateInsights(result)

        // 绘制图表
        await drawChart()

        emit('prediction-ready', result)

      } catch (error) {
        console.error('预测生成失败:', error)
        emit('error', error)
      } finally {
        isLoading.value = false
      }
    }

    // 实时更新预测
    const realtimeUpdate = async () => {
      if (!predictionModel.value || !props.historicalData.length) return

      try {
        const latestPoint = props.historicalData[0]
        const result = predictionModel.value.realtimeUpdate(latestPoint)

        if (result) {
          predictionData.value = result
          confidence.value = result.confidence
          generateInsights(result)
          await drawChart()
          emit('prediction-ready', result)
        }
      } catch (error) {
        console.error('实时更新失败:', error)
      }
    }

    // 生成预测洞察
    const generateInsights = (result) => {
      const modelInfo = predictionModel.value.getModelInfo()
      const trend = modelInfo.trendSlope > 0.001 ? '上升' : 
                   modelInfo.trendSlope < -0.001 ? '下降' : '稳定'
      
      const volatilityLevel = modelInfo.volatility < 0.05 ? '低' :
                             modelInfo.volatility < 0.15 ? '中等' : '高'
      
      insights.value = `预测显示未来7天辐射水平呈${trend}趋势，数据波动性为${volatilityLevel}。` +
                      `模型置信度为${(result.confidence * 100).toFixed(0)}%，基于${modelInfo.dataPoints}个历史数据点分析。`
    }

    // 绘制图表
    const drawChart = async () => {
      if (!chartContext.value || !predictionData.value) return

      const ctx = chartContext.value
      const width = props.chartWidth
      const height = props.chartHeight
      const padding = 40

      // 清空画布
      ctx.clearRect(0, 0, width, height)

      // 设置画布背景
      const bgGradient = ctx.createLinearGradient(0, 0, 0, height)
      bgGradient.addColorStop(0, '#fefefe')
      bgGradient.addColorStop(1, '#f8fafc')
      ctx.setFillStyle(bgGradient)
      ctx.fillRect(0, 0, width, height)

      // 合并历史数据和预测数据
      const allData = [...props.historicalData, ...predictionData.value.prediction]
      const allValues = allData.map(item => item.doseRate)
      const maxValue = Math.max(...allValues)
      const minValue = Math.min(...allValues)
      const range = maxValue - minValue || 1

      // 绘制网格
      drawGrid(ctx, width, height, padding)

      // 绘制误差带
      if (props.showErrorBand) {
        drawErrorBand(ctx, width, height, padding, minValue, range)
      }

      // 绘制历史数据
      drawHistoricalData(ctx, width, height, padding, minValue, range)

      // 绘制预测数据
      if (props.showPrediction) {
        drawPredictionData(ctx, width, height, padding, minValue, range)
      }

      // 绘制分界线
      drawDivisionLine(ctx, width, height, padding)

      ctx.draw()
    }

    // 绘制网格
    const drawGrid = (ctx, width, height, padding) => {
      ctx.setStrokeStyle('rgba(148, 163, 184, 0.2)')
      ctx.setLineWidth(1)

      // 水平网格线
      for (let i = 0; i <= 5; i++) {
        const y = padding + (i / 5) * (height - padding * 2)
        ctx.beginPath()
        ctx.moveTo(padding, y)
        ctx.lineTo(width - padding, y)
        ctx.stroke()
      }

      // 垂直网格线
      for (let i = 0; i <= 6; i++) {
        const x = padding + (i / 6) * (width - padding * 2)
        ctx.beginPath()
        ctx.moveTo(x, padding)
        ctx.lineTo(x, height - padding)
        ctx.stroke()
      }
    }

    // 绘制误差带
    const drawErrorBand = (ctx, width, height, padding, minValue, range) => {
      if (!predictionData.value) return

      const chartWidth = width - padding * 2
      const chartHeight = height - padding * 2
      const historicalLength = props.historicalData.length
      const totalLength = historicalLength + predictionData.value.prediction.length

      // 创建误差带路径
      ctx.beginPath()
      
      // 上边界
      predictionData.value.upperBound.forEach((point, index) => {
        const x = padding + ((historicalLength + index) / (totalLength - 1)) * chartWidth
        const y = height - padding - ((point.doseRate - minValue) / range) * chartHeight
        
        if (index === 0) {
          ctx.moveTo(x, y)
        } else {
          ctx.lineTo(x, y)
        }
      })
      
      // 下边界（反向）
      for (let i = predictionData.value.lowerBound.length - 1; i >= 0; i--) {
        const point = predictionData.value.lowerBound[i]
        const x = padding + ((historicalLength + i) / (totalLength - 1)) * chartWidth
        const y = height - padding - ((point.doseRate - minValue) / range) * chartHeight
        ctx.lineTo(x, y)
      }
      
      ctx.closePath()
      ctx.setFillStyle('rgba(59, 130, 246, 0.15)')
      ctx.fill()
    }

    // 绘制历史数据
    const drawHistoricalData = (ctx, width, height, padding, minValue, range) => {
      if (!props.historicalData.length) return

      const chartWidth = width - padding * 2
      const chartHeight = height - padding * 2
      const totalLength = props.historicalData.length + predictionData.value.prediction.length

      // 绘制历史数据填充
      const gradient = ctx.createLinearGradient(0, padding, 0, height - padding)
      gradient.addColorStop(0, 'rgba(34, 197, 94, 0.3)')
      gradient.addColorStop(1, 'rgba(34, 197, 94, 0.05)')

      ctx.beginPath()
      ctx.moveTo(padding, height - padding)

      props.historicalData.forEach((point, index) => {
        const x = padding + (index / (totalLength - 1)) * chartWidth
        const y = height - padding - ((point.doseRate - minValue) / range) * chartHeight
        ctx.lineTo(x, y)
      })

      ctx.lineTo(padding + ((props.historicalData.length - 1) / (totalLength - 1)) * chartWidth, height - padding)
      ctx.closePath()
      ctx.setFillStyle(gradient)
      ctx.fill()

      // 绘制历史数据线条
      ctx.beginPath()
      ctx.setStrokeStyle('#22c55e')
      ctx.setLineWidth(3)
      ctx.setShadow(0, 2, 6, 'rgba(34, 197, 94, 0.3)')

      props.historicalData.forEach((point, index) => {
        const x = padding + (index / (totalLength - 1)) * chartWidth
        const y = height - padding - ((point.doseRate - minValue) / range) * chartHeight

        if (index === 0) {
          ctx.moveTo(x, y)
        } else {
          ctx.lineTo(x, y)
        }
      })

      ctx.stroke()
      ctx.setShadow(0, 0, 0, 'transparent')
    }

    // 绘制预测数据
    const drawPredictionData = (ctx, width, height, padding, minValue, range) => {
      if (!predictionData.value) return

      const chartWidth = width - padding * 2
      const chartHeight = height - padding * 2
      const historicalLength = props.historicalData.length
      const totalLength = historicalLength + predictionData.value.prediction.length

      // 绘制预测数据线条
      ctx.beginPath()
      ctx.setStrokeStyle('#3b82f6')
      ctx.setLineWidth(3)
      ctx.setLineDash([8, 4]) // 虚线样式
      ctx.setShadow(0, 2, 6, 'rgba(59, 130, 246, 0.3)')

      predictionData.value.prediction.forEach((point, index) => {
        const x = padding + ((historicalLength + index) / (totalLength - 1)) * chartWidth
        const y = height - padding - ((point.doseRate - minValue) / range) * chartHeight

        if (index === 0) {
          ctx.moveTo(x, y)
        } else {
          ctx.lineTo(x, y)
        }
      })

      ctx.stroke()
      ctx.setLineDash([]) // 重置线条样式
      ctx.setShadow(0, 0, 0, 'transparent')

      // 绘制预测点
      predictionData.value.prediction.forEach((point, index) => {
        if (index % 6 === 0) { // 每6个点绘制一个标记点
          const x = padding + ((historicalLength + index) / (totalLength - 1)) * chartWidth
          const y = height - padding - ((point.doseRate - minValue) / range) * chartHeight

          ctx.beginPath()
          ctx.arc(x, y, 4, 0, 2 * Math.PI)
          ctx.setFillStyle('#3b82f6')
          ctx.fill()

          ctx.beginPath()
          ctx.arc(x, y, 4, 0, 2 * Math.PI)
          ctx.setStrokeStyle('#ffffff')
          ctx.setLineWidth(2)
          ctx.stroke()
        }
      })
    }

    // 绘制分界线
    const drawDivisionLine = (ctx, width, height, padding) => {
      const chartWidth = width - padding * 2
      const historicalLength = props.historicalData.length
      const totalLength = historicalLength + (predictionData.value?.prediction.length || 0)

      if (totalLength > historicalLength) {
        const x = padding + ((historicalLength - 1) / (totalLength - 1)) * chartWidth

        ctx.beginPath()
        ctx.moveTo(x, padding)
        ctx.lineTo(x, height - padding)
        ctx.setStrokeStyle('rgba(239, 68, 68, 0.6)')
        ctx.setLineWidth(2)
        ctx.setLineDash([6, 6])
        ctx.stroke()
        ctx.setLineDash([])

        // 添加标签
        ctx.setFillStyle('#ef4444')
        ctx.setFontSize(12)
        ctx.fillText('预测开始', x + 5, padding + 20)
      }
    }

    return {
      isLoading,
      confidence,
      insights,
      canvasStyle,
      initChart,
      generatePrediction,
      realtimeUpdate
    }
  },
  mounted() {
    this.initChart()

    // 设置实时更新定时器（每30秒更新一次）
    this.updateTimer = setInterval(() => {
      this.realtimeUpdate()
    }, 30000)
  },
  unmounted() {
    // 清理定时器
    if (this.updateTimer) {
      clearInterval(this.updateTimer)
    }
  },
  watch: {
    historicalData: {
      handler(newData, oldData) {
        // 如果数据长度发生变化，说明有新数据
        if (newData.length !== oldData?.length) {
          this.realtimeUpdate()
        } else {
          this.generatePrediction()
        }
      },
      deep: true
    }
  }
}
</script>

<style scoped>
.prediction-chart-container {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  border-radius: 24px;
  padding: 24px;
  border: 1px solid rgba(226, 232, 240, 0.8);
  box-shadow: 0 8px 32px rgba(15, 23, 42, 0.08);
  margin-bottom: 20px;
  position: relative;
  overflow: hidden;
}

.chart-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 20px;
}

.title-section {
  flex: 1;
}

.chart-title {
  font-size: 20px;
  font-weight: 700;
  color: #0f172a;
  display: block;
  margin-bottom: 4px;
}

.chart-subtitle {
  font-size: 14px;
  color: #64748b;
  display: block;
}

.chart-controls {
  display: flex;
  align-items: center;
  gap: 12px;
}

.prediction-indicator {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 6px 12px;
  background: rgba(59, 130, 246, 0.1);
  border-radius: 20px;
  border: 1px solid rgba(59, 130, 246, 0.2);
  transition: all 0.3s ease;
}

.prediction-indicator.active {
  background: rgba(59, 130, 246, 0.15);
  border-color: rgba(59, 130, 246, 0.3);
}

.indicator-dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background: #3b82f6;
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
    transform: scale(1);
  }
  50% {
    opacity: 0.7;
    transform: scale(1.1);
  }
}

.indicator-text {
  font-size: 12px;
  color: #3b82f6;
  font-weight: 600;
}

.confidence-badge {
  padding: 4px 10px;
  background: linear-gradient(135deg, #10b981 0%, #059669 100%);
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(16, 185, 129, 0.2);
}

.confidence-text {
  font-size: 11px;
  color: white;
  font-weight: 600;
}

.chart-canvas-container {
  position: relative;
  border-radius: 16px;
  overflow: hidden;
  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
  border: 1px solid rgba(226, 232, 240, 0.6);
}

.chart-canvas {
  display: block;
  width: 100%;
  height: 100%;
}

.error-band-legend {
  position: absolute;
  top: 12px;
  right: 12px;
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(10px);
  border-radius: 12px;
  padding: 8px 12px;
  border: 1px solid rgba(226, 232, 240, 0.6);
  display: flex;
  flex-direction: column;
  gap: 6px;
}

.legend-item {
  display: flex;
  align-items: center;
  gap: 6px;
}

.legend-color {
  width: 12px;
  height: 3px;
  border-radius: 2px;
}

.legend-color.prediction {
  background: #3b82f6;
}

.legend-color.error-band {
  background: rgba(59, 130, 246, 0.3);
}

.legend-color.historical {
  background: #22c55e;
}

.legend-text {
  font-size: 11px;
  color: #475569;
  font-weight: 500;
}

.chart-loading {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 12px;
  background: rgba(255, 255, 255, 0.95);
  padding: 20px;
  border-radius: 16px;
  border: 1px solid rgba(226, 232, 240, 0.8);
  box-shadow: 0 4px 16px rgba(15, 23, 42, 0.1);
}

.loading-spinner {
  width: 24px;
  height: 24px;
  border: 3px solid rgba(59, 130, 246, 0.2);
  border-top: 3px solid #3b82f6;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-text {
  font-size: 14px;
  color: #64748b;
  font-weight: 500;
}

.prediction-insights {
  margin-top: 16px;
  padding: 16px;
  background: linear-gradient(135deg, rgba(59, 130, 246, 0.05) 0%, rgba(16, 185, 129, 0.05) 100%);
  border-radius: 16px;
  border: 1px solid rgba(59, 130, 246, 0.1);
}

.insights-header {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 8px;
}

.insights-icon {
  font-size: 16px;
}

.insights-title {
  font-size: 14px;
  font-weight: 600;
  color: #3b82f6;
}

.insights-content {
  padding-left: 24px;
}

.insights-text {
  font-size: 13px;
  color: #475569;
  line-height: 1.5;
}

/* 响应式设计 */
@media screen and (max-width: 480px) {
  .prediction-chart-container {
    padding: 16px;
    margin-bottom: 16px;
  }

  .chart-header {
    flex-direction: column;
    gap: 12px;
    align-items: flex-start;
  }

  .chart-controls {
    align-self: stretch;
    justify-content: space-between;
  }

  .error-band-legend {
    position: static;
    margin-top: 12px;
    flex-direction: row;
    flex-wrap: wrap;
  }

  .prediction-insights {
    margin-top: 12px;
    padding: 12px;
  }
}
</style>
