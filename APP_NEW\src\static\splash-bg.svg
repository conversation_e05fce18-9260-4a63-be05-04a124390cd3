<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 375 812" fill="none">
  <defs>
    <!-- 渐变定义 -->
    <linearGradient id="bgGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#667eea;stop-opacity:1" />
      <stop offset="50%" style="stop-color:#764ba2;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#00b4d8;stop-opacity:1" />
    </linearGradient>
    
    <radialGradient id="iconGradient" cx="50%" cy="50%" r="50%">
      <stop offset="0%" style="stop-color:#ffffff;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#f0f9ff;stop-opacity:1" />
    </radialGradient>
    
    <radialGradient id="glowGradient" cx="50%" cy="50%" r="70%">
      <stop offset="0%" style="stop-color:#00b4d8;stop-opacity:0.3" />
      <stop offset="100%" style="stop-color:#00b4d8;stop-opacity:0" />
    </radialGradient>
    
    <!-- 滤镜效果 -->
    <filter id="glow">
      <feGaussianBlur stdDeviation="3" result="coloredBlur"/>
      <feMerge> 
        <feMergeNode in="coloredBlur"/>
        <feMergeNode in="SourceGraphic"/>
      </feMerge>
    </filter>
    
    <filter id="shadow">
      <feDropShadow dx="0" dy="4" stdDeviation="8" flood-opacity="0.3"/>
    </filter>
  </defs>
  
  <!-- 背景 -->
  <rect width="375" height="812" fill="url(#bgGradient)"/>
  
  <!-- 装饰性粒子 -->
  <circle cx="50" cy="150" r="2" fill="rgba(255,255,255,0.6)" opacity="0.8">
    <animate attributeName="opacity" values="0.3;0.8;0.3" dur="3s" repeatCount="indefinite"/>
  </circle>
  <circle cx="320" cy="200" r="1.5" fill="rgba(255,255,255,0.5)" opacity="0.6">
    <animate attributeName="opacity" values="0.2;0.6;0.2" dur="4s" repeatCount="indefinite"/>
  </circle>
  <circle cx="80" cy="300" r="1" fill="rgba(255,255,255,0.4)" opacity="0.5">
    <animate attributeName="opacity" values="0.1;0.5;0.1" dur="5s" repeatCount="indefinite"/>
  </circle>
  <circle cx="300" cy="350" r="2.5" fill="rgba(255,255,255,0.7)" opacity="0.7">
    <animate attributeName="opacity" values="0.4;0.7;0.4" dur="2.5s" repeatCount="indefinite"/>
  </circle>
  
  <!-- 主要图标区域 -->
  <g transform="translate(187.5, 300)">
    <!-- 发光效果 -->
    <circle cx="0" cy="0" r="60" fill="url(#glowGradient)" filter="url(#glow)">
      <animate attributeName="r" values="55;65;55" dur="2s" repeatCount="indefinite"/>
    </circle>
    
    <!-- 图标背景 -->
    <circle cx="0" cy="0" r="40" fill="url(#iconGradient)" filter="url(#shadow)"/>
    
    <!-- 辐射监测图标 -->
    <g stroke="#00b4d8" stroke-width="2.5" fill="none" stroke-linecap="round" stroke-linejoin="round">
      <!-- 外圈 -->
      <circle cx="0" cy="0" r="25" opacity="0.8">
        <animate attributeName="stroke-opacity" values="0.5;0.8;0.5" dur="2s" repeatCount="indefinite"/>
      </circle>
      <!-- 中圈 -->
      <circle cx="0" cy="0" r="15" opacity="0.9">
        <animate attributeName="stroke-opacity" values="0.6;0.9;0.6" dur="2s" repeatCount="indefinite" begin="0.3s"/>
      </circle>
      <!-- 内圈 -->
      <circle cx="0" cy="0" r="5" opacity="1" fill="#00b4d8">
        <animate attributeName="fill-opacity" values="0.7;1;0.7" dur="2s" repeatCount="indefinite" begin="0.6s"/>
      </circle>
    </g>
  </g>
  
  <!-- 应用标题 -->
  <text x="187.5" y="420" text-anchor="middle" fill="white" font-family="system-ui, -apple-system, sans-serif" font-size="28" font-weight="700" filter="url(#shadow)">
    智能辐射监测
  </text>
  
  <!-- 副标题 -->
  <text x="187.5" y="450" text-anchor="middle" fill="rgba(255,255,255,0.9)" font-family="system-ui, -apple-system, sans-serif" font-size="14" font-weight="400">
    专业的智能穿戴核辐射监测应用
  </text>
  
  <!-- 版本信息 -->
  <rect x="157.5" y="470" width="60" height="24" rx="12" fill="rgba(255,255,255,0.1)" stroke="rgba(255,255,255,0.3)" stroke-width="1"/>
  <text x="187.5" y="485" text-anchor="middle" fill="rgba(255,255,255,0.8)" font-family="system-ui, -apple-system, sans-serif" font-size="12" font-weight="500">
    v1.0.0
  </text>
  
  <!-- 底部特性图标 -->
  <g transform="translate(187.5, 600)">
    <!-- 实时监控 -->
    <g transform="translate(-60, 0)">
      <circle cx="0" cy="0" r="18" fill="rgba(255,255,255,0.15)" stroke="rgba(255,255,255,0.3)" stroke-width="1"/>
      <g stroke="white" stroke-width="1.5" fill="none" stroke-linecap="round" stroke-linejoin="round" transform="scale(0.7)">
        <path d="M12 22s8-4 8-10V5l-8-3-8 3v7c0 6 8 10 8 10z"/>
        <path d="M9 12l2 2 4-4"/>
      </g>
      <text x="0" y="35" text-anchor="middle" fill="rgba(255,255,255,0.9)" font-family="system-ui, -apple-system, sans-serif" font-size="10">
        实时监控
      </text>
    </g>
    
    <!-- 智能分析 -->
    <g transform="translate(0, 0)">
      <circle cx="0" cy="0" r="18" fill="rgba(255,255,255,0.15)" stroke="rgba(255,255,255,0.3)" stroke-width="1"/>
      <g stroke="white" stroke-width="1.5" fill="none" stroke-linecap="round" stroke-linejoin="round" transform="scale(0.7)">
        <path d="M22 11.08V12a10 10 0 1 1-5.93-9.14"/>
        <polyline points="22 4 12 14.01 9 11.01"/>
      </g>
      <text x="0" y="35" text-anchor="middle" fill="rgba(255,255,255,0.9)" font-family="system-ui, -apple-system, sans-serif" font-size="10">
        智能分析
      </text>
    </g>
    
    <!-- 安全预警 -->
    <g transform="translate(60, 0)">
      <circle cx="0" cy="0" r="18" fill="rgba(255,255,255,0.15)" stroke="rgba(255,255,255,0.3)" stroke-width="1"/>
      <g stroke="white" stroke-width="1.5" fill="none" stroke-linecap="round" stroke-linejoin="round" transform="scale(0.7)">
        <path d="M18 8A6 6 0 0 0 6 8c0 7-3 9-3 9h18s-3-2-3-9"/>
        <path d="M13.73 21a2 2 0 0 1-3.46 0"/>
      </g>
      <text x="0" y="35" text-anchor="middle" fill="rgba(255,255,255,0.9)" font-family="system-ui, -apple-system, sans-serif" font-size="10">
        安全预警
      </text>
    </g>
  </g>
  
  <!-- 底部版权信息 -->
  <text x="187.5" y="720" text-anchor="middle" fill="rgba(255,255,255,0.7)" font-family="system-ui, -apple-system, sans-serif" font-size="11">
    © 2024 智能辐射监测系统
  </text>
  <text x="187.5" y="740" text-anchor="middle" fill="rgba(255,255,255,0.6)" font-family="system-ui, -apple-system, sans-serif" font-size="10">
    科技有限公司
  </text>
  
  <!-- 波浪装饰 -->
  <path d="M0,750 Q93.75,730 187.5,750 T375,750 L375,812 L0,812 Z" fill="rgba(255,255,255,0.1)" opacity="0.6">
    <animate attributeName="d" values="M0,750 Q93.75,730 187.5,750 T375,750 L375,812 L0,812 Z;M0,750 Q93.75,770 187.5,750 T375,750 L375,812 L0,812 Z;M0,750 Q93.75,730 187.5,750 T375,750 L375,812 L0,812 Z" dur="4s" repeatCount="indefinite"/>
  </path>
  
  <path d="M0,770 Q93.75,750 187.5,770 T375,770 L375,812 L0,812 Z" fill="rgba(255,255,255,0.05)" opacity="0.8">
    <animate attributeName="d" values="M0,770 Q93.75,750 187.5,770 T375,770 L375,812 L0,812 Z;M0,770 Q93.75,790 187.5,770 T375,770 L375,812 L0,812 Z;M0,770 Q93.75,750 187.5,770 T375,770 L375,812 L0,812 Z" dur="5s" repeatCount="indefinite"/>
  </path>
</svg>
