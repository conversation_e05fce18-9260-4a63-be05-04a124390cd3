/**
 * 响应式工具类
 * 基于390px设计稿的完美适配系统
 */

/* ================== 响应式容器 ================== */
.responsive-container {
  width: 100%;
  padding-left: var(--space-base);
  padding-right: var(--space-base);
  margin-left: auto;
  margin-right: auto;
}

/* 不同断点的容器调整 */
.breakpoint-xs .responsive-container {
  padding-left: var(--space-sm);
  padding-right: var(--space-sm);
}

.breakpoint-xl .responsive-container,
.breakpoint-xxl .responsive-container {
  max-width: 1200rpx;
  padding-left: var(--space-xl);
  padding-right: var(--space-xl);
}

/* ================== 响应式网格系统 ================== */
.responsive-grid {
  display: grid;
  gap: var(--space-base);
}

.responsive-grid-1 { grid-template-columns: 1fr; }
.responsive-grid-2 { grid-template-columns: repeat(2, 1fr); }
.responsive-grid-3 { grid-template-columns: repeat(3, 1fr); }
.responsive-grid-4 { grid-template-columns: repeat(4, 1fr); }

/* 小屏幕网格调整 */
.breakpoint-xs .responsive-grid-2,
.breakpoint-xs .responsive-grid-3,
.breakpoint-xs .responsive-grid-4 {
  grid-template-columns: 1fr;
}

.breakpoint-sm .responsive-grid-3,
.breakpoint-sm .responsive-grid-4 {
  grid-template-columns: repeat(2, 1fr);
}

/* 大屏幕网格调整 */
.breakpoint-xl .responsive-grid-2 {
  grid-template-columns: repeat(3, 1fr);
}

.breakpoint-xl .responsive-grid-3 {
  grid-template-columns: repeat(4, 1fr);
}

.breakpoint-xxl .responsive-grid-2 {
  grid-template-columns: repeat(4, 1fr);
}

.breakpoint-xxl .responsive-grid-3 {
  grid-template-columns: repeat(5, 1fr);
}

.breakpoint-xxl .responsive-grid-4 {
  grid-template-columns: repeat(6, 1fr);
}

/* ================== 响应式文本 ================== */
.text-responsive-xs { font-size: var(--font-xs); }
.text-responsive-sm { font-size: var(--font-sm); }
.text-responsive-base { font-size: var(--font-base); }
.text-responsive-lg { font-size: var(--font-lg); }
.text-responsive-xl { font-size: var(--font-xl); }
.text-responsive-2xl { font-size: var(--font-2xl); }
.text-responsive-3xl { font-size: var(--font-3xl); }
.text-responsive-4xl { font-size: var(--font-4xl); }

/* ================== 响应式间距 ================== */
/* Padding */
.p-responsive-xs { padding: var(--space-xs); }
.p-responsive-sm { padding: var(--space-sm); }
.p-responsive-base { padding: var(--space-base); }
.p-responsive-lg { padding: var(--space-lg); }
.p-responsive-xl { padding: var(--space-xl); }
.p-responsive-2xl { padding: var(--space-2xl); }
.p-responsive-3xl { padding: var(--space-3xl); }
.p-responsive-4xl { padding: var(--space-4xl); }

/* Margin */
.m-responsive-xs { margin: var(--space-xs); }
.m-responsive-sm { margin: var(--space-sm); }
.m-responsive-base { margin: var(--space-base); }
.m-responsive-lg { margin: var(--space-lg); }
.m-responsive-xl { margin: var(--space-xl); }
.m-responsive-2xl { margin: var(--space-2xl); }
.m-responsive-3xl { margin: var(--space-3xl); }
.m-responsive-4xl { margin: var(--space-4xl); }

/* Margin Bottom */
.mb-responsive-xs { margin-bottom: var(--space-xs); }
.mb-responsive-sm { margin-bottom: var(--space-sm); }
.mb-responsive-base { margin-bottom: var(--space-base); }
.mb-responsive-lg { margin-bottom: var(--space-lg); }
.mb-responsive-xl { margin-bottom: var(--space-xl); }
.mb-responsive-2xl { margin-bottom: var(--space-2xl); }
.mb-responsive-3xl { margin-bottom: var(--space-3xl); }
.mb-responsive-4xl { margin-bottom: var(--space-4xl); }

/* Gap */
.gap-responsive-xs { gap: var(--space-xs); }
.gap-responsive-sm { gap: var(--space-sm); }
.gap-responsive-base { gap: var(--space-base); }
.gap-responsive-lg { gap: var(--space-lg); }
.gap-responsive-xl { gap: var(--space-xl); }
.gap-responsive-2xl { gap: var(--space-2xl); }
.gap-responsive-3xl { gap: var(--space-3xl); }
.gap-responsive-4xl { gap: var(--space-4xl); }

/* ================== 响应式圆角 ================== */
.rounded-responsive-xs { border-radius: var(--radius-xs); }
.rounded-responsive-sm { border-radius: var(--radius-sm); }
.rounded-responsive-base { border-radius: var(--radius-base); }
.rounded-responsive-lg { border-radius: var(--radius-lg); }
.rounded-responsive-xl { border-radius: var(--radius-xl); }
.rounded-responsive-2xl { border-radius: var(--radius-2xl); }
.rounded-responsive-3xl { border-radius: var(--radius-3xl); }
.rounded-responsive-full { border-radius: var(--radius-full); }

/* ================== 响应式阴影 ================== */
.shadow-responsive-sm { box-shadow: var(--shadow-sm); }
.shadow-responsive-base { box-shadow: var(--shadow-base); }
.shadow-responsive-lg { box-shadow: var(--shadow-lg); }
.shadow-responsive-xl { box-shadow: var(--shadow-xl); }
.shadow-responsive-2xl { box-shadow: var(--shadow-2xl); }

/* ================== 响应式显示控制 ================== */
/* 隐藏在特定断点 */
.hidden-xs { display: block; }
.hidden-sm { display: block; }
.hidden-md { display: block; }
.hidden-lg { display: block; }
.hidden-xl { display: block; }
.hidden-xxl { display: block; }

.breakpoint-xs .hidden-xs { display: none !important; }
.breakpoint-sm .hidden-sm { display: none !important; }
.breakpoint-md .hidden-md { display: none !important; }
.breakpoint-lg .hidden-lg { display: none !important; }
.breakpoint-xl .hidden-xl { display: none !important; }
.breakpoint-xxl .hidden-xxl { display: none !important; }

/* 只在特定断点显示 */
.show-xs { display: none; }
.show-sm { display: none; }
.show-md { display: none; }
.show-lg { display: none; }
.show-xl { display: none; }
.show-xxl { display: none; }

.breakpoint-xs .show-xs { display: block !important; }
.breakpoint-sm .show-sm { display: block !important; }
.breakpoint-md .show-md { display: block !important; }
.breakpoint-lg .show-lg { display: block !important; }
.breakpoint-xl .show-xl { display: block !important; }
.breakpoint-xxl .show-xxl { display: block !important; }

/* ================== 响应式布局 ================== */
.flex-responsive {
  display: flex;
}

.flex-responsive-col {
  display: flex;
  flex-direction: column;
}

/* 小屏幕改为列布局 */
.breakpoint-xs .flex-responsive,
.breakpoint-sm .flex-responsive {
  flex-direction: column;
}

/* 响应式对齐 */
.justify-responsive-start { justify-content: flex-start; }
.justify-responsive-center { justify-content: center; }
.justify-responsive-end { justify-content: flex-end; }
.justify-responsive-between { justify-content: space-between; }
.justify-responsive-around { justify-content: space-around; }

.items-responsive-start { align-items: flex-start; }
.items-responsive-center { align-items: center; }
.items-responsive-end { align-items: flex-end; }
.items-responsive-stretch { align-items: stretch; }

/* 小屏幕居中对齐 */
.breakpoint-xs .justify-responsive-between,
.breakpoint-xs .justify-responsive-around {
  justify-content: center;
}

.breakpoint-xs .items-responsive-start,
.breakpoint-xs .items-responsive-end {
  align-items: center;
}

/* ================== 响应式卡片 ================== */
.card-responsive {
  background: var(--bg-card);
  border-radius: var(--radius-xl);
  padding: var(--space-lg);
  box-shadow: var(--shadow-lg);
  transition: all 0.3s ease;
}

.card-responsive:hover {
  transform: translateY(-4rpx);
  box-shadow: var(--shadow-xl);
}

/* 小屏幕卡片调整 */
.breakpoint-xs .card-responsive,
.breakpoint-sm .card-responsive {
  border-radius: var(--radius-lg);
  padding: var(--space-base);
  box-shadow: var(--shadow-base);
}

/* 大屏幕卡片调整 */
.breakpoint-xl .card-responsive,
.breakpoint-xxl .card-responsive {
  border-radius: var(--radius-2xl);
  padding: var(--space-xl);
  box-shadow: var(--shadow-xl);
}

/* ================== 响应式按钮 ================== */
.btn-responsive {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: var(--space-base) var(--space-lg);
  border-radius: var(--radius-lg);
  font-size: var(--font-base);
  font-weight: 600;
  transition: all 0.3s ease;
  border: none;
  cursor: pointer;
}

/* 小屏幕按钮调整 */
.breakpoint-xs .btn-responsive,
.breakpoint-sm .btn-responsive {
  padding: var(--space-sm) var(--space-base);
  font-size: var(--font-sm);
  border-radius: var(--radius-base);
}

/* 大屏幕按钮调整 */
.breakpoint-xl .btn-responsive,
.breakpoint-xxl .btn-responsive {
  padding: var(--space-lg) var(--space-xl);
  font-size: var(--font-lg);
  border-radius: var(--radius-xl);
}

/* ================== 响应式图标 ================== */
.icon-responsive-xs { width: var(--icon-xs); height: var(--icon-xs); }
.icon-responsive-sm { width: var(--icon-sm); height: var(--icon-sm); }
.icon-responsive-base { width: var(--icon-base); height: var(--icon-base); }
.icon-responsive-lg { width: var(--icon-lg); height: var(--icon-lg); }
.icon-responsive-xl { width: var(--icon-xl); height: var(--icon-xl); }
.icon-responsive-2xl { width: var(--icon-2xl); height: var(--icon-2xl); }

/* ================== 响应式工具类 ================== */
.w-responsive-full { width: 100%; }
.h-responsive-full { height: 100%; }
.min-h-responsive-screen { min-height: 100vh; }

/* 响应式宽度 */
.w-responsive-1-2 { width: 50%; }
.w-responsive-1-3 { width: 33.333333%; }
.w-responsive-2-3 { width: 66.666667%; }
.w-responsive-1-4 { width: 25%; }
.w-responsive-3-4 { width: 75%; }

/* 小屏幕全宽 */
.breakpoint-xs .w-responsive-1-2,
.breakpoint-xs .w-responsive-1-3,
.breakpoint-xs .w-responsive-2-3,
.breakpoint-xs .w-responsive-1-4,
.breakpoint-xs .w-responsive-3-4 {
  width: 100%;
}

/* ================== 响应式文本对齐 ================== */
.text-responsive-left { text-align: left; }
.text-responsive-center { text-align: center; }
.text-responsive-right { text-align: right; }

/* 小屏幕居中对齐 */
.breakpoint-xs .text-responsive-left,
.breakpoint-xs .text-responsive-right {
  text-align: center;
}
