// 语言管理工具
class LanguageManager {
  constructor() {
    this.currentLanguage = uni.getStorageSync('app_language') || 'zh-CN'
    this.translations = {
      'zh-CN': {
        // 通用
        'settings': '设置',
        'back': '返回',
        'confirm': '确认',
        'cancel': '取消',
        'save': '保存',
        'edit': '编辑',
        
        // 用户信息
        'monitoring_days': '监测天数',
        'health_score': '健康分数',
        'device_count': '设备数量',
        'edit_profile': '编辑资料',
        
        // 设置分组
        'monitoring_settings': '监测设置',
        'monitoring_settings_desc': '配置监测参数和阈值',
        'notification_settings': '通知设置',
        'notification_settings_desc': '管理提醒和通知方式',
        'appearance_settings': '外观设置',
        'appearance_settings_desc': '个性化界面和主题',
        'data_management': '数据管理',
        'data_management_desc': '备份、导出和清理数据',
        'help_support': '帮助与支持',
        'help_support_desc': '获取帮助和技术支持',
        
        // 设置项
        'dose_rate_threshold': '剂量率阈值',
        'monitoring_interval': '监测间隔',
        'auto_upload': '自动上传',
        'sound_alert': '声音提醒',
        'vibration_alert': '震动提醒',
        'push_notification': '推送通知',
        'theme': '主题',
        'language': '语言',
        'units': '测量单位',
        'export_data': '导出数据',
        'backup_data': '备份数据',
        'clear_data': '清理数据',
        'about': '关于应用',
        'help': '帮助中心',
        
        // 主题选项
        'light_theme': '浅色主题',
        'dark_theme': '深色主题',
        
        // 语言选项
        'chinese': '中文',
        'english': 'English',
        'japanese': '日本語',
        
        // 消息提示
        'theme_switched': '主题已切换',
        'language_switched': '语言已切换',
        'settings_saved': '设置已保存',
        'data_exported': '数据导出成功',
        'data_backed_up': '数据备份成功',
        'data_cleared': '数据清理完成',
        'profile_updated': '用户资料已更新'
      },
      'en-US': {
        // 通用
        'settings': 'Settings',
        'back': 'Back',
        'confirm': 'Confirm',
        'cancel': 'Cancel',
        'save': 'Save',
        'edit': 'Edit',
        
        // 用户信息
        'monitoring_days': 'Monitoring Days',
        'health_score': 'Health Score',
        'device_count': 'Device Count',
        'edit_profile': 'Edit Profile',
        
        // 设置分组
        'monitoring_settings': 'Monitoring Settings',
        'monitoring_settings_desc': 'Configure monitoring parameters and thresholds',
        'notification_settings': 'Notification Settings',
        'notification_settings_desc': 'Manage alerts and notification methods',
        'appearance_settings': 'Appearance Settings',
        'appearance_settings_desc': 'Personalize interface and themes',
        'data_management': 'Data Management',
        'data_management_desc': 'Backup, export and clear data',
        'help_support': 'Help & Support',
        'help_support_desc': 'Get help and technical support',
        
        // 设置项
        'dose_rate_threshold': 'Dose Rate Threshold',
        'monitoring_interval': 'Monitoring Interval',
        'auto_upload': 'Auto Upload',
        'sound_alert': 'Sound Alert',
        'vibration_alert': 'Vibration Alert',
        'push_notification': 'Push Notification',
        'theme': 'Theme',
        'language': 'Language',
        'units': 'Units',
        'export_data': 'Export Data',
        'backup_data': 'Backup Data',
        'clear_data': 'Clear Data',
        'about': 'About App',
        'help': 'Help Center',
        
        // 主题选项
        'light_theme': 'Light Theme',
        'dark_theme': 'Dark Theme',
        
        // 语言选项
        'chinese': '中文',
        'english': 'English',
        'japanese': '日本語',
        
        // 消息提示
        'theme_switched': 'Theme switched',
        'language_switched': 'Language switched',
        'settings_saved': 'Settings saved',
        'data_exported': 'Data exported successfully',
        'data_backed_up': 'Data backed up successfully',
        'data_cleared': 'Data cleared successfully',
        'profile_updated': 'Profile updated'
      },
      'ja-JP': {
        // 通用
        'settings': '設定',
        'back': '戻る',
        'confirm': '確認',
        'cancel': 'キャンセル',
        'save': '保存',
        'edit': '編集',
        
        // 用户信息
        'monitoring_days': '監視日数',
        'health_score': '健康スコア',
        'device_count': 'デバイス数',
        'edit_profile': 'プロフィール編集',
        
        // 设置分组
        'monitoring_settings': '監視設定',
        'monitoring_settings_desc': '監視パラメータと閾値を設定',
        'notification_settings': '通知設定',
        'notification_settings_desc': 'アラートと通知方法を管理',
        'appearance_settings': '外観設定',
        'appearance_settings_desc': 'インターフェースとテーマをカスタマイズ',
        'data_management': 'データ管理',
        'data_management_desc': 'データのバックアップ、エクスポート、クリア',
        'help_support': 'ヘルプとサポート',
        'help_support_desc': 'ヘルプとテクニカルサポートを取得',
        
        // 设置项
        'dose_rate_threshold': '線量率閾値',
        'monitoring_interval': '監視間隔',
        'auto_upload': '自動アップロード',
        'sound_alert': '音声アラート',
        'vibration_alert': '振動アラート',
        'push_notification': 'プッシュ通知',
        'theme': 'テーマ',
        'language': '言語',
        'units': '測定単位',
        'export_data': 'データエクスポート',
        'backup_data': 'データバックアップ',
        'clear_data': 'データクリア',
        'about': 'アプリについて',
        'help': 'ヘルプセンター',
        
        // 主题选项
        'light_theme': 'ライトテーマ',
        'dark_theme': 'ダークテーマ',
        
        // 语言选项
        'chinese': '中文',
        'english': 'English',
        'japanese': '日本語',
        
        // 消息提示
        'theme_switched': 'テーマが切り替わりました',
        'language_switched': '言語が切り替わりました',
        'settings_saved': '設定が保存されました',
        'data_exported': 'データのエクスポートが完了しました',
        'data_backed_up': 'データのバックアップが完了しました',
        'data_cleared': 'データのクリアが完了しました',
        'profile_updated': 'プロフィールが更新されました'
      }
    }
  }

  // 获取翻译文本
  t(key) {
    const translation = this.translations[this.currentLanguage]
    return translation && translation[key] ? translation[key] : key
  }

  // 切换语言
  setLanguage(language) {
    this.currentLanguage = language
    uni.setStorageSync('app_language', language)
    
    // 触发全局语言切换事件
    uni.$emit('languageChanged', language)
  }

  // 获取当前语言
  getCurrentLanguage() {
    return this.currentLanguage
  }

  // 获取支持的语言列表
  getSupportedLanguages() {
    return [
      { code: 'zh-CN', name: this.t('chinese'), icon: '🇨🇳' },
      { code: 'en-US', name: this.t('english'), icon: '🇺🇸' },
      { code: 'ja-JP', name: this.t('japanese'), icon: '🇯🇵' }
    ]
  }
}

// 创建全局实例
const languageManager = new LanguageManager()

export default languageManager
