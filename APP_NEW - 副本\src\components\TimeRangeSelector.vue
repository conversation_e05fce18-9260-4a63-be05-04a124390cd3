<template>
  <view class="time-range-selector-container">
    <!-- 快速选择按钮 -->
    <view class="quick-range-section">
      <text class="section-label">快速选择:</text>
      <view class="quick-range-options">
        <view 
          v-for="option in quickOptions" 
          :key="option.value"
          class="quick-option" 
          :class="{ active: selectedRange === option.value }"
          @tap="selectQuickRange(option.value)"
        >
          <text class="option-text">{{ option.label }}</text>
        </view>
      </view>
    </view>

    <!-- 自定义时间范围 -->
    <view class="custom-range-section">
      <view class="section-header">
        <text class="section-label">自定义范围:</text>
        <view class="toggle-custom" @tap="toggleCustomMode">
          <text class="toggle-text">{{ showCustom ? '收起' : '展开' }}</text>
          <text class="toggle-icon">{{ showCustom ? '▲' : '▼' }}</text>
        </view>
      </view>
      
      <view class="custom-inputs" v-if="showCustom">
        <view class="input-group">
          <text class="input-label">开始时间</text>
          <view class="datetime-input" @tap="showStartPicker">
            <text class="datetime-text">{{ formatDateTime(customStartTime) }}</text>
            <text class="input-icon">📅</text>
          </view>
        </view>
        
        <view class="input-group">
          <text class="input-label">结束时间</text>
          <view class="datetime-input" @tap="showEndPicker">
            <text class="datetime-text">{{ formatDateTime(customEndTime) }}</text>
            <text class="input-icon">📅</text>
          </view>
        </view>
        
        <view class="custom-actions">
          <view class="action-btn secondary" @tap="resetCustomRange">
            <text class="btn-text">重置</text>
          </view>
          <view class="action-btn primary" @tap="applyCustomRange">
            <text class="btn-text">应用</text>
          </view>
        </view>
      </view>
    </view>

    <!-- 时间范围信息 -->
    <view class="range-info">
      <view class="info-item">
        <text class="info-label">时间跨度:</text>
        <text class="info-value">{{ timeSpanText }}</text>
      </view>
      <view class="info-item">
        <text class="info-label">数据点数:</text>
        <text class="info-value">{{ estimatedDataPoints }}</text>
      </view>
    </view>

    <!-- 时间选择器弹窗 -->
    <view class="picker-overlay" v-if="showPicker" @tap="hidePicker">
      <view class="picker-container" @tap.stop>
        <view class="picker-header">
          <text class="picker-title">选择{{ pickerType === 'start' ? '开始' : '结束' }}时间</text>
          <view class="picker-close" @tap="hidePicker">
            <text>✕</text>
          </view>
        </view>
        <picker-view 
          class="datetime-picker"
          :value="pickerValue"
          @change="onPickerChange"
        >
          <picker-view-column>
            <view v-for="year in years" :key="year" class="picker-item">{{ year }}年</view>
          </picker-view-column>
          <picker-view-column>
            <view v-for="month in months" :key="month" class="picker-item">{{ month }}月</view>
          </picker-view-column>
          <picker-view-column>
            <view v-for="day in days" :key="day" class="picker-item">{{ day }}日</view>
          </picker-view-column>
          <picker-view-column>
            <view v-for="hour in hours" :key="hour" class="picker-item">{{ hour }}时</view>
          </picker-view-column>
          <picker-view-column>
            <view v-for="minute in minutes" :key="minute" class="picker-item">{{ minute }}分</view>
          </picker-view-column>
        </picker-view>
        <view class="picker-actions">
          <view class="picker-btn cancel" @tap="hidePicker">
            <text>取消</text>
          </view>
          <view class="picker-btn confirm" @tap="confirmPicker">
            <text>确定</text>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
import { ref, computed, watch } from 'vue'

export default {
  name: 'TimeRangeSelector',
  props: {
    modelValue: {
      type: String,
      default: '24h'
    }
  },
  emits: ['update:modelValue', 'range-change'],
  setup(props, { emit }) {
    const selectedRange = ref(props.modelValue)
    const showCustom = ref(false)
    const showPicker = ref(false)
    const pickerType = ref('start')
    const pickerValue = ref([0, 0, 0, 0, 0])
    
    // 自定义时间范围
    const customStartTime = ref(new Date(Date.now() - 24 * 60 * 60 * 1000))
    const customEndTime = ref(new Date())

    // 快速选择选项
    const quickOptions = [
      { label: '1小时', value: '1h' },
      { label: '6小时', value: '6h' },
      { label: '24小时', value: '24h' },
      { label: '7天', value: '7d' },
      { label: '30天', value: '30d' }
    ]

    // 时间选择器数据
    const currentYear = new Date().getFullYear()
    const years = computed(() => {
      const yearList = []
      for (let i = currentYear - 1; i <= currentYear; i++) {
        yearList.push(i)
      }
      return yearList
    })

    const months = computed(() => {
      const monthList = []
      for (let i = 1; i <= 12; i++) {
        monthList.push(i)
      }
      return monthList
    })

    const days = computed(() => {
      const dayList = []
      for (let i = 1; i <= 31; i++) {
        dayList.push(i)
      }
      return dayList
    })

    const hours = computed(() => {
      const hourList = []
      for (let i = 0; i <= 23; i++) {
        hourList.push(i)
      }
      return hourList
    })

    const minutes = computed(() => {
      const minuteList = []
      for (let i = 0; i <= 59; i += 5) {
        minuteList.push(i)
      }
      return minuteList
    })

    // 计算属性
    const timeSpanText = computed(() => {
      if (selectedRange.value === 'custom') {
        const diff = customEndTime.value.getTime() - customStartTime.value.getTime()
        const hours = Math.floor(diff / (1000 * 60 * 60))
        const days = Math.floor(hours / 24)
        
        if (days > 0) {
          return `${days}天${hours % 24}小时`
        } else {
          return `${hours}小时`
        }
      } else {
        const option = quickOptions.find(opt => opt.value === selectedRange.value)
        return option ? option.label : '未知'
      }
    })

    const estimatedDataPoints = computed(() => {
      if (selectedRange.value === 'custom') {
        const diff = customEndTime.value.getTime() - customStartTime.value.getTime()
        const hours = Math.floor(diff / (1000 * 60 * 60))
        return Math.min(hours * 60, 1000) // 假设每分钟一个数据点，最多1000个
      } else {
        const pointsMap = {
          '1h': 60,
          '6h': 360,
          '24h': 1440,
          '7d': 1000,
          '30d': 1000
        }
        return pointsMap[selectedRange.value] || 0
      }
    })

    // 方法
    const selectQuickRange = (range) => {
      selectedRange.value = range
      emit('update:modelValue', range)
      emit('range-change', {
        type: 'quick',
        value: range,
        startTime: getStartTimeForRange(range),
        endTime: new Date()
      })
    }

    const getStartTimeForRange = (range) => {
      const now = new Date()
      const timeMap = {
        '1h': 1 * 60 * 60 * 1000,
        '6h': 6 * 60 * 60 * 1000,
        '24h': 24 * 60 * 60 * 1000,
        '7d': 7 * 24 * 60 * 60 * 1000,
        '30d': 30 * 24 * 60 * 60 * 1000
      }
      return new Date(now.getTime() - (timeMap[range] || 0))
    }

    const toggleCustomMode = () => {
      showCustom.value = !showCustom.value
    }

    const showStartPicker = () => {
      pickerType.value = 'start'
      setPickerValue(customStartTime.value)
      showPicker.value = true
    }

    const showEndPicker = () => {
      pickerType.value = 'end'
      setPickerValue(customEndTime.value)
      showPicker.value = true
    }

    const setPickerValue = (date) => {
      const year = years.value.indexOf(date.getFullYear())
      const month = date.getMonth()
      const day = date.getDate() - 1
      const hour = date.getHours()
      const minute = Math.floor(date.getMinutes() / 5)
      
      pickerValue.value = [year, month, day, hour, minute]
    }

    const onPickerChange = (e) => {
      pickerValue.value = e.detail.value
    }

    const confirmPicker = () => {
      const [yearIndex, monthIndex, dayIndex, hourIndex, minuteIndex] = pickerValue.value
      const selectedDate = new Date(
        years.value[yearIndex],
        monthIndex,
        dayIndex + 1,
        hours.value[hourIndex],
        minutes.value[minuteIndex]
      )

      if (pickerType.value === 'start') {
        customStartTime.value = selectedDate
      } else {
        customEndTime.value = selectedDate
      }

      hidePicker()
    }

    const hidePicker = () => {
      showPicker.value = false
    }

    const resetCustomRange = () => {
      customStartTime.value = new Date(Date.now() - 24 * 60 * 60 * 1000)
      customEndTime.value = new Date()
    }

    const applyCustomRange = () => {
      selectedRange.value = 'custom'
      emit('update:modelValue', 'custom')
      emit('range-change', {
        type: 'custom',
        value: 'custom',
        startTime: customStartTime.value,
        endTime: customEndTime.value
      })
      showCustom.value = false
    }

    const formatDateTime = (date) => {
      return date.toLocaleString('zh-CN', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit'
      })
    }

    // 监听props变化
    watch(() => props.modelValue, (newValue) => {
      selectedRange.value = newValue
    })

    return {
      selectedRange,
      showCustom,
      showPicker,
      pickerType,
      pickerValue,
      customStartTime,
      customEndTime,
      quickOptions,
      years,
      months,
      days,
      hours,
      minutes,
      timeSpanText,
      estimatedDataPoints,
      selectQuickRange,
      toggleCustomMode,
      showStartPicker,
      showEndPicker,
      onPickerChange,
      confirmPicker,
      hidePicker,
      resetCustomRange,
      applyCustomRange,
      formatDateTime
    }
  }
}
</script>

<style scoped>
.time-range-selector-container {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  border-radius: 20px;
  padding: 20px;
  margin: 0; /* 移除边距，依赖父容器card-container */
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.08);
  border: 1px solid rgba(226, 232, 240, 0.6);
}

.quick-range-section {
  margin-bottom: 20px;
}

.section-label {
  font-size: 14px;
  font-weight: 600;
  color: #374151;
  margin-bottom: 12px;
  display: block;
}

.quick-range-options {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
}

.quick-option {
  flex: 1;
  min-width: 60px;
  padding: 10px 12px;
  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
  border-radius: 12px;
  text-align: center;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  border: 2px solid transparent;
  cursor: pointer;
}

.quick-option:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.quick-option.active {
  background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
  border-color: #3b82f6;
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(59, 130, 246, 0.3);
}

.option-text {
  font-size: 13px;
  font-weight: 600;
  color: #64748b;
  transition: color 0.3s ease;
}

.quick-option.active .option-text {
  color: #ffffff;
}

.custom-range-section {
  border-top: 1px solid rgba(226, 232, 240, 0.6);
  padding-top: 20px;
  margin-bottom: 20px;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.toggle-custom {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 6px 12px;
  background: rgba(59, 130, 246, 0.1);
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.toggle-custom:hover {
  background: rgba(59, 130, 246, 0.15);
}

.toggle-text {
  font-size: 12px;
  color: #3b82f6;
  font-weight: 600;
}

.toggle-icon {
  font-size: 10px;
  color: #3b82f6;
}

.custom-inputs {
  display: flex;
  flex-direction: column;
  gap: 16px;
  animation: slideDown 0.3s ease;
}

@keyframes slideDown {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.input-group {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.input-label {
  font-size: 13px;
  font-weight: 600;
  color: #4b5563;
}

.datetime-input {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  background: #f8fafc;
  border-radius: 12px;
  border: 1px solid #e2e8f0;
  cursor: pointer;
  transition: all 0.3s ease;
}

.datetime-input:hover {
  border-color: #667eea;
  background: #f1f5f9;
}

.datetime-text {
  font-size: 14px;
  color: #374151;
  font-weight: 500;
}

.input-icon {
  font-size: 16px;
}

.custom-actions {
  display: flex;
  gap: 12px;
  margin-top: 8px;
}

.action-btn {
  flex: 1;
  padding: 12px;
  border-radius: 12px;
  text-align: center;
  cursor: pointer;
  transition: all 0.3s ease;
  font-weight: 600;
}

.action-btn.secondary {
  background: #f3f4f6;
  border: 1px solid #d1d5db;
}

.action-btn.secondary:hover {
  background: #e5e7eb;
}

.action-btn.primary {
  background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
  border: 1px solid #3b82f6;
  box-shadow: 0 2px 8px rgba(59, 130, 246, 0.2);
}

.action-btn.primary:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
}

.btn-text {
  font-size: 14px;
  color: #374151;
}

.action-btn.primary .btn-text {
  color: #ffffff;
}

.range-info {
  display: flex;
  justify-content: space-between;
  padding: 16px;
  background: linear-gradient(135deg, rgba(59, 130, 246, 0.05) 0%, rgba(29, 78, 216, 0.05) 100%);
  border-radius: 12px;
  border: 1px solid rgba(59, 130, 246, 0.1);
}

.info-item {
  text-align: center;
  flex: 1;
}

.info-label {
  font-size: 11px;
  color: #6b7280;
  display: block;
  margin-bottom: 4px;
  font-weight: 500;
}

.info-value {
  font-size: 14px;
  font-weight: 600;
  color: #374151;
  display: block;
}

/* 时间选择器弹窗 */
.picker-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  animation: fadeIn 0.3s ease;
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

.picker-container {
  background: #ffffff;
  border-radius: 20px;
  width: 90%;
  max-width: 400px;
  max-height: 80vh;
  overflow: hidden;
  animation: slideUp 0.3s ease;
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(50px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.picker-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px;
  border-bottom: 1px solid #e5e7eb;
}

.picker-title {
  font-size: 18px;
  font-weight: 600;
  color: #111827;
}

.picker-close {
  width: 32px;
  height: 32px;
  border-radius: 16px;
  background: #f3f4f6;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s ease;
}

.picker-close:hover {
  background: #e5e7eb;
}

.datetime-picker {
  height: 300px;
}

.picker-item {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 40px;
  font-size: 16px;
  color: #374151;
}

.picker-actions {
  display: flex;
  border-top: 1px solid #e5e7eb;
}

.picker-btn {
  flex: 1;
  padding: 16px;
  text-align: center;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 16px;
  font-weight: 600;
}

.picker-btn.cancel {
  color: #6b7280;
  border-right: 1px solid #e5e7eb;
}

.picker-btn.cancel:hover {
  background: #f9fafb;
}

.picker-btn.confirm {
  color: #3b82f6;
}

.picker-btn.confirm:hover {
  background: rgba(59, 130, 246, 0.05);
}

/* 响应式设计 */
@media screen and (max-width: 480px) {
  .time-range-selector-container {
    margin: 0; /* 移除边距，依赖父容器card-container */
    padding: 16px;
  }

  .quick-range-options {
    gap: 6px;
  }

  .quick-option {
    min-width: 50px;
    padding: 8px 10px;
  }

  .option-text {
    font-size: 12px;
  }

  .custom-inputs {
    gap: 12px;
  }

  .range-info {
    flex-direction: column;
    gap: 8px;
    text-align: left;
  }

  .info-item {
    text-align: left;
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

  .info-label {
    margin-bottom: 0;
  }
}
</style>
