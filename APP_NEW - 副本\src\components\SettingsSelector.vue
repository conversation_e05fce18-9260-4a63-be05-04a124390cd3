<template>
  <SettingsModal 
    v-model="visible" 
    :title="title" 
    :icon-text="iconText"
    :icon-class="iconClass"
    @confirm="onConfirm"
    @cancel="onCancel"
  >
    <view class="selector-content">
      <view class="selector-description" v-if="description">
        <text class="description-text">{{ description }}</text>
      </view>
      
      <view class="selector-options">
        <view 
          v-for="(option, index) in options" 
          :key="index"
          class="selector-option"
          :class="{ active: selectedIndex === index }"
          @tap="selectOption(index)"
        >
          <view class="option-icon" v-if="option.icon">
            <text class="option-icon-text">{{ option.icon }}</text>
          </view>
          <view class="option-content">
            <text class="option-label">{{ option.label }}</text>
            <text class="option-desc" v-if="option.desc">{{ option.desc }}</text>
          </view>
          <view class="option-check" v-if="selectedIndex === index">
            <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
              <path d="M20 6L9 17l-5-5"></path>
            </svg>
          </view>
        </view>
      </view>
      
      <!-- 自定义输入区域 -->
      <view class="custom-input-section" v-if="allowCustom && showCustomInput">
        <view class="input-label">
          <text class="label-text">自定义值</text>
        </view>
        <input 
          class="custom-input" 
          v-model="customValue" 
          :placeholder="customPlaceholder"
          :type="inputType"
          @input="onCustomInput"
        />
      </view>
    </view>
  </SettingsModal>
</template>

<script>
import { ref, watch, computed } from 'vue'
import SettingsModal from './SettingsModal.vue'

export default {
  name: 'SettingsSelector',
  components: {
    SettingsModal
  },
  props: {
    modelValue: {
      type: Boolean,
      default: false
    },
    title: {
      type: String,
      default: '选择设置'
    },
    iconText: {
      type: String,
      default: '⚙️'
    },
    iconClass: {
      type: String,
      default: 'default'
    },
    description: {
      type: String,
      default: ''
    },
    options: {
      type: Array,
      default: () => []
    },
    defaultIndex: {
      type: Number,
      default: 0
    },
    allowCustom: {
      type: Boolean,
      default: false
    },
    customPlaceholder: {
      type: String,
      default: '请输入自定义值'
    },
    inputType: {
      type: String,
      default: 'text'
    }
  },
  emits: ['update:modelValue', 'confirm', 'cancel'],
  setup(props, { emit }) {
    const visible = ref(false)
    const selectedIndex = ref(props.defaultIndex)
    const customValue = ref('')
    const showCustomInput = ref(false)
    
    watch(() => props.modelValue, (newVal) => {
      visible.value = newVal
      if (newVal) {
        selectedIndex.value = props.defaultIndex
        customValue.value = ''
        showCustomInput.value = false
      }
    })
    
    const selectOption = (index) => {
      selectedIndex.value = index
      // 触发震动反馈
      uni.vibrateShort()
      
      // 检查是否是自定义选项
      const option = props.options[index]
      if (option && option.custom) {
        showCustomInput.value = true
      } else {
        showCustomInput.value = false
      }
    }
    
    const onCustomInput = (e) => {
      customValue.value = e.detail.value
    }
    
    const onConfirm = () => {
      const selectedOption = props.options[selectedIndex.value]
      let result = {
        index: selectedIndex.value,
        option: selectedOption
      }
      
      // 如果是自定义选项且有输入值
      if (selectedOption && selectedOption.custom && customValue.value) {
        result.customValue = customValue.value
      }
      
      emit('confirm', result)
      emit('update:modelValue', false)
    }
    
    const onCancel = () => {
      emit('cancel')
      emit('update:modelValue', false)
    }
    
    return {
      visible,
      selectedIndex,
      customValue,
      showCustomInput,
      selectOption,
      onCustomInput,
      onConfirm,
      onCancel
    }
  }
}
</script>

<style scoped>
.selector-content {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.selector-description {
  padding: 16px;
  background: rgba(248, 250, 252, 0.6);
  border-radius: 12px;
  border: 1px solid rgba(226, 232, 240, 0.6);
}

.description-text {
  font-size: 14px;
  color: #64748b;
  line-height: 1.5;
}

.selector-options {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.selector-option {
  display: flex;
  align-items: center;
  padding: 16px;
  background: rgba(248, 250, 252, 0.4);
  border-radius: 12px;
  border: 2px solid transparent;
  transition: all 0.3s ease;
  cursor: pointer;
}

.selector-option:active {
  transform: scale(0.98);
}

.selector-option.active {
  background: rgba(59, 130, 246, 0.1);
  border-color: rgba(59, 130, 246, 0.3);
  box-shadow: 0 4px 16px rgba(59, 130, 246, 0.1);
}

.option-icon {
  width: 32px;
  height: 32px;
  border-radius: 8px;
  background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 12px;
}

.option-icon-text {
  font-size: 16px;
  color: white;
}

.option-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.option-label {
  font-size: 16px;
  font-weight: 600;
  color: #0f172a;
}

.option-desc {
  font-size: 14px;
  color: #64748b;
  line-height: 1.4;
}

.option-check {
  width: 24px;
  height: 24px;
  border-radius: 12px;
  background: linear-gradient(135deg, #10b981 0%, #059669 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  animation: checkIn 0.3s ease;
}

@keyframes checkIn {
  from {
    opacity: 0;
    transform: scale(0.5);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

.option-check svg {
  width: 14px;
  height: 14px;
  color: white;
}

.custom-input-section {
  display: flex;
  flex-direction: column;
  gap: 12px;
  padding: 16px;
  background: rgba(255, 255, 255, 0.8);
  border-radius: 12px;
  border: 1px solid rgba(226, 232, 240, 0.6);
  animation: slideDown 0.3s ease;
}

@keyframes slideDown {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.input-label {
  display: flex;
  align-items: center;
}

.label-text {
  font-size: 14px;
  font-weight: 600;
  color: #374151;
}

.custom-input {
  padding: 12px 16px;
  background: rgba(255, 255, 255, 0.9);
  border: 2px solid rgba(226, 232, 240, 0.8);
  border-radius: 8px;
  font-size: 16px;
  color: #0f172a;
  transition: all 0.3s ease;
}

.custom-input:focus {
  border-color: #3b82f6;
  outline: none;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}
</style>
