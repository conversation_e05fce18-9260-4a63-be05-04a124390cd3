import { ref, reactive } from 'vue'

// 定位状态
export const locationState = reactive({
  isEnabled: false,
  isLocating: false,
  hasPermission: false,
  accuracy: 0,
  lastUpdate: null,
  error: null
})

// 当前位置信息
export const currentLocation = ref({
  latitude: null,
  longitude: null,
  altitude: null,
  accuracy: null,
  speed: null,
  address: '',
  name: '当前位置'
})

// 位置历史记录
export const locationHistory = ref([])

// 定位配置
const locationConfig = {
  type: 'gcj02', // 坐标系类型：wgs84、gcj02
  altitude: true, // 传入 true 会返回高度信息
  geocode: false, // 传入 true 会返回地址信息
  highAccuracyExpireTime: 3000, // 高精度定位超时时间
  timeout: 10000, // 定位超时时间
  cacheTimeout: 30000 // 定位缓存超时时间
}

// 定位监听器
let locationWatcher = null
let watcherCallbacks = []

/**
 * 检查定位权限
 */
export const checkLocationPermission = () => {
  return new Promise((resolve, reject) => {
    // 检查是否在H5环境
    // #ifdef H5
    // H5环境下，浏览器会在调用定位API时自动请求权限
    locationState.hasPermission = true
    resolve(true)
    // #endif

    // #ifndef H5
    // 小程序环境
    if (typeof uni.getSetting === 'function') {
      uni.getSetting({
        success: (res) => {
          const hasPermission = res.authSetting['scope.userLocation']
          locationState.hasPermission = hasPermission !== false
          resolve(hasPermission)
        },
        fail: (err) => {
          console.error('检查定位权限失败:', err)
          locationState.hasPermission = false
          reject(err)
        }
      })
    } else {
      // 如果API不存在，假设有权限
      locationState.hasPermission = true
      resolve(true)
    }
    // #endif
  })
}

/**
 * 请求定位权限
 */
export const requestLocationPermission = () => {
  return new Promise((resolve, reject) => {
    // #ifdef H5
    // H5环境下，浏览器会在调用定位API时自动请求权限
    locationState.hasPermission = true
    resolve(true)
    // #endif

    // #ifndef H5
    // 小程序环境
    if (typeof uni.authorize === 'function') {
      uni.authorize({
        scope: 'scope.userLocation',
        success: () => {
          locationState.hasPermission = true
          resolve(true)
        },
        fail: () => {
          // 权限被拒绝，引导用户到设置页面
          if (typeof uni.showModal === 'function') {
            uni.showModal({
              title: '定位权限',
              content: '需要定位权限来显示您的位置，请在设置中开启',
              confirmText: '去设置',
              success: (res) => {
                if (res.confirm && typeof uni.openSetting === 'function') {
                  uni.openSetting({
                    success: (settingRes) => {
                      const hasPermission = settingRes.authSetting['scope.userLocation']
                      locationState.hasPermission = hasPermission
                      if (hasPermission) {
                        resolve(true)
                      } else {
                        reject(new Error('用户拒绝授权定位权限'))
                      }
                    }
                  })
                } else {
                  reject(new Error('用户拒绝授权定位权限'))
                }
              }
            })
          } else {
            reject(new Error('用户拒绝授权定位权限'))
          }
        }
      })
    } else {
      // 如果API不存在，假设有权限
      locationState.hasPermission = true
      resolve(true)
    }
    // #endif
  })
}

/**
 * 获取当前位置
 */
export const getCurrentLocation = (options = {}) => {
  return new Promise((resolve, reject) => {
    locationState.isLocating = true
    locationState.error = null

    const config = { ...locationConfig, ...options }

    // #ifdef H5
    // H5环境下使用浏览器的地理位置API作为备选
    if (typeof uni.getLocation === 'function') {
      uni.getLocation({
        ...config,
        success: handleLocationSuccess,
        fail: (err) => {
          console.warn('uni.getLocation失败，尝试使用浏览器API:', err)
          // 尝试使用浏览器原生API
          if (navigator.geolocation) {
            navigator.geolocation.getCurrentPosition(
              (position) => {
                const res = {
                  latitude: position.coords.latitude,
                  longitude: position.coords.longitude,
                  altitude: position.coords.altitude,
                  accuracy: position.coords.accuracy,
                  speed: position.coords.speed
                }
                handleLocationSuccess(res)
              },
              (error) => {
                handleLocationError(error)
              },
              {
                enableHighAccuracy: true,
                timeout: config.timeout || 10000,
                maximumAge: config.cacheTimeout || 30000
              }
            )
          } else {
            handleLocationError(new Error('浏览器不支持地理位置API'))
          }
        }
      })
    } else if (navigator.geolocation) {
      // 直接使用浏览器API
      navigator.geolocation.getCurrentPosition(
        (position) => {
          const res = {
            latitude: position.coords.latitude,
            longitude: position.coords.longitude,
            altitude: position.coords.altitude,
            accuracy: position.coords.accuracy,
            speed: position.coords.speed
          }
          handleLocationSuccess(res)
        },
        (error) => {
          handleLocationError(error)
        },
        {
          enableHighAccuracy: true,
          timeout: config.timeout || 10000,
          maximumAge: config.cacheTimeout || 30000
        }
      )
    } else {
      handleLocationError(new Error('定位功能不可用'))
    }
    // #endif

    // #ifndef H5
    // 小程序环境
    if (typeof uni.getLocation === 'function') {
      uni.getLocation({
        ...config,
        success: handleLocationSuccess,
        fail: handleLocationError
      })
    } else {
      handleLocationError(new Error('定位功能不可用'))
    }
    // #endif

    function handleLocationSuccess(res) {
      const location = {
        latitude: res.latitude,
        longitude: res.longitude,
        altitude: res.altitude || null,
        accuracy: res.accuracy || null,
        speed: res.speed || null,
        address: res.address || '',
        name: '当前位置',
        timestamp: Date.now()
      }

      // 更新当前位置
      currentLocation.value = location

      // 更新状态
      locationState.isLocating = false
      locationState.isEnabled = true
      locationState.accuracy = res.accuracy || 0
      locationState.lastUpdate = Date.now()

      // 添加到历史记录
      addLocationToHistory(location)

      resolve(location)
    }

    function handleLocationError(err) {
      locationState.isLocating = false
      locationState.error = err

      console.error('定位失败:', err)

      // 处理不同的错误类型
      let errorMessage = '定位失败'
      if (err.message) {
        errorMessage = err.message
      } else if (err.errMsg) {
        if (err.errMsg.includes('auth deny')) {
          errorMessage = '定位权限被拒绝'
        } else if (err.errMsg.includes('timeout')) {
          errorMessage = '定位超时'
        } else if (err.errMsg.includes('network')) {
          errorMessage = '网络错误'
        }
      } else if (err.code) {
        switch (err.code) {
          case 1:
            errorMessage = '定位权限被拒绝'
            break
          case 2:
            errorMessage = '位置信息不可用'
            break
          case 3:
            errorMessage = '定位超时'
            break
          default:
            errorMessage = '定位失败'
        }
      }

      reject(new Error(errorMessage))
    }
  })
}

/**
 * 开始位置监听
 */
export const startLocationWatch = (callback, options = {}) => {
  if (locationWatcher) {
    console.warn('位置监听已经开启')
    return
  }

  // 添加回调函数
  if (callback && typeof callback === 'function') {
    watcherCallbacks.push(callback)
  }

  const config = { ...locationConfig, ...options }

  // 开始监听位置变化
  const watchLocation = () => {
    getCurrentLocation(config)
      .then((location) => {
        // 通知所有回调函数
        watcherCallbacks.forEach(cb => {
          try {
            cb(location)
          } catch (error) {
            console.error('位置监听回调执行错误:', error)
          }
        })
      })
      .catch((error) => {
        console.error('位置监听错误:', error)
        // 通知错误
        watcherCallbacks.forEach(cb => {
          try {
            cb(null, error)
          } catch (err) {
            console.error('位置监听错误回调执行错误:', err)
          }
        })
      })
  }

  // 立即执行一次
  watchLocation()

  // 设置定时器
  locationWatcher = setInterval(watchLocation, 5000) // 每5秒更新一次位置

  console.log('位置监听已开启')
}

/**
 * 停止位置监听
 */
export const stopLocationWatch = () => {
  if (locationWatcher) {
    clearInterval(locationWatcher)
    locationWatcher = null
    watcherCallbacks = []
    console.log('位置监听已停止')
  }
}

/**
 * 添加位置到历史记录
 */
const addLocationToHistory = (location) => {
  locationHistory.value.unshift(location)
  
  // 限制历史记录数量
  if (locationHistory.value.length > 100) {
    locationHistory.value = locationHistory.value.slice(0, 100)
  }
}

/**
 * 计算两点间距离（米）
 */
export const calculateDistance = (lat1, lng1, lat2, lng2) => {
  const R = 6371000 // 地球半径（米）
  const dLat = (lat2 - lat1) * Math.PI / 180
  const dLng = (lng2 - lng1) * Math.PI / 180
  const a = Math.sin(dLat / 2) * Math.sin(dLat / 2) +
    Math.cos(lat1 * Math.PI / 180) * Math.cos(lat2 * Math.PI / 180) *
    Math.sin(dLng / 2) * Math.sin(dLng / 2)
  const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a))
  return R * c
}

/**
 * 格式化坐标
 */
export const formatCoordinate = (lat, lng) => {
  if (!lat || !lng) return '未知位置'
  return `${lat.toFixed(6)}, ${lng.toFixed(6)}`
}

/**
 * 初始化定位服务
 */
export const initLocationService = async () => {
  try {
    // 检查权限
    await checkLocationPermission()
    
    if (!locationState.hasPermission) {
      await requestLocationPermission()
    }
    
    // 获取初始位置
    await getCurrentLocation()
    
    console.log('定位服务初始化成功')
    return true
  } catch (error) {
    console.error('定位服务初始化失败:', error)
    return false
  }
}

/**
 * 清理定位服务
 */
export const cleanupLocationService = () => {
  stopLocationWatch()
  locationHistory.value = []
  currentLocation.value = {
    latitude: null,
    longitude: null,
    altitude: null,
    accuracy: null,
    speed: null,
    address: '',
    name: '当前位置'
  }
  locationState.isEnabled = false
  locationState.isLocating = false
  locationState.error = null
}
