<template>
  <view class="container">
    <!-- Toast容器 -->
    <ToastContainer />

    <!-- 圆角矩形头部卡片 - 与数据卡片对齐 -->
    <view class="card-container animate-slide-in" style="animation-delay: 0.1s;">
      <view class="header-card-modern">
        <view class="header-content-card">
          <view class="header-left">
            <view class="app-icon-container-card">
              <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2.5" stroke-linecap="round" stroke-linejoin="round">
                <path d="M3 3v18h18"></path>
                <path d="M7 16l4-4 4 4 6-6"></path>
              </svg>
            </view>
            <view class="header-text-card">
              <text class="app-title-card">数据分析中心</text>
              <text class="app-subtitle-card">{{ currentTime }} • 智能分析</text>
            </view>
          </view>
          <view class="header-right">
            <view class="status-indicator-card animate-pulse" :class="{ active: deviceState.connection.mqtt }">
              <view class="status-dot-card animate-glow"></view>
            </view>
            <view class="refresh-button-card animate-fade-scale" :class="{ 'refreshing': isLoading, 'success': refreshSuccess }" style="animation-delay: 1.2s;" @tap="refreshChart">
              <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                <path d="M3 12a9 9 0 0 1 9-9 9.75 9.75 0 0 1 6.74 2.74L21 8"></path>
                <path d="M21 3v5h-5"></path>
                <path d="M21 12a9 9 0 0 1-9 9 9.75 9.75 0 0 1-6.74-2.74L3 16"></path>
                <path d="M3 21v-5h5"></path>
              </svg>
            </view>
          </view>
        </view>
      </view>
    </view>

    <!-- 时间范围选择器 - 参考仪表盘卡片样式 -->
    <view class="card-container animate-slide-in" style="animation-delay: 0.2s;">
      <TimeRangeSelector
        v-model="selectedTimeRange"
        @range-change="onRangeChange"
      />
    </view>

    <!-- 核心数据卡片 - 参考仪表盘设计 -->
    <view class="card-container animate-slide-in" style="animation-delay: 0.3s;">
      <view class="data-cards-redesigned">
        <view class="data-card-modern primary interactive-card" @tap="onDoseRateCardTap">
          <view class="card-header-modern">
            <view class="card-icon-modern dose-rate pulse-icon">
              <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                <path d="M22 12h-4l-3 9L9 3l-3 9H2"></path>
              </svg>
            </view>
            <view class="card-title-modern">
              <text class="title-modern">当前剂量率</text>
              <text class="subtitle-modern">实时监测</text>
            </view>
          </view>
          <view class="card-value-section">
            <text class="value-modern animate-bounce-in counter-animation" style="animation-delay: 0.8s;">{{ animatedDoseRate.toFixed(2) }}</text>
            <text class="unit-modern animate-fade-scale" style="animation-delay: 0.9s;">μSv/h</text>
          </view>
          <view class="trend-indicator-modern">
            <text class="trend-icon-modern animate-float">{{ doseRateTrend.icon }}</text>
            <text class="trend-text-modern" :class="doseRateTrend.class">{{ doseRateTrend.text }}</text>
          </view>
          <view class="card-ripple"></view>
        </view>

        <view class="data-card-modern secondary interactive-card" @tap="onCpsCardTap">
          <view class="card-header-modern">
            <view class="card-icon-modern cps pulse-icon">
              <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                <circle cx="12" cy="12" r="10"></circle>
                <path d="M12 6v6l4 2"></path>
              </svg>
            </view>
            <view class="card-title-modern">
              <text class="title-modern">计数率</text>
              <text class="subtitle-modern">每秒计数</text>
            </view>
          </view>
          <view class="card-value-section">
            <text class="value-modern animate-bounce-in counter-animation" style="animation-delay: 1.0s;">{{ animatedCps.toFixed(0) }}</text>
            <text class="unit-modern animate-fade-scale" style="animation-delay: 1.1s;">CPS</text>
          </view>
          <view class="trend-indicator-modern">
            <text class="trend-icon-modern animate-float">{{ cpsTrend.icon }}</text>
            <text class="trend-text-modern" :class="cpsTrend.class">{{ cpsTrend.text }}</text>
          </view>
          <view class="card-ripple"></view>
        </view>
      </view>
    </view>

    <!-- 数据统计概览 - 2x2网格布局 -->
    <view class="card-container animate-slide-in" style="animation-delay: 0.4s;">
      <view class="stats-overview-redesigned">
        <view class="stats-header-redesigned">
          <view class="header-left-section">
            <view class="stats-icon">
              <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                <path d="M3 3v18h18"></path>
                <path d="M7 16l4-4 4 4 6-6"></path>
              </svg>
            </view>
            <view class="header-text-section">
              <text class="stats-title-redesigned">数据统计</text>
              <text class="stats-subtitle-redesigned">{{ selectedTimeRange.toUpperCase() }} 时段分析</text>
            </view>
          </view>
          <view class="time-badge-redesigned">
            <text class="time-text-redesigned">{{ new Date().toLocaleTimeString('zh-CN', { hour: '2-digit', minute: '2-digit' }) }}</text>
          </view>
        </view>

        <!-- 2x2统计网格 -->
        <view class="stats-grid-2x2">
          <!-- 数据质量 -->
          <view class="stat-card-redesigned quality-card interactive-stat-card" @tap="onStatCardTap('quality')">
            <view class="stat-header-redesigned">
              <view class="stat-icon-redesigned quality-icon pulse-icon">
                <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                  <path d="M12 22s8-4 8-10V5l-8-3-8 3v7c0 6 8 10 8 10z"></path>
                  <path d="M9 12l2 2 4-4"></path>
                </svg>
              </view>
              <text class="stat-label-redesigned">数据质量</text>
            </view>
            <text class="stat-value-redesigned counter-animation">{{ dataQuality }}%</text>
            <text class="stat-description-redesigned">信号稳定</text>
            <view class="stat-card-glow"></view>
          </view>

          <!-- 采样频率 -->
          <view class="stat-card-redesigned frequency-card interactive-stat-card" @tap="onStatCardTap('frequency')">
            <view class="stat-header-redesigned">
              <view class="stat-icon-redesigned frequency-icon pulse-icon">
                <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                  <path d="M12 2v20"></path>
                  <path d="M2 12h20"></path>
                  <path d="M6 8l6 6 6-6"></path>
                </svg>
              </view>
              <text class="stat-label-redesigned">采样频率</text>
            </view>
            <text class="stat-value-redesigned counter-animation">{{ samplingRate }}</text>
            <text class="stat-description-redesigned">次/分钟</text>
            <view class="stat-card-glow"></view>
          </view>

          <!-- 数据范围 -->
          <view class="stat-card-redesigned range-card interactive-stat-card" @tap="onStatCardTap('range')">
            <view class="stat-header-redesigned">
              <view class="stat-icon-redesigned range-icon pulse-icon">
                <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                  <path d="M8 3v3a2 2 0 0 1-2 2H3"></path>
                  <path d="M21 8h-3a2 2 0 0 1-2-2V3"></path>
                  <path d="M3 16h3a2 2 0 0 1 2 2v3"></path>
                  <path d="M16 21v-3a2 2 0 0 1 2-2h3"></path>
                </svg>
              </view>
              <text class="stat-label-redesigned">数据范围</text>
            </view>
            <text class="stat-value-redesigned counter-animation">{{ dataRange }}</text>
            <text class="stat-description-redesigned">μSv/h</text>
            <view class="stat-card-glow"></view>
          </view>

          <!-- 异常检测 -->
          <view class="stat-card-redesigned anomaly-card interactive-stat-card" @tap="onStatCardTap('anomaly')">
            <view class="stat-header-redesigned">
              <view class="stat-icon-redesigned anomaly-icon pulse-icon">
                <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                  <path d="M10.29 3.86L1.82 18a2 2 0 0 0 1.71 3h16.94a2 2 0 0 0 1.71-3L13.71 3.86a2 2 0 0 0-3.42 0z"></path>
                  <line x1="12" y1="9" x2="12" y2="13"></line>
                  <line x1="12" y1="17" x2="12.01" y2="17"></line>
                </svg>
              </view>
              <text class="stat-label-redesigned">异常检测</text>
            </view>
            <text class="stat-value-redesigned counter-animation">{{ anomalyCount }}</text>
            <text class="stat-description-redesigned">个异常点</text>
            <view class="stat-card-glow"></view>
          </view>
        </view>
      </view>
    </view>

    <!-- 预测数据图表 - 美化波形显示 -->
    <view class="card-container animate-slide-in" style="animation-delay: 0.5s;">
      <view class="prediction-chart-container">
        <view class="chart-header-enhanced">
          <view class="chart-icon-wrapper-enhanced">
            <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2.5" stroke-linecap="round" stroke-linejoin="round">
              <path d="M22 12h-4l-3 9L9 3l-3 9H2"></path>
            </svg>
          </view>
          <view class="chart-title-section-enhanced">
            <text class="chart-title-enhanced">剂量率监测</text>
            <text class="chart-subtitle-enhanced">实时数据 • 平滑曲线</text>
          </view>
        </view>

        <!-- 预测图表画布 -->
        <view class="chart-canvas-wrapper">
          <canvas
            canvas-id="predictionChart"
            class="prediction-canvas"
            :style="{ width: '100%', height: '280px' }"
          ></canvas>
        </view>

        <!-- 置信度指示器 - 移到图表下方 -->
        <view class="chart-legend-bottom">
          <view class="indicator-item-enhanced">
            <view class="indicator-color-enhanced"></view>
            <text class="indicator-text-enhanced">±1% 误差带</text>
          </view>
          <view class="indicator-item-enhanced">
            <view class="confidence-indicator-high"></view>
            <text class="indicator-text-enhanced">高置信度</text>
          </view>
          <view class="indicator-item-enhanced">
            <view class="confidence-indicator-low"></view>
            <text class="indicator-text-enhanced">低置信度</text>
          </view>
        </view>

        <!-- 数据统计 -->
        <view class="chart-statistics-enhanced">
          <view class="stat-item-enhanced">
            <text class="stat-label-enhanced">平均值</text>
            <text class="stat-value-enhanced">{{ predictionStats.average }}</text>
          </view>
          <view class="stat-item-enhanced">
            <text class="stat-label-enhanced">最大值</text>
            <text class="stat-value-enhanced">{{ predictionStats.max }}</text>
          </view>
          <view class="stat-item-enhanced">
            <text class="stat-label-enhanced">最小值</text>
            <text class="stat-value-enhanced">{{ predictionStats.min }}</text>
          </view>
          <view class="stat-item-enhanced">
            <text class="stat-label-enhanced">置信度</text>
            <text class="stat-value-enhanced">{{ predictionStats.confidence }}</text>
          </view>
        </view>
      </view>
    </view>

    <!-- 最新记录 - 参考仪表盘设计 -->
    <view class="card-container animate-slide-in" style="animation-delay: 0.6s;">
      <view class="history-section-redesigned">
        <view class="section-header-redesigned">
          <view class="section-title-section">
            <view class="section-icon">
              <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                <circle cx="12" cy="12" r="10"></circle>
                <polyline points="12,6 12,12 16,14"></polyline>
              </svg>
            </view>
            <view class="section-title-text">
              <text class="section-title-main">最新记录</text>
              <text class="section-subtitle-main">近期数据概览</text>
            </view>
          </view>
          <view class="view-all-btn" @tap="viewDetailedStats">
            <text class="view-all-text">查看全部</text>
            <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
              <path d="M9 18l6-6-6-6"></path>
            </svg>
          </view>
        </view>
        <view class="history-list-redesigned">
          <view
            v-for="(item, index) in recentHistory"
            :key="index"
            class="history-item-redesigned"
            :class="getStatusClass(item.doseRate)"
          >
            <view class="history-time-redesigned">
              <text class="time-redesigned">{{ formatTime(item.timestamp) }}</text>
              <view class="status-badge-redesigned" :class="getStatusClass(item.doseRate)">
                <text class="status-text-redesigned">{{ getStatusText(item.doseRate) }}</text>
              </view>
            </view>
            <view class="history-data-redesigned">
              <text class="dose-rate-redesigned">{{ item.doseRate.toFixed(2) }} μSv/h</text>
              <text class="cps-redesigned">{{ item.cps }} CPS</text>
            </view>
          </view>
        </view>
      </view>
    </view>

    <!-- 底部导航栏 - 固定在底部 -->
    <BottomNavigation currentPage="charts" />
  </view>

  <!-- 详细统计弹窗 - 移到container外部 -->
  <view v-if="showStatsModal" class="stats-modal-overlay" @tap="closeStatsModal">
    <view class="stats-modal-container" @tap.stop>
      <view class="stats-modal-header">
        <view class="modal-icon">
          <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
            <path d="M3 3v18h18"></path>
            <path d="M7 16l4-4 4 4 6-6"></path>
          </svg>
        </view>
        <text class="modal-title">详细统计</text>
        <view class="modal-close" @tap="closeStatsModal">
          <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
            <line x1="18" y1="6" x2="6" y2="18"></line>
            <line x1="6" y1="6" x2="18" y2="18"></line>
          </svg>
        </view>
      </view>

      <view class="stats-modal-content">
        <text class="modal-subtitle">当前数据统计</text>

        <view class="stats-grid">
          <view class="stat-item-modal">
            <view class="stat-icon-modal average">
              <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                <path d="M3 3v18h18"></path>
                <path d="M7 16l4-4 4 4 6-6"></path>
              </svg>
            </view>
            <view class="stat-info-modal">
              <text class="stat-label-modal">平均剂量率</text>
              <text class="stat-value-modal">{{ avgDoseRate.toFixed(2) }} μSv/h</text>
            </view>
          </view>

          <view class="stat-item-modal">
            <view class="stat-icon-modal max">
              <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                <path d="M7 17l10-10"></path>
                <path d="M17 7v10"></path>
              </svg>
            </view>
            <view class="stat-info-modal">
              <text class="stat-label-modal">最大值</text>
              <text class="stat-value-modal">{{ maxDoseRate.toFixed(2) }} μSv/h</text>
            </view>
          </view>

          <view class="stat-item-modal">
            <view class="stat-icon-modal min">
              <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                <path d="M17 7l-10 10"></path>
                <path d="M7 7v10"></path>
              </svg>
            </view>
            <view class="stat-info-modal">
              <text class="stat-label-modal">最小值</text>
              <text class="stat-value-modal">{{ minDoseRate.toFixed(2) }} μSv/h</text>
            </view>
          </view>

          <view class="stat-item-modal">
            <view class="stat-icon-modal count">
              <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                <path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z"></path>
                <polyline points="14,2 14,8 20,8"></polyline>
                <line x1="16" y1="13" x2="8" y2="13"></line>
                <line x1="16" y1="17" x2="8" y2="17"></line>
                <polyline points="10,9 9,9 8,9"></polyline>
              </svg>
            </view>
            <view class="stat-info-modal">
              <text class="stat-label-modal">数据点数</text>
              <text class="stat-value-modal">{{ radiationState.history.length }}</text>
            </view>
          </view>

          <view class="stat-item-modal">
            <view class="stat-icon-modal frequency">
              <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                <circle cx="12" cy="12" r="10"></circle>
                <polyline points="12,6 12,12 16,14"></polyline>
              </svg>
            </view>
            <view class="stat-info-modal">
              <text class="stat-label-modal">采样频率</text>
              <text class="stat-value-modal">{{ samplingRate }} 次/分钟</text>
            </view>
          </view>

          <view class="stat-item-modal">
            <view class="stat-icon-modal quality">
              <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                <path d="M12 22s8-4 8-10V5l-8-3-8 3v7c0 6 8 10 8 10z"></path>
                <path d="M9 12l2 2 4-4"></path>
              </svg>
            </view>
            <view class="stat-info-modal">
              <text class="stat-label-modal">数据质量</text>
              <text class="stat-value-modal">{{ dataQuality }}%</text>
            </view>
          </view>
        </view>
      </view>

      <view class="stats-modal-footer">
        <view class="modal-button primary" @tap="closeStatsModal">
          <text class="button-text">确定</text>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
import { ref, computed, onMounted, onUnmounted } from 'vue'
import { radiationState, deviceState } from '../../utils/dataStore.js'
import dataStore from '../../utils/dataStore.js'
import mqttService from '../../utils/mqttService.js'
import { RadiationPredictionModel } from '../../utils/predictionAlgorithm.js'
import ToastContainer from '../../components/ToastContainer.vue'
import BottomNavigation from '../../components/BottomNavigation.vue'
import TimeRangeSelector from '../../components/TimeRangeSelector.vue'
import AdvancedChart from '../../components/AdvancedChart.vue'
import toastManager from '../../utils/toastManager.js'

export default {
  name: 'Charts',
  components: {
    ToastContainer,
    BottomNavigation,
    TimeRangeSelector,
    AdvancedChart
  },
  setup() {
    const selectedTimeRange = ref('24h')
    const isLoading = ref(false)
    const refreshSuccess = ref(false)

    // 创建高级预测模型实例
    const predictionModel = new RadiationPredictionModel()
    const chartWidth = ref(300)
    const chartHeight = ref(300)  // 增加图表高度以匹配CSS
    const fabMenuOpen = ref(false)
    let chartContext = null
    let updateInterval = null

    // 动画数据
    const animatedDoseRate = ref(0)
    const animatedCps = ref(0)
    const animationDuration = 1000 // 1秒动画时长

    // 数值动画函数
    const animateValue = (from, to, duration, callback) => {
      const startTime = Date.now()
      const animate = () => {
        const elapsed = Date.now() - startTime
        const progress = Math.min(elapsed / duration, 1)

        // 使用缓动函数
        const easeOutCubic = 1 - Math.pow(1 - progress, 3)
        const currentValue = from + (to - from) * easeOutCubic

        callback(currentValue)

        if (progress < 1) {
          requestAnimationFrame(animate)
        }
      }
      animate()
    }

    const doseRateTrend = computed(() => {
      const history = radiationState.history.slice(0, 10)
      if (history.length < 2) return { icon: '➖', text: '无数据', class: 'neutral' }
      
      const current = history[0].doseRate
      const previous = history[1].doseRate
      const change = ((current - previous) / previous * 100)
      
      if (Math.abs(change) < 1) return { icon: '➖', text: '稳定', class: 'stable' }
      if (change > 0) return { icon: '📈', text: `+${change.toFixed(1)}%`, class: 'up' }
      return { icon: '📉', text: `${change.toFixed(1)}%`, class: 'down' }
    })

    const cpsTrend = computed(() => {
      const history = radiationState.history.slice(0, 10)
      if (history.length < 2) return { icon: '➖', text: '无数据', class: 'neutral' }
      
      const current = history[0].cps
      const previous = history[1].cps
      const change = ((current - previous) / previous * 100)
      
      if (Math.abs(change) < 1) return { icon: '➖', text: '稳定', class: 'stable' }
      if (change > 0) return { icon: '📈', text: `+${change.toFixed(1)}%`, class: 'up' }
      return { icon: '📉', text: `${change.toFixed(1)}%`, class: 'down' }
    })

    const todayStats = computed(() => {
      const today = new Date().toDateString()
      const todayData = radiationState.history.filter(item => 
        new Date(item.timestamp).toDateString() === today
      )

      if (todayData.length === 0) {
        return { avgDoseRate: 0, maxDoseRate: 0, totalDose: 0 }
      }

      const doseRates = todayData.map(item => item.doseRate)
      return {
        avgDoseRate: doseRates.reduce((sum, rate) => sum + rate, 0) / doseRates.length,
        maxDoseRate: Math.max(...doseRates),
        totalDose: radiationState.currentData.doseSum
      }
    })

    const todayAlerts = computed(() => {
      const today = new Date().toDateString()
      return radiationState.alerts.filter(alert => 
        new Date(alert.timestamp).toDateString() === today
      )
    })

    const recentHistory = computed(() => {
      return radiationState.history.slice(0, 5)
    })

    // 新增统计数据计算属性
    const currentTime = computed(() => {
      return new Date().toLocaleTimeString('zh-CN', {
        hour: '2-digit',
        minute: '2-digit'
      })
    })



    const dataQuality = computed(() => {
      const history = radiationState.history.slice(0, 100)
      if (history.length < 10) return 85

      // 计算数据稳定性
      const values = history.map(item => item.doseRate)
      const mean = values.reduce((sum, val) => sum + val, 0) / values.length
      const variance = values.reduce((sum, val) => sum + Math.pow(val - mean, 2), 0) / values.length
      const stability = Math.max(0, 100 - variance * 1000)

      return Math.round(Math.min(100, stability))
    })

    const samplingRate = computed(() => {
      const history = radiationState.history.slice(0, 60)
      return history.length || 60
    })

    const dataRange = computed(() => {
      const history = radiationState.history.slice(0, 100)
      if (history.length === 0) return '0.000'

      const values = history.map(item => item.doseRate)
      const min = Math.min(...values)
      const max = Math.max(...values)
      const range = max - min

      return range.toFixed(2)
    })

    const anomalyCount = computed(() => {
      const history = radiationState.history.slice(0, 100)
      if (history.length < 10) return 0

      const values = history.map(item => item.doseRate)
      const mean = values.reduce((sum, val) => sum + val, 0) / values.length
      const stdDev = Math.sqrt(values.reduce((sum, val) => sum + Math.pow(val - mean, 2), 0) / values.length)

      // 检测超过2个标准差的异常值
      const threshold = 2 * stdDev
      return values.filter(val => Math.abs(val - mean) > threshold).length
    })

    // 自定义时间范围状态
    const customTimeRange = ref({
      startTime: null,
      endTime: null
    })

    // 根据时间范围过滤数据的函数
    const getFilteredData = (timeRange) => {
      const now = Date.now()
      let startTime = now
      let endTime = now

      if (timeRange === 'custom' && customTimeRange.value.startTime && customTimeRange.value.endTime) {
        // 自定义时间范围
        startTime = customTimeRange.value.startTime.getTime()
        endTime = customTimeRange.value.endTime.getTime()
      } else {
        // 预设时间范围
        switch (timeRange) {
          case '1h':
            startTime = now - 1 * 60 * 60 * 1000
            break
          case '6h':
            startTime = now - 6 * 60 * 60 * 1000
            break
          case '24h':
            startTime = now - 24 * 60 * 60 * 1000
            break
          case '7d':
            startTime = now - 7 * 24 * 60 * 60 * 1000
            break
          case '30d':
            startTime = now - 30 * 24 * 60 * 60 * 1000
            break
          default:
            startTime = now - 24 * 60 * 60 * 1000 // 默认24小时
        }
      }

      // 过滤指定时间范围内的数据
      return radiationState.history.filter(item =>
        item.timestamp >= startTime && item.timestamp <= endTime
      ).sort((a, b) => a.timestamp - b.timestamp) // 按时间排序
    }

    // 图表数据 - 根据选择的时间范围动态过滤
    const chartData = computed(() => {
      const filteredData = getFilteredData(selectedTimeRange.value)
      return filteredData.map(item => ({
        timestamp: item.timestamp,
        value: item.doseRate,
        doseRate: item.doseRate
      }))
    })

    const cpsChartData = computed(() => {
      const filteredData = getFilteredData(selectedTimeRange.value)
      return filteredData.map(item => ({
        timestamp: item.timestamp,
        value: item.cps,
        cps: item.cps
      }))
    })

    // 内容宽度 - 参考仪表盘实现
    const contentWidth = computed(() => {
      return Math.min(350, window.innerWidth - 32) // 减去左右边距
    })

    // 高级ARIMA+LSTM预测数据生成
    const predictionData = computed(() => {
      const history = radiationState.history.slice(-100) // 取最近100个数据点进行高级分析
      if (history.length < 10) return []

      try {
        // 初始化预测模型
        if (!predictionModel.modelState.isInitialized && history.length >= 10) {
          predictionModel.initializeModel(history)
        }

        // 生成高级预测结果
        const predictionResult = predictionModel.generateAdvancedPrediction(42) // 84小时，每2小时一个点

        // 转换为组件需要的格式
        const predictions = predictionResult.prediction.map((item, index) => ({
          timestamp: item.timestamp.toISOString(),
          value: item.doseRate,
          doseRate: item.doseRate,
          isPrediction: true,
          confidence: item.confidence,
          algorithm: 'ARIMA+LSTM',
          // 添加3%误差带信息
          errorBand: {
            upper: item.doseRate * 1.03,
            lower: item.doseRate * 0.97
          }
        }))

        // 添加误差带数据
        predictions.errorBands = {
          primary: predictionResult.errorBands.primary,
          confidence95: predictionResult.errorBands.confidence95,
          confidence68: predictionResult.errorBands.confidence68,
          environmental: predictionResult.errorBands.environmental
        }

        predictions.metadata = predictionResult.metadata

        return predictions
      } catch (error) {
        console.warn('高级预测模型失败，使用简化预测:', error)

        // 备用简化预测
        const latestValue = history[history.length - 1].doseRate
        const predictions = []

        for (let i = 0; i < 42; i++) {
          const timestamp = new Date(Date.now() + i * 2 * 60 * 60 * 1000)
          const timeHours = i * 2

          // 简化预测算法
          const trend = (Math.random() - 0.5) * 0.001
          const seasonal = Math.sin(timeHours * Math.PI / 12) * 0.005
          const noise = (Math.random() - 0.5) * 0.002

          const predictedValue = latestValue + trend + seasonal + noise

          predictions.push({
            timestamp: timestamp.toISOString(),
            value: Math.max(0.001, predictedValue),
            doseRate: Math.max(0.001, predictedValue),
            isPrediction: true,
            confidence: Math.max(0.4, 0.9 - timeHours * 0.01),
            algorithm: 'Simplified',
            errorBand: {
              upper: predictedValue * 1.03,
              lower: predictedValue * 0.97
            }
          })
        }

        return predictions
      }
    })

    // 预测统计数据
    const predictionStats = computed(() => {
      const data = predictionData.value
      if (data.length === 0) {
        return { average: '0.00', max: '0.00', min: '0.00', confidence: '0%' }
      }

      const values = data.map(item => item.value)
      const confidences = data.map(item => item.confidence || 0.8)

      const average = values.reduce((sum, val) => sum + val, 0) / values.length
      const max = Math.max(...values)
      const min = Math.min(...values)
      const avgConfidence = confidences.reduce((sum, val) => sum + val, 0) / confidences.length

      return {
        average: average.toFixed(2),
        max: max.toFixed(2),
        min: min.toFixed(2),
        confidence: (avgConfidence * 100).toFixed(0) + '%'
      }
    })

    // 方法
    const selectTimeRange = (range) => {
      selectedTimeRange.value = range
      drawChart()
    }

    const onRangeChange = (rangeInfo) => {
      console.log('时间范围变更:', rangeInfo)
      selectedTimeRange.value = rangeInfo.value

      // 根据时间范围类型处理数据
      if (rangeInfo.type === 'custom') {
        // 保存自定义时间范围
        customTimeRange.value = {
          startTime: rangeInfo.startTime,
          endTime: rangeInfo.endTime
        }

        console.log(`自定义时间范围: ${rangeInfo.startTime.toLocaleString()} - ${rangeInfo.endTime.toLocaleString()}`)
        toastManager.info(`已切换到自定义时间范围: ${rangeInfo.startTime.toLocaleDateString()} - ${rangeInfo.endTime.toLocaleDateString()}`)
      } else {
        // 清除自定义时间范围
        customTimeRange.value = {
          startTime: null,
          endTime: null
        }

        // 快速选择的处理逻辑
        const rangeLabels = {
          '1h': '1小时',
          '6h': '6小时',
          '24h': '24小时',
          '7d': '7天',
          '30d': '30天'
        }
        toastManager.info(`已切换到${rangeLabels[rangeInfo.value] || rangeInfo.value}数据`)
      }

      // 重新绘制图表
      drawChart()
    }

    const onPredictionReady = (predictionResult) => {
      console.log('预测结果已准备:', predictionResult)
      toastManager.success(`AI预测完成，置信度: ${(predictionResult.confidence * 100).toFixed(0)}%`)
    }

    const onPredictionError = (error) => {
      console.error('预测生成失败:', error)
      toastManager.error('AI预测功能暂时不可用，请稍后重试')
    }

    const refreshChart = () => {
      if (isLoading.value) return // 防止重复点击

      isLoading.value = true
      refreshSuccess.value = false

      // 触发震动反馈
      uni.vibrateShort()

      // 显示丰富的刷新提示
      toastManager.info('🔄 正在刷新数据...', {
        duration: 1500,
        showCountdown: true
      })

      // 生成更真实的模拟数据
      const generateRealisticData = () => {
        const baseValue = radiationState.currentData.doseRate
        const variation = (Math.random() - 0.5) * 0.02 // ±1%变化
        const trendFactor = Math.sin(Date.now() / 1000000) * 0.01 // 长期趋势

        return {
          doseRate: Math.max(0.05, baseValue + variation + trendFactor),
          cps: Math.max(30, 50 + Math.random() * 30 + Math.sin(Date.now() / 100000) * 10),
          doseSum: radiationState.currentData.doseSum + Math.random() * 0.002,
          alarmStatus: Math.random() > 0.98 ? 2 : 0,
          temperature: 25 + Math.sin(Date.now() / 500000) * 8 + (Math.random() - 0.5) * 3,
          timestamp: Date.now()
        }
      }

      // 分阶段更新数据，增加真实感
      const updateStages = [
        { delay: 300, message: '📡 连接设备中...' },
        { delay: 600, message: '📊 获取最新数据...' },
        { delay: 900, message: '🧮 AI分析处理中...' },
        { delay: 1200, message: '📈 更新预测模型...' }
      ]

      updateStages.forEach((stage, index) => {
        setTimeout(() => {
          toastManager.info(stage.message, {
            duration: 300,
            showCountdown: false
          })

          // 在最后阶段更新数据
          if (index === updateStages.length - 1) {
            const mockData = generateRealisticData()
            dataStore.updateRadiationData(mockData)

            // 更新历史数据
            radiationState.history.push(mockData)
            if (radiationState.history.length > 500) {
              radiationState.history = radiationState.history.slice(-500)
            }
          }
        }, stage.delay)
      })

      // 完成刷新
      setTimeout(() => {
        drawChart()
        drawPredictionChart()

        isLoading.value = false
        refreshSuccess.value = true

        // 显示成功动效
        toastManager.success('✨ 数据刷新完成！', {
          duration: 2000,
          showCountdown: false
        })

        // 重置成功状态
        setTimeout(() => {
          refreshSuccess.value = false
        }, 2000)
      }, 1500)
    }

    // 预测图表绘制
    const predictionChartContext = ref(null)

    const initPredictionChart = () => {
      const ctx = uni.createCanvasContext('predictionChart')
      if (!ctx) return

      predictionChartContext.value = ctx
      drawPredictionChart()
    }

    const drawPredictionChart = () => {
      if (!predictionChartContext.value) return

      const ctx = predictionChartContext.value

      // 获取画布实际尺寸
      uni.createSelectorQuery().select('.prediction-canvas').boundingClientRect((rect) => {
        if (!rect) return

        const width = rect.width || contentWidth.value
        const height = 280  // 从320改为280，因为图注移到了下方
        const padding = 40

        // 清空画布
        ctx.clearRect(0, 0, width, height)

        const data = predictionData.value
        if (data.length === 0) return

        const values = data.map(item => item.value)
        const maxValue = Math.max(...values)
        const minValue = Math.min(...values)
        const range = maxValue - minValue || 0.01

        // 绘制背景网格
        drawPredictionGrid(ctx, width, height, padding)

        // 绘制3%误差带（使用不同颜色）
        draw3PercentErrorBand(ctx, data, width, height, padding, minValue, range)

        // 绘制置信度误差带（基于ARIMA+LSTM预测置信度）
        drawAdvancedConfidenceErrorBand(ctx, data, width, height, padding, minValue, range)

        // 绘制平滑预测曲线
        drawSmoothPredictionCurve(ctx, data, width, height, padding, minValue, range)

        // 绘制坐标轴和标签
        drawPredictionAxes(ctx, data, width, height, padding, minValue, maxValue)

        ctx.draw()
      }).exec()
    }

    // 绘制预测图表网格
    const drawPredictionGrid = (ctx, width, height, padding) => {
      ctx.setStrokeStyle('rgba(0, 0, 0, 0.05)')
      ctx.setLineWidth(0.5)

      const chartWidth = width - padding * 2
      const chartHeight = height - padding * 2

      // 水平网格线
      for (let i = 1; i < 5; i++) {
        const y = padding + (i * chartHeight / 5)
        ctx.beginPath()
        ctx.moveTo(padding, y)
        ctx.lineTo(width - padding, y)
        ctx.stroke()
      }

      // 垂直网格线
      for (let i = 1; i < 6; i++) {
        const x = padding + (i * chartWidth / 6)
        ctx.beginPath()
        ctx.moveTo(x, padding)
        ctx.lineTo(x, height - padding)
        ctx.stroke()
      }
    }

    // 绘制3%误差带（使用橙色）
    const draw3PercentErrorBand = (ctx, data, width, height, padding, minValue, range) => {
      if (data.length < 2) return

      const chartWidth = width - padding * 2
      const chartHeight = height - padding * 2

      ctx.beginPath()

      // 上边界（3%误差）
      data.forEach((item, index) => {
        const x = padding + (index / (data.length - 1)) * chartWidth
        const upperValue = item.value * 1.03 // 3%上误差
        const upperY = height - padding - ((upperValue - minValue) / range) * chartHeight

        if (index === 0) {
          ctx.moveTo(x, upperY)
        } else {
          ctx.lineTo(x, upperY)
        }
      })

      // 下边界（反向）
      for (let i = data.length - 1; i >= 0; i--) {
        const x = padding + (i / (data.length - 1)) * chartWidth
        const lowerValue = data[i].value * 0.97 // 3%下误差
        const lowerY = height - padding - ((lowerValue - minValue) / range) * chartHeight
        ctx.lineTo(x, lowerY)
      }

      ctx.closePath()

      // 橙色半透明填充
      ctx.setFillStyle('rgba(245, 158, 11, 0.15)')
      ctx.fill()

      // 绘制边界线
      ctx.setStrokeStyle('rgba(245, 158, 11, 0.4)')
      ctx.setLineWidth(1)
      ctx.stroke()
    }

    // 绘制高级置信度误差带（基于ARIMA+LSTM）
    const drawAdvancedConfidenceErrorBand = (ctx, data, width, height, padding, minValue, range) => {
      if (data.length < 2) return

      const chartWidth = width - padding * 2
      const chartHeight = height - padding * 2

      // 多层置信度带
      const confidenceLevels = [
        { level: 0.95, alpha: 0.08, color: '59, 130, 246' }, // 蓝色 - 95%置信区间
        { level: 0.68, alpha: 0.12, color: '16, 185, 129' }  // 绿色 - 68%置信区间
      ]

      confidenceLevels.forEach(({ level, alpha, color }) => {
        ctx.beginPath()

        // 上边界
        data.forEach((item, index) => {
          const x = padding + (index / (data.length - 1)) * chartWidth
          const confidence = item.confidence || 0.8
          const errorMargin = (1 - confidence * level) * 0.05 + 0.02
          const upperValue = item.value * (1 + errorMargin)
          const upperY = height - padding - ((upperValue - minValue) / range) * chartHeight

          if (index === 0) {
            ctx.moveTo(x, upperY)
          } else {
            ctx.lineTo(x, upperY)
          }
        })

        // 下边界（反向）
        for (let i = data.length - 1; i >= 0; i--) {
          const x = padding + (i / (data.length - 1)) * chartWidth
          const confidence = data[i].confidence || 0.8
          const errorMargin = (1 - confidence * level) * 0.05 + 0.02
          const lowerValue = data[i].value * (1 - errorMargin)
          const lowerY = height - padding - ((lowerValue - minValue) / range) * chartHeight
          ctx.lineTo(x, lowerY)
        }

        ctx.closePath()
        ctx.setFillStyle(`rgba(${color}, ${alpha})`)
        ctx.fill()
      })
    }

    // 绘制平滑预测曲线（使用贝塞尔曲线）
    const drawSmoothPredictionCurve = (ctx, data, width, height, padding, minValue, range) => {
      if (data.length < 2) return

      const chartWidth = width - padding * 2
      const chartHeight = height - padding * 2

      // 计算控制点用于贝塞尔曲线
      const points = data.map((item, index) => ({
        x: padding + (index / (data.length - 1)) * chartWidth,
        y: height - padding - ((item.value - minValue) / range) * chartHeight,
        confidence: item.confidence || 0.8
      }))

      // 绘制渐变填充区域
      ctx.beginPath()
      ctx.moveTo(points[0].x, height - padding)

      // 使用贝塞尔曲线绘制平滑填充
      ctx.lineTo(points[0].x, points[0].y)

      for (let i = 1; i < points.length; i++) {
        const prev = points[i - 1]
        const curr = points[i]
        const next = points[i + 1] || curr

        // 计算控制点
        const cp1x = prev.x + (curr.x - prev.x) * 0.3
        const cp1y = prev.y
        const cp2x = curr.x - (next.x - curr.x) * 0.3
        const cp2y = curr.y

        ctx.bezierCurveTo(cp1x, cp1y, cp2x, cp2y, curr.x, curr.y)
      }

      ctx.lineTo(points[points.length - 1].x, height - padding)
      ctx.closePath()

      // 动态渐变填充（基于算法类型）
      const gradient = ctx.createLinearGradient(0, padding, 0, height - padding)
      gradient.addColorStop(0, 'rgba(16, 185, 129, 0.4)') // 绿色表示LSTM
      gradient.addColorStop(0.5, 'rgba(59, 130, 246, 0.3)') // 蓝色表示ARIMA
      gradient.addColorStop(1, 'rgba(16, 185, 129, 0.1)')
      ctx.setFillStyle(gradient)
      ctx.fill()

      // 绘制主预测线
      ctx.beginPath()
      ctx.setStrokeStyle('#10b981')
      ctx.setLineWidth(3)
      ctx.moveTo(points[0].x, points[0].y)

      for (let i = 1; i < points.length; i++) {
        const prev = points[i - 1]
        const curr = points[i]
        const next = points[i + 1] || curr

        const cp1x = prev.x + (curr.x - prev.x) * 0.3
        const cp1y = prev.y
        const cp2x = curr.x - (next.x - curr.x) * 0.3
        const cp2y = curr.y

        ctx.bezierCurveTo(cp1x, cp1y, cp2x, cp2y, curr.x, curr.y)
      }
      ctx.stroke()

      // 绘制置信度指示点
      points.forEach((point, index) => {
        if (index % 6 === 0) { // 每6个点显示一个置信度指示
          const confidence = point.confidence
          const radius = 2 + confidence * 2

          ctx.beginPath()
          ctx.setFillStyle('#ffffff')
          ctx.arc(point.x, point.y, radius + 1, 0, 2 * Math.PI)
          ctx.fill()

          ctx.beginPath()
          ctx.setFillStyle(`rgba(16, 185, 129, ${confidence})`)
          ctx.arc(point.x, point.y, radius, 0, 2 * Math.PI)
          ctx.fill()
        }
      })
    }

    // 原有的置信度误差带函数（保留兼容性）
    const drawConfidenceErrorBand = (ctx, data, width, height, padding, minValue, range) => {
      if (data.length < 2) return

      const chartWidth = width - padding * 2
      const chartHeight = height - padding * 2

      // 计算每个点的置信度误差带
      ctx.beginPath()

      // 上边界
      data.forEach((item, index) => {
        const x = padding + (index / (data.length - 1)) * chartWidth
        const confidence = item.confidence || 0.8
        const errorMargin = (1 - confidence) * 0.005 + 0.01 // 基础1%误差 + 置信度相关误差
        const upperValue = item.value * (1 + errorMargin)
        const upperY = height - padding - ((upperValue - minValue) / range) * chartHeight

        if (index === 0) {
          ctx.moveTo(x, upperY)
        } else {
          ctx.lineTo(x, upperY)
        }
      })

      // 下边界（反向）
      for (let i = data.length - 1; i >= 0; i--) {
        const x = padding + (i / (data.length - 1)) * chartWidth
        const confidence = data[i].confidence || 0.8
        const errorMargin = (1 - confidence) * 0.005 + 0.01
        const lowerValue = data[i].value * (1 - errorMargin)
        const lowerY = height - padding - ((lowerValue - minValue) / range) * chartHeight
        ctx.lineTo(x, lowerY)
      }

      ctx.closePath()

      // 动态渐变填充（基于平均置信度）
      const avgConfidence = data.reduce((sum, item) => sum + (item.confidence || 0.8), 0) / data.length
      const alpha = avgConfidence * 0.2

      const gradient = ctx.createLinearGradient(0, padding, 0, height - padding)
      gradient.addColorStop(0, `rgba(59, 130, 246, ${alpha})`)
      gradient.addColorStop(0.5, `rgba(59, 130, 246, ${alpha * 0.6})`)
      gradient.addColorStop(1, `rgba(59, 130, 246, ${alpha * 0.3})`)
      ctx.setFillStyle(gradient)
      ctx.fill()
    }

    // 绘制预测曲线
    const drawPredictionCurve = (ctx, data, width, height, padding, minValue, range) => {
      if (data.length < 2) return

      const chartWidth = width - padding * 2
      const chartHeight = height - padding * 2

      // 绘制渐变填充
      ctx.beginPath()
      ctx.moveTo(padding, height - padding)

      data.forEach((item, index) => {
        const x = padding + (index / (data.length - 1)) * chartWidth
        const y = height - padding - ((item.value - minValue) / range) * chartHeight

        if (index === 0) {
          ctx.lineTo(x, y)
        } else {
          ctx.lineTo(x, y)
        }
      })

      ctx.lineTo(width - padding, height - padding)
      ctx.closePath()

      // 渐变填充
      const fillGradient = ctx.createLinearGradient(0, padding, 0, height - padding)
      fillGradient.addColorStop(0, 'rgba(59, 130, 246, 0.3)')
      fillGradient.addColorStop(0.5, 'rgba(59, 130, 246, 0.15)')
      fillGradient.addColorStop(1, 'rgba(59, 130, 246, 0.05)')
      ctx.setFillStyle(fillGradient)
      ctx.fill()

      // 绘制主线条
      ctx.beginPath()
      ctx.setStrokeStyle('#3b82f6')
      ctx.setLineWidth(3)
      ctx.setLineCap('round')
      ctx.setLineJoin('round')

      data.forEach((item, index) => {
        const x = padding + (index / (data.length - 1)) * chartWidth
        const y = height - padding - ((item.value - minValue) / range) * chartHeight

        if (index === 0) {
          ctx.moveTo(x, y)
        } else {
          // 使用贝塞尔曲线创建平滑效果
          const prevX = padding + ((index - 1) / (data.length - 1)) * chartWidth
          const prevY = height - padding - ((data[index - 1].value - minValue) / range) * chartHeight
          const cpX = (prevX + x) / 2
          ctx.bezierCurveTo(cpX, prevY, cpX, y, x, y)
        }
      })

      ctx.stroke()

      // 绘制数据点（基于置信度调整显示）
      data.forEach((item, index) => {
        if (index % 6 === 0 || index === data.length - 1) { // 每6个点显示一个
          const x = padding + (index / (data.length - 1)) * chartWidth
          const y = height - padding - ((item.value - minValue) / range) * chartHeight
          const confidence = item.confidence || 0.8

          // 根据置信度调整点的大小和透明度（减小尺寸）
          const pointSize = 2 + confidence * 1.5  // 从4+3改为2+1.5
          const alpha = 0.6 + confidence * 0.4

          // 外圈（置信度环）
          ctx.beginPath()
          ctx.arc(x, y, pointSize + 1, 0, 2 * Math.PI)  // 从+2改为+1
          ctx.setFillStyle(`rgba(255, 255, 255, ${alpha})`)
          ctx.fill()
          ctx.setStrokeStyle(`rgba(59, 130, 246, ${alpha})`)
          ctx.setLineWidth(1)  // 从2改为1，减少线宽
          ctx.stroke()

          // 内圈（数据点）
          ctx.beginPath()
          ctx.arc(x, y, pointSize, 0, 2 * Math.PI)
          ctx.setFillStyle(`rgba(59, 130, 246, ${alpha})`)
          ctx.fill()

          // 置信度指示器（低置信度时显示警告色）
          if (confidence < 0.6) {
            ctx.beginPath()
            ctx.arc(x, y, pointSize * 0.5, 0, 2 * Math.PI)
            ctx.setFillStyle('rgba(245, 158, 11, 0.8)')
            ctx.fill()
          }
        }
      })
    }

    // 绘制预测图表坐标轴
    const drawPredictionAxes = (ctx, data, width, height, padding, minValue, maxValue) => {
      ctx.setStrokeStyle('#e5e7eb')
      ctx.setLineWidth(1)

      // X轴
      ctx.beginPath()
      ctx.moveTo(padding, height - padding)
      ctx.lineTo(width - padding, height - padding)
      ctx.stroke()

      // Y轴
      ctx.beginPath()
      ctx.moveTo(padding, padding)
      ctx.lineTo(padding, height - padding)
      ctx.stroke()

      // Y轴标签
      ctx.setFillStyle('#666')
      ctx.setFontSize(10)
      ctx.setTextAlign('right')

      const range = maxValue - minValue
      for (let i = 0; i <= 4; i++) {
        const value = minValue + (range * i / 4)
        const y = height - padding - (i * (height - 2 * padding) / 4)
        ctx.fillText(value.toFixed(2), padding - 5, y + 3)
      }

      // X轴时间标签
      ctx.setTextAlign('center')
      const timeLabels = Math.min(6, data.length)
      for (let i = 0; i < timeLabels; i++) {
        const dataIndex = Math.floor(i * (data.length - 1) / (timeLabels - 1))
        const item = data[dataIndex]
        const x = padding + (dataIndex / (data.length - 1)) * (width - 2 * padding)

        if (item && item.timestamp) {
          const time = new Date(item.timestamp)
          const timeStr = time.getHours().toString().padStart(2, '0') + ':' +
                         time.getMinutes().toString().padStart(2, '0')
          ctx.fillText(timeStr, x, height - padding + 15)
        }
      }
    }

    const exportChart = () => {
      uni.canvasToTempFilePath({
        canvasId: 'radiationChart',
        success: (res) => {
          uni.saveImageToPhotosAlbum({
            filePath: res.tempFilePath,
            success: () => {
              toastManager.success('图表已保存')
            }
          })
        }
      })
    }

    const exportData = () => {
      uni.showActionSheet({
        itemList: ['导出CSV数据', '导出JSON数据', '保存图表图片'],
        success: (res) => {
          switch (res.tapIndex) {
            case 0:
              exportCSV()
              break
            case 1:
              exportJSON()
              break
            case 2:
              exportChart()
              break
          }
        }
      })
    }

    const exportCSV = () => {
      const data = radiationState.history.slice(0, 100)
      let csvContent = '时间,剂量率(μSv/h),计数率(CPS),温度(°C)\n'
      
      data.forEach(item => {
        const time = new Date(item.timestamp).toLocaleString()
        csvContent += `${time},${item.doseRate},${item.cps},${item.temperature}\n`
      })
      
              toastManager.success('CSV数据已准备')
            }

    const exportJSON = () => {
      const exportData = {
        exportTime: new Date().toISOString(),
        totalRecords: radiationState.history.length,
        data: radiationState.history.slice(0, 100)
      }

      console.log('导出数据:', exportData)
      toastManager.success('JSON数据已准备')
    }

    const goBack = () => {
      uni.navigateBack()
    }

    // 统一的导航函数（支持tabBar与普通页面）
    const navigateTo = (page) => {
      const routes = {
        'dashboard': '/pages/dashboard/dashboard',
        'charts': '/pages/charts/charts',
        'health': '/pages/health/health',
        'map': '/pages/map/map',
        'settings': '/pages/settings/settings',
        'notification': '/pages/notification/notification'
      }

      if (routes[page]) {
        // 使用navigateTo替代switchTab，因为我们移除了tabBar配置
        uni.navigateTo({
          url: routes[page],
          fail: (err) => {
            console.error('导航失败:', err)
            uni.showToast({
              title: '页面跳转失败',
              icon: 'none'
            })
          }
        })
      }
    }

    const getAlertCount = (type) => {
      return todayAlerts.value.filter(alert => alert.type === type).length
    }

    const formatTime = (timestamp) => {
      const date = new Date(timestamp)
      return date.toLocaleTimeString('zh-CN', {
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit'
      })
    }

    const getStatusClass = (doseRate) => {
      if (doseRate > 1.0) return 'high'
      if (doseRate > 0.5) return 'warning'
      return 'normal'
    }

    const getStatusText = (doseRate) => {
      if (doseRate > 1.0) return '高'
      if (doseRate > 0.5) return '中'
      return '正常'
    }

    const initChart = () => {
      chartContext = uni.createCanvasContext('radiationChart')
      if (chartContext) {
        drawChart()
        // 设置定时更新
        updateInterval = setInterval(drawChart, 5000)
      }
    }

    const drawChart = () => {
      if (!chartContext) return
      
      const ctx = chartContext
      const width = chartWidth.value
      const height = chartHeight.value
      const padding = 40
      
      // 清空画布
      ctx.clearRect(0, 0, width, height)
      
      // 设置画布背景渐变
      const bgGradient = ctx.createLinearGradient(0, 0, 0, height)
      bgGradient.addColorStop(0, '#fefefe')
      bgGradient.addColorStop(1, '#f8fafc')
      ctx.setFillStyle(bgGradient)
      ctx.fillRect(0, 0, width, height)
      
      // 使用计算属性中的过滤数据
      const data = chartData.value.map(item => ({
        timestamp: item.timestamp,
        doseRate: item.doseRate
      }))
      if (data.length < 2) {
        // 绘制无数据提示
        ctx.setFillStyle('#64748b')
        ctx.setFontSize(16)
        ctx.setTextAlign('center')
        ctx.fillText('暂无数据', width / 2, height / 2)
        ctx.draw()
        return
      }
      
      // 绘制网格线 - 更美观的样式
      ctx.setStrokeStyle('rgba(226, 232, 240, 0.4)')
      ctx.setLineWidth(0.8)
      
      // 垂直网格线
      for (let i = 1; i < 10; i++) {  // 去掉边界线
        const x = padding + (i / 10) * (width - padding * 2)
        ctx.beginPath()
        ctx.moveTo(x, padding)
        ctx.lineTo(x, height - padding)
        ctx.stroke()
      }
      
      // 水平网格线
      for (let i = 1; i < 5; i++) {  // 去掉边界线
        const y = padding + (i / 5) * (height - padding * 2)
        ctx.beginPath()
        ctx.moveTo(padding, y)
        ctx.lineTo(width - padding, y)
        ctx.stroke()
      }
      
      // 绘制坐标轴
      ctx.setStrokeStyle('#64748b')
      ctx.setLineWidth(2)
      
      // X轴
      ctx.beginPath()
      ctx.moveTo(padding, height - padding)
      ctx.lineTo(width - padding, height - padding)
      ctx.stroke()
      
      // Y轴
      ctx.beginPath()
      ctx.moveTo(padding, padding)
      ctx.lineTo(padding, height - padding)
      ctx.stroke()
      
      // 绘制剂量率曲线
      const doseRates = data.map(item => item.doseRate)
      const maxDose = Math.max(...doseRates)
      const minDose = Math.min(...doseRates)
      const doseRange = (maxDose - minDose) * 1.2 || 1  // 增加显示范围
      const adjustedMinDose = minDose - (maxDose - minDose) * 0.1
      
      // 计算坐标点
      const dosePoints = data.map((item, index) => ({
        x: padding + (index / (data.length - 1)) * (width - padding * 2),
        y: height - padding - ((item.doseRate - adjustedMinDose) / doseRange) * (height - padding * 2)
      }))
      
      // 绘制剂量率渐变填充
      if (dosePoints.length > 1) {
        const doseGradient = ctx.createLinearGradient(0, padding, 0, height - padding)
        doseGradient.addColorStop(0, 'rgba(0, 180, 216, 0.4)')
        doseGradient.addColorStop(0.5, 'rgba(0, 180, 216, 0.2)')
        doseGradient.addColorStop(1, 'rgba(0, 180, 216, 0.05)')
        ctx.setFillStyle(doseGradient)
        
        ctx.beginPath()
        ctx.moveTo(dosePoints[0].x, height - padding)
        dosePoints.forEach((point, index) => {
        if (index === 0) {
            ctx.lineTo(point.x, point.y)
        } else {
            // 平滑曲线
            const prevPoint = dosePoints[index - 1]
            const cpx = (prevPoint.x + point.x) / 2
            ctx.quadraticCurveTo(cpx, prevPoint.y, point.x, point.y)
          }
        })
        ctx.lineTo(dosePoints[dosePoints.length - 1].x, height - padding)
        ctx.closePath()
        ctx.fill()
      }
      
      // 绘制剂量率曲线
      ctx.beginPath()
      ctx.setStrokeStyle('#00b4d8')
      ctx.setLineWidth(4)
      ctx.setShadow(0, 3, 8, 'rgba(0, 180, 216, 0.3)')
      
      dosePoints.forEach((point, index) => {
        if (index === 0) {
          ctx.moveTo(point.x, point.y)
        } else {
          // 平滑曲线
          const prevPoint = dosePoints[index - 1]
          const cpx = (prevPoint.x + point.x) / 2
          ctx.quadraticCurveTo(cpx, prevPoint.y, point.x, point.y)
        }
      })
      ctx.stroke()
      
      // 绘制计数率曲线（缩放显示）
      const cpsData = data.map(item => item.cps)
      const maxCps = Math.max(...cpsData)
      const minCps = Math.min(...cpsData)
      const cpsRange = (maxCps - minCps) * 1.2 || 1
      const adjustedMinCps = minCps - (maxCps - minCps) * 0.1
      
      // 计算CPS坐标点
      const cpsPoints = data.map((item, index) => ({
        x: padding + (index / (data.length - 1)) * (width - padding * 2),
        y: height - padding - ((item.cps - adjustedMinCps) / cpsRange) * (height - padding * 2) * 0.6  // 占60%高度
      }))
      
      // 绘制CPS渐变填充
      if (cpsPoints.length > 1) {
        const cpsGradient = ctx.createLinearGradient(0, height - padding, 0, height - padding - (height - padding * 2) * 0.6)
        cpsGradient.addColorStop(0, 'rgba(16, 185, 129, 0.3)')
        cpsGradient.addColorStop(1, 'rgba(16, 185, 129, 0.05)')
        ctx.setFillStyle(cpsGradient)
        
      ctx.beginPath()
        ctx.moveTo(cpsPoints[0].x, height - padding)
        cpsPoints.forEach((point, index) => {
        if (index === 0) {
            ctx.lineTo(point.x, point.y)
        } else {
            const prevPoint = cpsPoints[index - 1]
            const cpx = (prevPoint.x + point.x) / 2
            ctx.quadraticCurveTo(cpx, prevPoint.y, point.x, point.y)
          }
        })
        ctx.lineTo(cpsPoints[cpsPoints.length - 1].x, height - padding)
        ctx.closePath()
        ctx.fill()
      }
      
      // 绘制CPS曲线
      ctx.beginPath()
      ctx.setStrokeStyle('#10b981')
      ctx.setLineWidth(3)
      ctx.setShadow(0, 2, 6, 'rgba(16, 185, 129, 0.3)')
      
      cpsPoints.forEach((point, index) => {
        if (index === 0) {
          ctx.moveTo(point.x, point.y)
        } else {
          const prevPoint = cpsPoints[index - 1]
          const cpx = (prevPoint.x + point.x) / 2
          ctx.quadraticCurveTo(cpx, prevPoint.y, point.x, point.y)
        }
      })
      ctx.stroke()
      
      // 重置阴影
      ctx.setShadow(0, 0, 0, 'transparent')
      
      // 绘制数据点（仅显示关键点）
      const showPointInterval = Math.max(1, Math.floor(data.length / 20))  // 最多显示20个点
      dosePoints.forEach((point, index) => {
        if (index % showPointInterval === 0 || index === dosePoints.length - 1) {
          // 外圈
          ctx.beginPath()
          ctx.setFillStyle('#ffffff')
          ctx.arc(point.x, point.y, 3, 0, 2 * Math.PI)  // 从5改为3
          ctx.fill()
          // 内圈
          ctx.beginPath()
          ctx.setFillStyle('#00b4d8')
          ctx.arc(point.x, point.y, 2, 0, 2 * Math.PI)  // 从3改为2
          ctx.fill()
        }
      })
      
      // CPS数据点
      cpsPoints.forEach((point, index) => {
        if (index % showPointInterval === 0 || index === cpsPoints.length - 1) {
          ctx.beginPath()
          ctx.setFillStyle('#ffffff')
          ctx.arc(point.x, point.y, 3, 0, 2 * Math.PI)  // 从4改为3
          ctx.fill()
          ctx.beginPath()
          ctx.setFillStyle('#10b981')
          ctx.arc(point.x, point.y, 2, 0, 2 * Math.PI)  // 从2.5改为2
          ctx.fill()
        }
      })
      
      ctx.draw()
    }

    // 生成模拟历史数据
    const generateMockHistoryData = () => {
      const now = Date.now()
      const mockData = []

      // 生成过去7天的数据，每小时一个点
      for (let i = 7 * 24; i >= 0; i--) {
        const timestamp = now - i * 60 * 60 * 1000
        const baseValue = 0.1 + Math.sin(i * 0.1) * 0.02 // 基础值加上一些波动
        const randomVariation = (Math.random() - 0.5) * 0.01 // 随机变化

        mockData.push({
          doseRate: Math.max(0.05, baseValue + randomVariation),
          cps: 50 + Math.sin(i * 0.15) * 10 + (Math.random() - 0.5) * 5,
          doseSum: i * 0.001,
          alarmStatus: Math.random() > 0.98 ? 2 : 0,
          temperature: 25 + Math.sin(i * 0.2) * 3 + (Math.random() - 0.5) * 2,
          timestamp: timestamp
        })
      }

      // 将模拟数据添加到历史记录中
      radiationState.history = mockData
    }

    onMounted(() => {
      // 如果历史数据不足，生成模拟数据
      if (radiationState.history.length < 50) {
        generateMockHistoryData()
      }

      // 初始化动画数值
      animatedDoseRate.value = radiationState.currentData.doseRate
      animatedCps.value = radiationState.currentData.cps

      // 初始化MQTT连接
      if (!mqttService.isConnected) {
        mqttService.connect()
      }

      // 注册MQTT事件监听
      mqttService.onMessage('radiationData', (data) => {
        dataStore.updateRadiationData(data)
        // 重新绘制图表
        setTimeout(() => {
          drawChart()
          drawPredictionChart()
        }, 100)
      })

      mqttService.onMessage('deviceStatus', (data) => {
        dataStore.updateDeviceStatus(data)
      })

      mqttService.onMessage('connected', () => {
        deviceState.connection.mqtt = true
        toastManager.success('设备连接成功', {
          duration: 2000,
          showCountdown: false
        })
      })

      mqttService.onMessage('disconnected', () => {
        deviceState.connection.mqtt = false
        toastManager.warning('设备连接断开', {
          duration: 3000,
          showCountdown: true
        })
      })

      setTimeout(() => {
        initChart()
        initPredictionChart() // 初始化预测图表
      }, 500)
    })

      // 新增的交互方法
    const focusOnDoseRate = () => {
      toastManager.info('聚焦剂量率数据')
    }

    const focusOnCumulative = () => {
      toastManager.info('聚焦累积剂量')
    }

    const focusOnCPS = () => {
      toastManager.info('聚焦计数率数据')
    }

    const openChartSettings = () => {
      uni.showActionSheet({
        itemList: ['切换主题', '调整精度', '数据源设置'],
        success: (res) => {
          switch (res.tapIndex) {
            case 0:
              toastManager.info('主题切换')
              break
            case 1:
              toastManager.info('精度调整')
              break
            case 2:
              toastManager.info('数据源设置')
              break
          }
        }
      })
    }

    // 卡片交互函数
    const onDoseRateCardTap = () => {
      // 触发涟漪效果
      uni.vibrateShort()

      // 更新动画数值
      const currentValue = animatedDoseRate.value
      const targetValue = radiationState.currentData.doseRate
      animateValue(currentValue, targetValue, animationDuration, (value) => {
        animatedDoseRate.value = value
      })

      // 显示详细信息
      uni.showToast({
        title: `${radiationState.currentData.doseRate.toFixed(2)} μSv/h`,
        icon: 'none',
        duration: 2000
      })
    }

    const onCpsCardTap = () => {
      // 触发涟漪效果
      uni.vibrateShort()

      // 更新动画数值
      const currentValue = animatedCps.value
      const targetValue = radiationState.currentData.cps
      animateValue(currentValue, targetValue, animationDuration, (value) => {
        animatedCps.value = value
      })

      // 显示详细信息
      uni.showToast({
        title: `${radiationState.currentData.cps.toFixed(0)} CPS`,
        icon: 'none',
        duration: 2000
      })
    }

    const onStatCardTap = (type) => {
      uni.vibrateShort()

      let message = ''
      switch(type) {
        case 'quality':
          message = `质量: ${dataQuality.value}%`
          break
        case 'frequency':
          message = `频率: ${samplingRate.value}/分钟`
          break
        case 'range':
          message = `范围: ${dataRange.value}`
          break
        case 'anomaly':
          message = `异常: ${anomalyCount.value}个`
          break
        default:
          message = '统计信息'
      }

      uni.showToast({
        title: message,
        icon: 'none',
        duration: 2000
      })
    }

    const showStatsModal = ref(false)

    const viewDetailedStats = () => {
      showStatsModal.value = true
    }

    const closeStatsModal = () => {
      showStatsModal.value = false
    }

    const toggleFabMenu = () => {
      fabMenuOpen.value = !fabMenuOpen.value
    }

    const shareChart = () => {
      uni.share({
        provider: 'weixin',
        type: 1,
        title: '辐射监测数据图表',
        summary: '智能辐射监测系统生成的数据分析图表',
        success: () => {
          toastManager.success('分享成功')
        }
      })
    }

    const resetChart = () => {
      selectedTimeRange.value = '24h'
      drawChart()
      toastManager.success('视图已重置')
    }

    onUnmounted(() => {
      if (updateInterval) {
        clearInterval(updateInterval)
      }

      // 清理MQTT事件监听器
      mqttService.offMessage('radiationData')
      mqttService.offMessage('deviceStatus')
      mqttService.offMessage('connected')
      mqttService.offMessage('disconnected')
    })

    return {
      radiationState,
      selectedTimeRange,
      isLoading,
      refreshSuccess,
      chartWidth,
      chartHeight,
      doseRateTrend,
      cpsTrend,
      todayStats,
      todayAlerts,
      recentHistory,
      selectTimeRange,
      onRangeChange,
      onPredictionReady,
      onPredictionError,
      refreshChart,
      exportChart,
      exportData,
      getAlertCount,
      formatTime,
      getStatusClass,
      getStatusText,
      fabMenuOpen,
      focusOnDoseRate,
      focusOnCumulative,
      focusOnCPS,
      openChartSettings,
      viewDetailedStats,
      toggleFabMenu,
      shareChart,
      resetChart,
      maxDoseRate: computed(() => Math.max(...radiationState.history.slice(0, 50).map(item => item.doseRate))),
      minDoseRate: computed(() => Math.min(...radiationState.history.slice(0, 50).map(item => item.doseRate))),
      avgDoseRate: computed(() => {
        const data = radiationState.history.slice(0, 50)
        return data.reduce((sum, item) => sum + item.doseRate, 0) / data.length || 0
      }),
      changeRate: computed(() => {
        const recent = radiationState.history.slice(0, 10)
        if (recent.length < 2) return 0
        const current = recent[0].doseRate
        const previous = recent[recent.length - 1].doseRate
        return ((current - previous) / previous) * 100
      }),
      maxDoseProgress: computed(() => {
        const maxSafeLevel = 0.5 // 假设0.5 μSv/h为安全上限
        return Math.min((maxDoseRate.value / maxSafeLevel) * 100, 100)
      }),
      minDoseProgress: computed(() => {
        const maxSafeLevel = 0.5
        return Math.min((minDoseRate.value / maxSafeLevel) * 100, 100)
      }),
      avgDoseProgress: computed(() => {
        const maxSafeLevel = 0.5
        return Math.min((avgDoseRate.value / maxSafeLevel) * 100, 100)
      }),
      goBack,
      navigateTo,
      // 新增的计算属性
      currentTime,
      deviceState,
      dataQuality,
      samplingRate,
      dataRange,
      anomalyCount,
      // 图表相关
      chartData,
      cpsChartData,
      contentWidth,
      customTimeRange,
      // 预测图表相关
      predictionData,
      predictionStats,
      initPredictionChart,
      drawPredictionChart,
      draw3PercentErrorBand,
      drawAdvancedConfidenceErrorBand,
      drawSmoothPredictionCurve,
      // 动画相关
      animatedDoseRate,
      animatedCps,
      onDoseRateCardTap,
      onCpsCardTap,
      onStatCardTap,
      // 弹窗相关
      showStatsModal,
      closeStatsModal
    }
  }
}
</script>

<style scoped>
/* 全局容器样式 - 与地图页面保持一致的顶部间距 */
.container {
  width: 100%;
  min-height: 100vh;
  margin: 0;
  padding: 64px 0 120px 0; /* 顶部64px与地图页面一致 */
  display: flex;
  flex-direction: column;
  background: #ffffff;
  position: relative;
  box-sizing: border-box;
  font-family: -apple-system, BlinkMacSystemFont, 'SF Pro Display', sans-serif;
  overflow-x: hidden;
}

.container::before {
  content: '';
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background:
    radial-gradient(circle at 20% 20%, rgba(59, 130, 246, 0.03) 0%, transparent 50%),
    radial-gradient(circle at 80% 80%, rgba(16, 185, 129, 0.02) 0%, transparent 50%),
    linear-gradient(45deg, rgba(139, 92, 246, 0.01) 0%, transparent 50%),
    linear-gradient(-45deg, rgba(236, 72, 153, 0.01) 0%, transparent 50%);
  pointer-events: none;
  z-index: 0;
  animation: backgroundShift 20s ease-in-out infinite;
}

@keyframes backgroundShift {
  0%, 100% {
    background-position: 0% 0%, 100% 100%, 0% 100%, 100% 0%;
  }
  50% {
    background-position: 100% 100%, 0% 0%, 100% 0%, 0% 100%;
  }
}

.container > *:not(.bottom-navigation):not(.toast-container-wrapper) {
  position: relative;
  z-index: 1;
}

/* 统一卡片容器样式 - 完全参考仪表盘 */
.card-container {
  width: calc(100% - 32px);
  max-width: 600px;
  margin: 12px 16px;
  box-sizing: border-box;
}

/* 状态栏 */
.status-bar {
  height: 44px;
  background: transparent;
}

/* 现代化头部 - 与地图页面保持一致的顶部间距 */
.modern-header {
  position: relative;
  padding: 20px 0 24px;
  margin: 64px 0 20px;
  overflow: hidden;
}

.header-background {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
  border-radius: 0 0 32px 32px;
  border: 1px solid rgba(59, 130, 246, 0.2);
}

.header-gradient {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0.05) 100%);
}

.header-content {
  position: relative;
  z-index: 2;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 20px;
}

.header-left {
  display: flex;
  align-items: center;
  gap: 16px;
  flex: 1;
}

.app-logo {
  width: 48px;
  height: 48px;
  background: rgba(255, 255, 255, 0.15);
  border-radius: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.app-logo svg {
  width: 24px;
  height: 24px;
  color: #ffffff;
}

.header-info {
  flex: 1;
}

.app-title {
  font-size: 24px;
  font-weight: 700;
  color: #ffffff;
  margin-bottom: 2px;
  display: block;
  text-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.app-subtitle {
  font-size: 13px;
  color: rgba(255, 255, 255, 0.8);
  display: block;
  text-shadow: 0 1px 4px rgba(0, 0, 0, 0.1);
}

.header-right {
  display: flex;
  align-items: center;
  gap: 12px;
}

.status-indicators {
  display: flex;
  align-items: center;
  gap: 12px;
}

.connection-status {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  background: rgba(239, 68, 68, 0.8);
  position: relative;
  transition: all 0.3s ease;
}

.connection-status.connected {
  background: rgba(34, 197, 94, 0.8);
}

.status-dot {
  width: 100%;
  height: 100%;
  border-radius: 50%;
  background: inherit;
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
    transform: scale(1);
  }
  50% {
    opacity: 0.7;
    transform: scale(1.2);
  }
}

.refresh-button {
  width: 44px;
  height: 44px;
  border-radius: 14px;
  background: rgba(255, 255, 255, 0.15);
  backdrop-filter: blur(10px);
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.refresh-button:hover {
  background: rgba(255, 255, 255, 0.2);
  transform: translateY(-1px);
}

.refresh-button:active {
  background: rgba(255, 255, 255, 0.25);
  transform: translateY(0) scale(0.95);
}

.refresh-button svg {
  width: 20px;
  height: 20px;
  color: #ffffff;
  transition: transform 0.3s ease;
}

.refresh-button:active svg {
  transform: rotate(180deg);
}

/* 时间范围选择器 */
.time-range-selector {
  margin: 0 16px 20px;
  background: rgba(255, 255, 255, 0.95);
  border-radius: 16px;
  padding: 16px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}

.range-label {
  font-size: 14px;
  color: #64748b;
  margin-bottom: 12px;
  display: block;
}

.range-options {
  display: flex;
  gap: 8px;
}

.range-option {
  flex: 1;
  padding: 12px;
  background: #f8fafc;
  border-radius: 12px;
  text-align: center;
  transition: all 0.3s ease;
  border: 2px solid transparent;
}

.range-option.active {
  background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
  border-color: #1d4ed8;
  transform: translateY(-2px);
}

.range-option text {
  font-size: 14px;
  font-weight: 600;
  color: #64748b;
}

.range-option.active text {
  color: #ffffff;
}

/* 核心数据卡片 */
.data-cards {
  display: flex;
  gap: 12px;
  margin: 0 16px 20px;
}

.data-card {
  flex: 1;
  background: rgba(255, 255, 255, 0.95);
  border-radius: 20px;
  padding: 20px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(20px);
  position: relative;
  overflow: hidden;
}

.data-card.primary {
  background: linear-gradient(135deg, rgba(59, 130, 246, 0.1) 0%, rgba(29, 78, 216, 0.05) 100%);
  border: 1px solid rgba(59, 130, 246, 0.2);
}

.data-card.secondary {
  background: linear-gradient(135deg, rgba(16, 185, 129, 0.1) 0%, rgba(5, 150, 105, 0.05) 100%);
  border: 1px solid rgba(16, 185, 129, 0.2);
}

.card-header {
  display: flex;
  align-items: flex-start;
  gap: 12px;
  margin-bottom: 16px;
}

.card-icon {
  width: 40px;
  height: 40px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.card-icon.dose-rate {
  background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
}

.card-icon.cps {
  background: linear-gradient(135deg, #10b981 0%, #059669 100%);
}

.card-icon svg {
  width: 20px;
  height: 20px;
  color: #ffffff;
}

.card-title {
  flex: 1;
}

.card-title .title {
  display: block;
  font-size: 12px;
  color: #64748b;
  margin-bottom: 4px;
}

.card-title .value {
  display: block;
  font-size: 24px;
  font-weight: 700;
  color: #0f172a;
  margin-bottom: 2px;
}

.card-title .unit {
  font-size: 12px;
  color: #94a3b8;
}

.trend-indicator {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 6px 12px;
  background: rgba(248, 250, 252, 0.8);
  border-radius: 20px;
}

.trend-icon {
  font-size: 14px;
}

.trend-text {
  font-size: 12px;
  font-weight: 600;
}

.trend-text.up { color: #ef4444; }
.trend-text.down { color: #10b981; }
.trend-text.stable { color: #64748b; }

/* 数据卡片重新设计 - 参考仪表盘，确保对齐 */
.data-cards-redesigned {
  display: flex;
  gap: 12px;
  width: 100%;
  box-sizing: border-box;
}

.data-card-modern {
  flex: 1;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  border-radius: 24px;
  padding: 24px;
  border: 1px solid rgba(255, 255, 255, 0.2);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.08);
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  min-height: 180px;
}

.data-card-modern:hover {
  transform: translateY(-4px);
  box-shadow:
    0 16px 48px rgba(0, 0, 0, 0.12),
    0 0 0 1px rgba(255, 255, 255, 0.5),
    inset 0 1px 0 rgba(255, 255, 255, 0.9);
  border-color: rgba(255, 255, 255, 0.3);
}

.data-card-modern.primary:hover {
  box-shadow:
    0 16px 48px rgba(59, 130, 246, 0.15),
    0 0 0 1px rgba(59, 130, 246, 0.2),
    inset 0 1px 0 rgba(255, 255, 255, 0.9);
}

.data-card-modern.secondary:hover {
  box-shadow:
    0 16px 48px rgba(16, 185, 129, 0.15),
    0 0 0 1px rgba(16, 185, 129, 0.2),
    inset 0 1px 0 rgba(255, 255, 255, 0.9);
}

.data-card-modern::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, #3b82f6 0%, #1d4ed8 100%);
  border-radius: 24px 24px 0 0;
}

.data-card-modern.secondary::before {
  background: linear-gradient(90deg, #10b981 0%, #059669 100%);
}

.card-header-modern {
  display: flex;
  align-items: center;
  gap: 16px;
  margin-bottom: 20px;
}

.card-icon-modern {
  width: 52px;
  height: 52px;
  border-radius: 18px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
  position: relative;
}

.card-icon-modern.dose-rate {
  background: linear-gradient(135deg, rgba(59, 130, 246, 0.15) 0%, rgba(29, 78, 216, 0.08) 100%);
  border: 2px solid rgba(59, 130, 246, 0.2);
}

.card-icon-modern.cps {
  background: linear-gradient(135deg, rgba(16, 185, 129, 0.15) 0%, rgba(5, 150, 105, 0.08) 100%);
  border: 2px solid rgba(16, 185, 129, 0.2);
}

.card-icon-modern svg {
  width: 26px;
  height: 26px;
  color: #3b82f6;
  filter: drop-shadow(0 2px 4px rgba(59, 130, 246, 0.2));
}

.card-icon-modern.cps svg {
  color: #10b981;
  filter: drop-shadow(0 2px 4px rgba(16, 185, 129, 0.2));
}

.card-title-modern {
  flex: 1;
}

.title-modern {
  font-size: 15px;
  font-weight: 600;
  color: #374151;
  margin-bottom: 2px;
  display: block;
}

.subtitle-modern {
  font-size: 12px;
  color: #9ca3af;
  display: block;
}

.card-value-section {
  display: flex;
  align-items: baseline;
  gap: 8px;
  margin-bottom: 16px;
  min-height: 50px;
  justify-content: flex-start;
}

.value-modern {
  font-size: 32px;
  font-weight: 700;
  color: #111827;
  line-height: 1.2;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
  display: flex;
  align-items: center;
  min-height: 38px;
}

.unit-modern {
  font-size: 14px;
  color: #6b7280;
  font-weight: 600;
}

.trend-indicator-modern {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 14px;
  background: linear-gradient(135deg, rgba(248, 250, 252, 0.9) 0%, rgba(241, 245, 249, 0.9) 100%);
  border-radius: 14px;
  border: 1px solid rgba(226, 232, 240, 0.6);
  backdrop-filter: blur(10px);
  margin-top: auto;
}

.trend-icon-modern {
  font-size: 16px;
}

.trend-text-modern {
  font-size: 13px;
  font-weight: 600;
}

.trend-text-modern.up { color: #10b981; }
.trend-text-modern.down { color: #ef4444; }
.trend-text-modern.stable { color: #6b7280; }
.trend-text-modern.neutral { color: #9ca3af; }

/* 统计概览重新设计 - 2x2网格布局 */
.stats-overview-redesigned {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  border-radius: 24px;
  padding: 24px;
  border: 1px solid rgba(255, 255, 255, 0.2);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.08);
  position: relative;
  overflow: hidden;
}

.stats-overview-redesigned::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, #3b82f6 0%, #10b981 50%, #f59e0b 100%);
  border-radius: 24px 24px 0 0;
}

.stats-header-redesigned {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
}

.header-left-section {
  display: flex;
  align-items: center;
  gap: 16px;
  flex: 1;
}

.stats-icon {
  width: 48px;
  height: 48px;
  background: linear-gradient(135deg, rgba(59, 130, 246, 0.15) 0%, rgba(29, 78, 216, 0.08) 100%);
  border-radius: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 2px solid rgba(59, 130, 246, 0.2);
}

.stats-icon svg {
  width: 24px;
  height: 24px;
  color: #3b82f6;
}

.header-text-section {
  flex: 1;
}

.stats-title-redesigned {
  font-size: 20px;
  font-weight: 700;
  color: #111827;
  margin-bottom: 2px;
  display: block;
}

.stats-subtitle-redesigned {
  font-size: 13px;
  color: #6b7280;
  display: block;
}

.time-badge-redesigned {
  padding: 8px 16px;
  background: linear-gradient(135deg, #10b981 0%, #059669 100%);
  border-radius: 14px;
  box-shadow: 0 2px 8px rgba(16, 185, 129, 0.2);
}

.time-text-redesigned {
  font-size: 12px;
  color: white;
  font-weight: 600;
}

.stats-grid-2x2 {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 16px;
  width: 100%;
  box-sizing: border-box;
}

.stat-card-redesigned {
  background: linear-gradient(135deg, rgba(248, 250, 252, 0.9) 0%, rgba(241, 245, 249, 0.9) 100%);
  border-radius: 18px;
  padding: 20px;
  border: 1px solid rgba(226, 232, 240, 0.6);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
}

.stat-card-redesigned:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.08);
  border-color: rgba(59, 130, 246, 0.3);
}

.stat-header-redesigned {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 16px;
}

.stat-icon-redesigned {
  width: 40px;
  height: 40px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
}

.quality-icon {
  background: linear-gradient(135deg, rgba(34, 197, 94, 0.15) 0%, rgba(22, 163, 74, 0.08) 100%);
  border: 2px solid rgba(34, 197, 94, 0.2);
}

.frequency-icon {
  background: linear-gradient(135deg, rgba(59, 130, 246, 0.15) 0%, rgba(29, 78, 216, 0.08) 100%);
  border: 2px solid rgba(59, 130, 246, 0.2);
}

.range-icon {
  background: linear-gradient(135deg, rgba(245, 158, 11, 0.15) 0%, rgba(217, 119, 6, 0.08) 100%);
  border: 2px solid rgba(245, 158, 11, 0.2);
}

.anomaly-icon {
  background: linear-gradient(135deg, rgba(239, 68, 68, 0.15) 0%, rgba(220, 38, 38, 0.08) 100%);
  border: 2px solid rgba(239, 68, 68, 0.2);
}

.stat-icon-redesigned svg {
  width: 20px;
  height: 20px;
}

.quality-icon svg { color: #22c55e; }
.frequency-icon svg { color: #3b82f6; }
.range-icon svg { color: #f59e0b; }
.anomaly-icon svg { color: #ef4444; }

.stat-label-redesigned {
  font-size: 14px;
  font-weight: 600;
  color: #374151;
}

.stat-value-redesigned {
  font-size: 24px;
  font-weight: 700;
  color: #111827;
  margin: 8px 0 4px;
  display: block;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
}

.stat-description-redesigned {
  font-size: 12px;
  color: #6b7280;
  font-weight: 500;
}

.stats-header {
  margin-bottom: 20px;
}

.stats-title {
  display: block;
  font-size: 18px;
  font-weight: 700;
  color: #0f172a;
  margin-bottom: 4px;
}

.stats-subtitle {
  font-size: 14px;
  color: #64748b;
}

.stats-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 16px;
}

.stat-item {
  background: #f8fafc;
  border-radius: 12px;
  padding: 16px;
  border: 1px solid #e2e8f0;
}

.stat-label {
  display: block;
  font-size: 12px;
  color: #64748b;
  margin-bottom: 8px;
}

.stat-value {
  display: block;
  font-size: 20px;
  font-weight: 700;
  color: #0f172a;
  margin-bottom: 4px;
}

.stat-unit {
  font-size: 12px;
  color: #94a3b8;
  margin-bottom: 12px;
  display: block;
}

.stat-bar {
  height: 4px;
  background: #e2e8f0;
  border-radius: 2px;
  overflow: hidden;
}

.stat-fill {
  height: 100%;
  border-radius: 2px;
  transition: width 0.6s ease;
}

.stat-fill.max { background: linear-gradient(90deg, #ef4444, #dc2626); }
.stat-fill.avg { background: linear-gradient(90deg, #3b82f6, #1d4ed8); }
.stat-fill.min { background: linear-gradient(90deg, #10b981, #059669); }
.stat-fill.change { background: linear-gradient(90deg, #f59e0b, #d97706); }

/* 增强统计概览样式 */
.enhanced-stats-overview {
  margin: 0 16px 20px;
  background: rgba(255, 255, 255, 0.98);
  border-radius: 24px;
  padding: 24px;
  box-shadow: 0 12px 40px rgba(0, 0, 0, 0.08);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(226, 232, 240, 0.6);
  position: relative;
  overflow: hidden;
}

.enhanced-stats-overview::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, #667eea 0%, #764ba2 50%, #667eea 100%);
  border-radius: 24px 24px 0 0;
}

.stats-header-enhanced {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 24px;
}

.header-left {
  flex: 1;
}

.stats-title-enhanced {
  font-size: 22px;
  font-weight: 700;
  color: #0f172a;
  margin-bottom: 4px;
  display: block;
}

.stats-subtitle-enhanced {
  font-size: 14px;
  color: #64748b;
  display: block;
}

.header-right {
  display: flex;
  align-items: center;
}

.time-badge {
  background: linear-gradient(135deg, #10b981 0%, #059669 100%);
  padding: 6px 12px;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(16, 185, 129, 0.2);
}

.time-text {
  font-size: 12px;
  color: white;
  font-weight: 600;
}

.enhanced-stats-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 16px;
  margin-bottom: 20px;
}

.enhanced-stat-card {
  background: linear-gradient(135deg, #f8fafc 0%, #ffffff 100%);
  border-radius: 16px;
  padding: 18px;
  border: 1px solid rgba(226, 232, 240, 0.8);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
}

.enhanced-stat-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.12);
  border-color: rgba(102, 126, 234, 0.3);
}

.enhanced-stat-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 3px;
  border-radius: 16px 16px 0 0;
}

.max-card::before {
  background: linear-gradient(90deg, #ef4444 0%, #dc2626 100%);
}

.avg-card::before {
  background: linear-gradient(90deg, #3b82f6 0%, #1d4ed8 100%);
}

.min-card::before {
  background: linear-gradient(90deg, #10b981 0%, #059669 100%);
}

.change-card::before {
  background: linear-gradient(90deg, #f59e0b 0%, #d97706 100%);
}

.card-header-enhanced {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 12px;
}

.stat-icon {
  width: 36px;
  height: 36px;
  border-radius: 10px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 16px;
}

.max-icon {
  background: linear-gradient(135deg, rgba(239, 68, 68, 0.1) 0%, rgba(220, 38, 38, 0.05) 100%);
}

.avg-icon {
  background: linear-gradient(135deg, rgba(59, 130, 246, 0.1) 0%, rgba(29, 78, 216, 0.05) 100%);
}

.min-icon {
  background: linear-gradient(135deg, rgba(16, 185, 129, 0.1) 0%, rgba(5, 150, 105, 0.05) 100%);
}

.change-icon {
  background: linear-gradient(135deg, rgba(245, 158, 11, 0.1) 0%, rgba(217, 119, 6, 0.05) 100%);
}

.stat-info {
  flex: 1;
}

.stat-label-enhanced {
  font-size: 14px;
  font-weight: 600;
  color: #374151;
  display: block;
  margin-bottom: 2px;
}

.stat-sublabel {
  font-size: 11px;
  color: #9ca3af;
  display: block;
}

.stat-value-section {
  display: flex;
  align-items: baseline;
  gap: 6px;
  margin-bottom: 12px;
}

.stat-value-enhanced {
  font-size: 24px;
  font-weight: 700;
  color: #0f172a;
  line-height: 1;
}

.stat-value-enhanced.positive {
  color: #10b981;
}

.stat-value-enhanced.negative {
  color: #ef4444;
}

.stat-unit-enhanced {
  font-size: 12px;
  color: #6b7280;
  font-weight: 500;
}

.stat-progress-enhanced {
  margin-top: 8px;
}

.progress-track {
  width: 100%;
  height: 6px;
  background: rgba(226, 232, 240, 0.6);
  border-radius: 3px;
  overflow: hidden;
  margin-bottom: 6px;
}

.progress-fill {
  height: 100%;
  border-radius: 3px;
  transition: width 0.8s cubic-bezier(0.4, 0, 0.2, 1);
}

.max-fill {
  background: linear-gradient(90deg, #ef4444 0%, #dc2626 100%);
}

.avg-fill {
  background: linear-gradient(90deg, #3b82f6 0%, #1d4ed8 100%);
}

.min-fill {
  background: linear-gradient(90deg, #10b981 0%, #059669 100%);
}

.change-fill {
  background: linear-gradient(90deg, #f59e0b 0%, #d97706 100%);
}

.progress-text {
  font-size: 10px;
  color: #9ca3af;
  font-weight: 500;
}

.additional-stats {
  display: flex;
  justify-content: space-between;
  padding: 16px;
  background: linear-gradient(135deg, rgba(102, 126, 234, 0.03) 0%, rgba(118, 75, 162, 0.03) 100%);
  border-radius: 12px;
  border: 1px solid rgba(226, 232, 240, 0.5);
}

.additional-stat-item {
  text-align: center;
  flex: 1;
}

.additional-label {
  font-size: 11px;
  color: #6b7280;
  display: block;
  margin-bottom: 4px;
  font-weight: 500;
}

.additional-value {
  font-size: 14px;
  font-weight: 600;
  color: #374151;
  display: block;
}

.additional-value.safe {
  color: #10b981;
}

/* 响应式设计 - 增强统计卡片 */
@media screen and (max-width: 480px) {
  .enhanced-stats-overview {
    margin: 0 12px 16px;
    padding: 16px;
  }

  .stats-header-enhanced {
    flex-direction: column;
    gap: 12px;
    margin-bottom: 16px;
  }

  .enhanced-stats-grid {
    grid-template-columns: 1fr;
    gap: 12px;
    margin-bottom: 16px;
  }

  .enhanced-stat-card {
    padding: 14px;
  }

  .stat-value-enhanced {
    font-size: 20px;
  }

  .additional-stats {
    flex-direction: column;
    gap: 8px;
    text-align: left;
  }

  .additional-stat-item {
    text-align: left;
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

  .additional-label {
    margin-bottom: 0;
  }
}

/* 图表容器 */
.chart-container {
  margin: 0 16px 20px;
  background: rgba(255, 255, 255, 0.95);
  border-radius: 20px;
  padding: 20px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

.chart-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.chart-title {
  font-size: 18px;
  font-weight: 700;
  color: #0f172a;
}

.chart-legend {
  display: flex;
  gap: 16px;
}

.legend-item {
  display: flex;
  align-items: center;
  gap: 6px;
}

.legend-color {
  width: 12px;
  height: 12px;
  border-radius: 6px;
}

.legend-color.dose {
  background: linear-gradient(135deg, #3b82f6, #1d4ed8);
}

.legend-color.cps {
  background: linear-gradient(135deg, #10b981, #059669);
}

.legend-item text {
  font-size: 12px;
  color: #64748b;
}

.chart-wrapper {
  position: relative;
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 300px;
  background: #f8fafc;
  border-radius: 12px;
  overflow: hidden;
}

.chart-canvas {
  border-radius: 12px;
}

.chart-loading {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 12px;
}

.loading-spinner {
  width: 32px;
  height: 32px;
  border: 3px solid #e2e8f0;
  border-top: 3px solid #3b82f6;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.chart-loading text {
  font-size: 14px;
  color: #64748b;
}

/* 图表容器重新设计 - 参考仪表盘波形样式 */
.charts-container-optimized {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  border-radius: 24px;
  padding: 24px;
  border: 1px solid rgba(255, 255, 255, 0.2);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.08);
  position: relative;
  overflow: hidden;
}

.charts-container-optimized::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, #3b82f6 0%, #1d4ed8 50%, #3b82f6 100%);
  border-radius: 24px 24px 0 0;
}

.chart-card-redesigned {
  position: relative;
}

.chart-header-redesigned {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
}

.chart-title-section {
  display: flex;
  align-items: center;
  gap: 16px;
  flex: 1;
}

.chart-icon {
  width: 48px;
  height: 48px;
  background: linear-gradient(135deg, rgba(59, 130, 246, 0.15) 0%, rgba(29, 78, 216, 0.08) 100%);
  border-radius: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 2px solid rgba(59, 130, 246, 0.2);
}

.chart-icon svg {
  width: 24px;
  height: 24px;
  color: #3b82f6;
}

.chart-title-text {
  flex: 1;
}

.chart-title-main {
  font-size: 20px;
  font-weight: 700;
  color: #111827;
  margin-bottom: 2px;
  display: block;
}

.chart-subtitle-main {
  font-size: 13px;
  color: #6b7280;
  display: block;
}

.chart-controls {
  display: flex;
  align-items: center;
}

.chart-legend {
  display: flex;
  gap: 16px;
  padding: 8px 16px;
  background: linear-gradient(135deg, rgba(248, 250, 252, 0.9) 0%, rgba(241, 245, 249, 0.9) 100%);
  border-radius: 14px;
  border: 1px solid rgba(226, 232, 240, 0.6);
}

.legend-item {
  display: flex;
  align-items: center;
  gap: 8px;
}

.legend-color {
  width: 16px;
  height: 4px;
  border-radius: 2px;
}

.legend-color.dose {
  background: linear-gradient(90deg, #3b82f6 0%, #1d4ed8 100%);
}

.legend-color.cps {
  background: linear-gradient(90deg, #10b981 0%, #059669 100%);
}

.legend-text {
  font-size: 12px;
  color: #6b7280;
  font-weight: 600;
}

.chart-wrapper-redesigned {
  position: relative;
  border-radius: 16px;
  overflow: hidden;
  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
  border: 1px solid rgba(226, 232, 240, 0.6);
  padding: 16px;
}

.chart-canvas-redesigned {
  display: block;
  width: 100%;
  height: 100%;
  border-radius: 12px;
}

.chart-loading-redesigned {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 12px;
  background: rgba(255, 255, 255, 0.95);
  padding: 24px;
  border-radius: 16px;
  border: 1px solid rgba(226, 232, 240, 0.6);
  backdrop-filter: blur(10px);
}

.loading-spinner-redesigned {
  width: 28px;
  height: 28px;
  border: 3px solid rgba(59, 130, 246, 0.2);
  border-top: 3px solid #3b82f6;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

.loading-text-redesigned {
  font-size: 14px;
  color: #6b7280;
  font-weight: 600;
}

/* 头部卡片样式 - 圆角矩形 */
.header-card-modern {
  background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
  border-radius: 24px;
  padding: 20px 24px;
  box-shadow:
    0 8px 32px rgba(59, 130, 246, 0.25),
    0 4px 16px rgba(59, 130, 246, 0.15);
  border: 1px solid rgba(255, 255, 255, 0.2);
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
}

.header-content-card {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.header-left {
  display: flex;
  align-items: center;
  gap: 16px;
}

.app-icon-container-card {
  width: 48px;
  height: 48px;
  background: rgba(255, 255, 255, 0.15);
  border-radius: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.app-icon-container-card svg {
  width: 24px;
  height: 24px;
  color: #ffffff;
}

.header-text-card {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.app-title-card {
  font-size: 20px;
  font-weight: 700;
  color: #ffffff;
  line-height: 1.2;
}

.app-subtitle-card {
  font-size: 14px;
  color: rgba(255, 255, 255, 0.8);
  font-weight: 500;
}

.header-right {
  display: flex;
  align-items: center;
  gap: 12px;
}

.status-indicator-card {
  width: 40px;
  height: 40px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 1px solid rgba(255, 255, 255, 0.15);
}

.status-indicator-card.active {
  background: rgba(16, 185, 129, 0.2);
  border-color: rgba(16, 185, 129, 0.3);
}

.status-dot-card {
  width: 8px;
  height: 8px;
  background: rgba(255, 255, 255, 0.6);
  border-radius: 50%;
}

.status-indicator-card.active .status-dot-card {
  background: #10b981;
  box-shadow: 0 0 8px rgba(16, 185, 129, 0.5);
}

.refresh-button-card {
  width: 40px;
  height: 40px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 1px solid rgba(255, 255, 255, 0.15);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  cursor: pointer;
  position: relative;
  overflow: hidden;
}

.refresh-button-card::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 0;
  height: 0;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 50%;
  transform: translate(-50%, -50%);
  transition: all 0.6s ease;
}

.refresh-button-card:hover {
  background: rgba(255, 255, 255, 0.2);
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.refresh-button-card:hover::before {
  width: 60px;
  height: 60px;
}

.refresh-button-card:active {
  transform: translateY(0) scale(0.95);
}

/* 刷新中状态 */
.refresh-button-card.refreshing {
  background: linear-gradient(135deg, rgba(16, 185, 129, 0.2), rgba(59, 130, 246, 0.2));
  border-color: rgba(16, 185, 129, 0.3);
  animation: pulse-refresh 1.5s infinite;
}

.refresh-button-card.refreshing svg {
  animation: spin-refresh 1s linear infinite;
  color: #10b981;
}

/* 成功状态 */
.refresh-button-card.success {
  background: linear-gradient(135deg, rgba(16, 185, 129, 0.3), rgba(34, 197, 94, 0.2));
  border-color: rgba(16, 185, 129, 0.5);
  animation: success-pulse 0.6s ease-out;
}

.refresh-button-card.success svg {
  color: #10b981;
  animation: success-bounce 0.6s ease-out;
}

.refresh-button-card:hover svg {
  animation: rotate 0.5s ease-in-out;
}

.refresh-button-card svg {
  width: 20px;
  height: 20px;
  color: #ffffff;
  transition: all 0.3s ease;
}

/* 动画定义 */
@keyframes spin-refresh {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

@keyframes pulse-refresh {
  0%, 100% {
    box-shadow: 0 0 0 0 rgba(16, 185, 129, 0.4);
    transform: scale(1);
  }
  50% {
    box-shadow: 0 0 0 10px rgba(16, 185, 129, 0);
    transform: scale(1.05);
  }
}

@keyframes success-pulse {
  0% {
    transform: scale(1);
    box-shadow: 0 0 0 0 rgba(16, 185, 129, 0.6);
  }
  50% {
    transform: scale(1.1);
    box-shadow: 0 0 0 15px rgba(16, 185, 129, 0);
  }
  100% {
    transform: scale(1);
    box-shadow: 0 0 0 0 rgba(16, 185, 129, 0);
  }
}

@keyframes success-bounce {
  0%, 20%, 50%, 80%, 100% {
    transform: translateY(0);
  }
  40% {
    transform: translateY(-3px);
  }
  60% {
    transform: translateY(-1px);
  }
}

/* 预测图表容器样式 */
.prediction-chart-container {
  background: linear-gradient(135deg,
    rgba(255, 255, 255, 0.95) 0%,
    rgba(248, 250, 252, 0.9) 100%);
  border-radius: 24px;
  padding: 24px;
  box-shadow:
    0 8px 32px rgba(0, 0, 0, 0.08),
    0 4px 16px rgba(0, 0, 0, 0.04),
    inset 0 1px 0 rgba(255, 255, 255, 0.8);
  border: 1px solid rgba(226, 232, 240, 0.5);
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
}

/* 图注底部样式 */
.chart-legend-bottom {
  display: flex;
  justify-content: center;
  gap: 20px;
  margin-top: 16px;
  padding: 12px 16px;
  background: linear-gradient(135deg, rgba(248, 250, 252, 0.8) 0%, rgba(241, 245, 249, 0.8) 100%);
  border-radius: 16px;
  border: 1px solid rgba(226, 232, 240, 0.4);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
}

.chart-header-enhanced {
  display: flex;
  align-items: center;
  gap: 16px;
  margin-bottom: 20px;
}

.chart-icon-wrapper-enhanced {
  width: 44px;
  height: 44px;
  background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
  border-radius: 14px;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
}

.chart-icon-wrapper-enhanced svg {
  width: 22px;
  height: 22px;
  color: #ffffff;
}

.chart-title-section-enhanced {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.chart-title-enhanced {
  font-size: 18px;
  font-weight: 700;
  color: #1f2937;
  line-height: 1.2;
}

.chart-subtitle-enhanced {
  font-size: 14px;
  color: #6b7280;
  font-weight: 500;
}

.chart-canvas-wrapper {
  position: relative;
  margin-bottom: 20px;
  padding: 16px;
  background: linear-gradient(135deg,
    rgba(255, 255, 255, 0.9) 0%,
    rgba(248, 250, 252, 0.8) 100%);
  border-radius: 20px;
  border: 1px solid rgba(226, 232, 240, 0.3);
}

.prediction-canvas {
  width: 100%;
  border-radius: 16px;
  background: linear-gradient(135deg,
    rgba(255, 255, 255, 0.95) 0%,
    rgba(250, 251, 252, 0.9) 100%);
  box-shadow:
    inset 0 2px 8px rgba(0, 0, 0, 0.03),
    0 2px 8px rgba(0, 0, 0, 0.02);
}

.error-band-indicator-enhanced {
  position: absolute;
  bottom: 12px;
  right: 12px;
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  gap: 6px;
}

.indicator-item-enhanced {
  display: flex;
  align-items: center;
  gap: 4px;
  background: rgba(255, 255, 255, 0.9);
  padding: 4px 8px;
  border-radius: 8px;
  border: 1px solid rgba(226, 232, 240, 0.5);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
}

.indicator-color-enhanced {
  width: 12px;
  height: 8px;
  background: rgba(59, 130, 246, 0.3);
  border-radius: 4px;
  border: 1px solid rgba(59, 130, 246, 0.5);
}

.confidence-indicator-high {
  width: 8px;
  height: 8px;
  background: rgba(59, 130, 246, 0.8);
  border-radius: 50%;
  border: 1px solid rgba(59, 130, 246, 1);
}

.confidence-indicator-low {
  width: 8px;
  height: 8px;
  background: rgba(245, 158, 11, 0.8);
  border-radius: 50%;
  border: 1px solid rgba(245, 158, 11, 1);
}

.indicator-text-enhanced {
  font-size: 10px;
  color: #6b7280;
  font-weight: 500;
}

.chart-statistics-enhanced {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 16px;
}

.stat-item-enhanced {
  text-align: center;
  padding: 16px 12px;
  background: linear-gradient(135deg,
    rgba(255, 255, 255, 0.8) 0%,
    rgba(248, 250, 252, 0.6) 100%);
  border-radius: 16px;
  border: 1px solid rgba(226, 232, 240, 0.3);
}

.stat-label-enhanced {
  display: block;
  font-size: 12px;
  color: #6b7280;
  font-weight: 500;
  margin-bottom: 8px;
}

.stat-value-enhanced {
  display: block;
  font-size: 18px;
  font-weight: 700;
  color: #1f2937;
  line-height: 1.2;
}

/* 历史数据列表重新设计 - 参考仪表盘 */
.history-section-redesigned {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  border-radius: 24px;
  padding: 24px;
  border: 1px solid rgba(255, 255, 255, 0.2);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.08);
  position: relative;
  overflow: hidden;
}

.history-section-redesigned::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, #10b981 0%, #059669 50%, #10b981 100%);
  border-radius: 24px 24px 0 0;
}

.section-header-redesigned {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.section-title-section {
  display: flex;
  align-items: center;
  gap: 16px;
  flex: 1;
}

.section-icon {
  width: 48px;
  height: 48px;
  background: linear-gradient(135deg, rgba(16, 185, 129, 0.15) 0%, rgba(5, 150, 105, 0.08) 100%);
  border-radius: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 2px solid rgba(16, 185, 129, 0.2);
}

.section-icon svg {
  width: 24px;
  height: 24px;
  color: #10b981;
}

.section-title-text {
  flex: 1;
}

.section-title-main {
  font-size: 20px;
  font-weight: 700;
  color: #111827;
  margin-bottom: 2px;
  display: block;
}

.section-subtitle-main {
  font-size: 13px;
  color: #6b7280;
  display: block;
}

.view-all-btn {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 8px 16px;
  background: linear-gradient(135deg, rgba(59, 130, 246, 0.1) 0%, rgba(29, 78, 216, 0.05) 100%);
  border-radius: 14px;
  border: 1px solid rgba(59, 130, 246, 0.2);
  transition: all 0.3s ease;
}

.view-all-btn:hover {
  background: linear-gradient(135deg, rgba(59, 130, 246, 0.15) 0%, rgba(29, 78, 216, 0.08) 100%);
  transform: translateY(-1px);
}

.view-all-text {
  font-size: 13px;
  color: #3b82f6;
  font-weight: 600;
}

.view-all-btn svg {
  width: 16px;
  height: 16px;
  color: #3b82f6;
}

.history-list-redesigned {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.history-item-redesigned {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 20px;
  background: linear-gradient(135deg, rgba(248, 250, 252, 0.9) 0%, rgba(241, 245, 249, 0.9) 100%);
  border-radius: 16px;
  border: 1px solid rgba(226, 232, 240, 0.6);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.history-item-redesigned:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.08);
  border-color: rgba(59, 130, 246, 0.3);
}

.history-time-redesigned {
  display: flex;
  flex-direction: column;
  gap: 6px;
}

.time-redesigned {
  font-size: 14px;
  font-weight: 600;
  color: #374151;
}

.status-badge-redesigned {
  padding: 4px 10px;
  border-radius: 10px;
  font-size: 11px;
  font-weight: 600;
  text-align: center;
}

.status-badge-redesigned.normal {
  background: linear-gradient(135deg, rgba(34, 197, 94, 0.15) 0%, rgba(22, 163, 74, 0.08) 100%);
  color: #16a34a;
  border: 1px solid rgba(34, 197, 94, 0.2);
}

.status-badge-redesigned.warning {
  background: linear-gradient(135deg, rgba(245, 158, 11, 0.15) 0%, rgba(217, 119, 6, 0.08) 100%);
  color: #d97706;
  border: 1px solid rgba(245, 158, 11, 0.2);
}

.status-badge-redesigned.danger {
  background: linear-gradient(135deg, rgba(239, 68, 68, 0.15) 0%, rgba(220, 38, 38, 0.08) 100%);
  color: #dc2626;
  border: 1px solid rgba(239, 68, 68, 0.2);
}

.status-text-redesigned {
  font-size: 11px;
  font-weight: 600;
}

.history-data-redesigned {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  gap: 4px;
}

.dose-rate-redesigned {
  font-size: 16px;
  font-weight: 700;
  color: #111827;
}

.cps-redesigned {
  font-size: 12px;
  color: #6b7280;
  font-weight: 500;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.section-title {
  font-size: 18px;
  font-weight: 700;
  color: #0f172a;
}

.view-all {
  font-size: 14px;
  color: #3b82f6;
  font-weight: 600;
}

.history-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.history-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px;
  background: #f8fafc;
  border-radius: 12px;
  border: 1px solid #e2e8f0;
  border-left: 4px solid #e2e8f0;
}

.history-item.normal {
  border-left-color: #10b981;
}

.history-item.warning {
  border-left-color: #f59e0b;
}

.history-item.danger {
  border-left-color: #ef4444;
}

.history-time {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.history-time .time {
  font-size: 14px;
  color: #0f172a;
  font-weight: 600;
}

.history-time .status {
  font-size: 12px;
  padding: 2px 8px;
  border-radius: 12px;
  font-weight: 600;
}

.status.normal {
  background: rgba(16, 185, 129, 0.1);
  color: #10b981;
}

.status.warning {
  background: rgba(245, 158, 11, 0.1);
  color: #f59e0b;
}

.status.danger {
  background: rgba(239, 68, 68, 0.1);
  color: #ef4444;
}

.history-data {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  gap: 4px;
}

.history-data .dose-rate {
  font-size: 16px;
  font-weight: 700;
  color: #0f172a;
}

.history-data .cps {
  font-size: 12px;
  color: #64748b;
}

/* 浮动操作按钮 */
.fab-container {
  position: fixed;
  bottom: 140px;
  right: 20px;
  z-index: 100;
}

.fab {
  width: 56px;
  height: 56px;
  border-radius: 28px;
  background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
  box-shadow: 0 8px 32px rgba(59, 130, 246, 0.3);
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  transition: all 0.3s ease;
}

.fab.active {
  transform: rotate(45deg);
}

.fab-icon {
  font-size: 24px;
  color: #ffffff;
}

.fab-menu {
  position: absolute;
  bottom: 70px;
  right: 0;
  display: flex;
  flex-direction: column;
  gap: 12px;
  opacity: 0;
  transform: translateY(20px);
  transition: all 0.3s ease;
  pointer-events: none;
}

.fab-menu.open {
  opacity: 1;
  transform: translateY(0);
  pointer-events: auto;
}

.fab-item {
  display: flex;
  align-items: center;
  gap: 12px;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  padding: 12px 16px;
  border-radius: 28px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  transform: translateX(100px);
  animation: slideInRight 0.3s ease forwards;
}

.fab-text {
  font-size: 14px;
  color: #0f172a;
  font-weight: 600;
  white-space: nowrap;
}

.fab-item .fab-icon {
  font-size: 16px;
  color: #3b82f6;
}

@keyframes slideInRight {
  to {
    transform: translateX(0);
  }
}

/* 美观的动画效果 */
@keyframes slideInUp {
  0% {
    opacity: 0;
    transform: translateY(30px);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeInScale {
  0% {
    opacity: 0;
    transform: scale(0.95);
  }
  100% {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes bounceIn {
  0% {
    opacity: 0;
    transform: scale(0.3);
  }
  50% {
    opacity: 1;
    transform: scale(1.05);
  }
  70% {
    transform: scale(0.9);
  }
  100% {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes shimmer {
  0% {
    background-position: -200px 0;
  }
  100% {
    background-position: calc(200px + 100%) 0;
  }
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

@keyframes glow {
  0%, 100% {
    box-shadow: 0 0 5px rgba(59, 130, 246, 0.3);
  }
  50% {
    box-shadow: 0 0 20px rgba(59, 130, 246, 0.6);
  }
}

@keyframes rotate {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

@keyframes float {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-10px);
  }
}

/* 动画类 */
.animate-slide-in {
  animation: slideInUp 0.6s cubic-bezier(0.4, 0, 0.2, 1) both;
}

.animate-fade-scale {
  animation: fadeInScale 0.5s cubic-bezier(0.4, 0, 0.2, 1) both;
}

.animate-bounce-in {
  animation: bounceIn 0.8s cubic-bezier(0.68, -0.55, 0.265, 1.55) both;
}

.animate-shimmer {
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
  background-size: 200px 100%;
  animation: shimmer 2s infinite;
}

.animate-pulse {
  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

.animate-glow {
  animation: glow 2s ease-in-out infinite alternate;
}

.animate-rotate {
  animation: rotate 1s linear infinite;
}

.animate-float {
  animation: float 3s ease-in-out infinite;
}

/* 交互卡片样式 */
.interactive-card {
  cursor: pointer;
  position: relative;
  overflow: hidden;
}

.interactive-card:active {
  transform: scale(0.98);
  transition: transform 0.1s ease;
}

/* 涟漪效果 */
.card-ripple {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: radial-gradient(circle, rgba(255, 255, 255, 0.3) 0%, transparent 70%);
  opacity: 0;
  pointer-events: none;
  transition: opacity 0.3s ease;
}

.interactive-card:active .card-ripple {
  opacity: 1;
  animation: ripple 0.6s ease-out;
}

@keyframes ripple {
  0% {
    transform: scale(0);
    opacity: 1;
  }
  100% {
    transform: scale(1);
    opacity: 0;
  }
}

/* 脉冲图标 */
.pulse-icon {
  animation: iconPulse 2s ease-in-out infinite;
}

@keyframes iconPulse {
  0%, 100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.1);
  }
}

/* 数值计数动画 */
.counter-animation {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* 交互统计卡片 */
.interactive-stat-card {
  cursor: pointer;
  position: relative;
  overflow: hidden;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.interactive-stat-card:hover {
  transform: translateY(-2px) scale(1.02);
  box-shadow:
    0 8px 25px rgba(0, 0, 0, 0.1),
    0 0 0 1px rgba(255, 255, 255, 0.5);
}

.interactive-stat-card:active {
  transform: translateY(0) scale(0.98);
}

/* 统计卡片发光效果 */
.stat-card-glow {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(45deg, transparent 30%, rgba(255, 255, 255, 0.1) 50%, transparent 70%);
  opacity: 0;
  transition: opacity 0.3s ease;
  pointer-events: none;
}

.interactive-stat-card:hover .stat-card-glow {
  opacity: 1;
  animation: shimmer 1.5s ease-in-out;
}

/* 详细统计弹窗样式 */
.stats-modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  z-index: 10001;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20px;
  animation: fadeIn 0.3s ease;
  box-sizing: border-box;
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

.stats-modal-container {
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.95) 0%, rgba(248, 250, 252, 0.9) 100%);
  border-radius: 24px;
  max-width: 400px;
  width: 100%;
  max-height: 80vh;
  overflow: hidden;
  box-shadow:
    0 20px 60px rgba(0, 0, 0, 0.15),
    0 8px 32px rgba(0, 0, 0, 0.1),
    inset 0 1px 0 rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.3);
  animation: slideInUp 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  margin: auto;
}

@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(30px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

.stats-modal-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 24px 24px 16px;
  border-bottom: 1px solid rgba(226, 232, 240, 0.3);
}

.modal-icon {
  width: 32px;
  height: 32px;
  background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
}

.modal-icon svg {
  width: 18px;
  height: 18px;
}

.modal-title {
  font-size: 20px;
  font-weight: 700;
  color: #0f172a;
  flex: 1;
  margin-left: 12px;
}

.modal-close {
  width: 32px;
  height: 32px;
  background: rgba(148, 163, 184, 0.1);
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.2s ease;
}

.modal-close:hover {
  background: rgba(148, 163, 184, 0.2);
  transform: scale(1.05);
}

.modal-close svg {
  width: 16px;
  height: 16px;
  color: #64748b;
}

.stats-modal-content {
  padding: 20px 24px;
  max-height: 60vh;
  overflow-y: auto;
}

.modal-subtitle {
  font-size: 14px;
  color: #64748b;
  margin-bottom: 20px;
  font-weight: 600;
}

.stats-grid {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.stat-item-modal {
  display: flex;
  align-items: center;
  padding: 16px;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.8) 0%, rgba(248, 250, 252, 0.6) 100%);
  border-radius: 16px;
  border: 1px solid rgba(226, 232, 240, 0.4);
  transition: all 0.3s ease;
}

.stat-item-modal:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
}

.stat-icon-modal {
  width: 40px;
  height: 40px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 16px;
}

.stat-icon-modal svg {
  width: 20px;
  height: 20px;
  color: white;
}

.stat-icon-modal.average {
  background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
}

.stat-icon-modal.max {
  background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
}

.stat-icon-modal.min {
  background: linear-gradient(135deg, #10b981 0%, #059669 100%);
}

.stat-icon-modal.count {
  background: linear-gradient(135deg, #8b5cf6 0%, #7c3aed 100%);
}

.stat-icon-modal.frequency {
  background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
}

.stat-icon-modal.quality {
  background: linear-gradient(135deg, #06b6d4 0%, #0891b2 100%);
}

.stat-info-modal {
  flex: 1;
}

.stat-label-modal {
  font-size: 14px;
  color: #64748b;
  font-weight: 600;
  display: block;
  margin-bottom: 4px;
}

.stat-value-modal {
  font-size: 18px;
  font-weight: 700;
  color: #0f172a;
  display: block;
}

.stats-modal-footer {
  padding: 16px 24px 24px;
  border-top: 1px solid rgba(226, 232, 240, 0.3);
}

.modal-button {
  width: 100%;
  padding: 14px 24px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s ease;
  font-weight: 600;
}

.modal-button.primary {
  background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
  box-shadow: 0 4px 16px rgba(59, 130, 246, 0.3);
}

.modal-button.primary:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(59, 130, 246, 0.4);
}

.modal-button.primary:active {
  transform: translateY(0);
}

.button-text {
  color: white;
  font-size: 16px;
  font-weight: 600;
}

/* 移除重复的底部导航栏样式，使用BottomNavigation组件 */

/* 响应式优化 - 与地图页面保持一致的顶部间距 */
@media (max-width: 375px) {
  .container {
    padding: 60px 0 120px 0; /* 小屏幕顶部60px与地图页面一致 */
  }

  .data-cards {
    flex-direction: column;
  }
  
  .stats-grid {
    grid-template-columns: 1fr;
  }
  
  .range-options {
    flex-wrap: wrap;
  }
}

/* 页面动画 */
.charts-container > * {
  animation: fadeInUp 0.6s ease forwards;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.modern-header { animation-delay: 0.1s; }
.time-range-selector-container { animation-delay: 0.2s; }
.data-cards-redesigned { animation-delay: 0.3s; }
.stats-overview-redesigned { animation-delay: 0.4s; }
.charts-container-optimized { animation-delay: 0.5s; }
.history-section-redesigned { animation-delay: 0.6s; }

/* 全局响应式设计 - 完全参考仪表盘 */
@media screen and (max-width: 480px) {
  .container {
    padding: 60px 0 120px 0;
  }

  .card-container {
    margin: 12px 16px;
    width: calc(100% - 32px);
    max-width: 600px;
  }
}

@media screen and (min-width: 768px) {
  /* 平板和大屏幕适配 - 完全参考仪表盘 */
  .container {
    align-items: center;
    max-width: 800px;
    margin: 0 auto;
    padding: 64px 0 120px 0;
  }

  .card-container {
    max-width: 700px;
    margin: 12px auto;
    width: calc(100% - 64px);
  }

  .modern-header {
    padding: 16px 0 20px;
    margin-bottom: 16px;
  }

  .header-content {
    padding: 0 16px;
  }

  .app-title {
    font-size: 20px;
  }

  .data-cards-redesigned {
    gap: 8px;
  }

  .data-card-modern {
    padding: 18px;
    min-height: 160px;
  }

  .value-modern {
    font-size: 28px;
  }

  .stats-overview-redesigned {
    padding: 18px;
  }

  .stats-grid-2x2 {
    gap: 12px;
  }

  .stat-card-redesigned {
    padding: 16px;
  }

  .stat-value-redesigned {
    font-size: 20px;
  }

  .charts-container-optimized {
    gap: 16px;
  }

  .history-section-redesigned {
    padding: 18px;
  }

  .history-list-redesigned {
    gap: 10px;
  }

  .history-item-redesigned {
    padding: 14px 16px;
  }
}

/* 大屏幕优化 */
@media screen and (min-width: 768px) {
  .card-container {
    margin: 0 auto 20px;
    max-width: 700px;
  }

  .data-cards-redesigned {
    gap: 16px;
  }

  .stats-grid-2x2 {
    gap: 20px;
  }

  .charts-container-optimized {
    max-width: 700px;
    gap: 24px;
  }
}
</style>