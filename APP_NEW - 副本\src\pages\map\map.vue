<template>
  <scroll-view class="map-container" scroll-y="true" enable-back-to-top="true">
    <!-- 自定义Toast通知 -->
    <view v-if="toast.show" class="custom-toast" :class="toast.type">
      <view class="toast-content">
        <view class="toast-icon">
          <svg v-if="toast.type === 'success'" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
            <path d="M22 11.08V12a10 10 0 1 1-5.93-9.14"></path>
            <polyline points="22,4 12,14.01 9,11.01"></polyline>
          </svg>
          <svg v-else-if="toast.type === 'error'" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
            <circle cx="12" cy="12" r="10"></circle>
            <line x1="15" y1="9" x2="9" y2="15"></line>
            <line x1="9" y1="9" x2="15" y2="15"></line>
          </svg>
          <svg v-else xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
            <circle cx="12" cy="12" r="10"></circle>
            <line x1="12" y1="16" x2="12" y2="12"></line>
            <line x1="12" y1="8" x2="12.01" y2="8"></line>
          </svg>
        </view>
        <text class="toast-text">{{ toast.message }}</text>
      </view>
    </view>

    <!-- 状态栏 -->
    <view class="status-bar"></view>

    <!-- 圆角矩形头部卡片 - 参考数据页面风格 -->
    <view class="card-container animate-slide-in" style="animation-delay: 0.1s;">
      <view class="header-card-modern">
        <view class="header-content-card">
          <view class="header-left">
            <view class="app-icon-container-card">
              <!-- 地图导航图标 -->
              <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2.5" stroke-linecap="round" stroke-linejoin="round">
                <polygon points="3 6 9 1 15 6 21 1 21 18 15 23 9 18 3 23"></polygon>
                <line x1="9" y1="1" x2="9" y2="18"></line>
                <line x1="15" y1="6" x2="15" y2="23"></line>
              </svg>
            </view>
            <view class="header-text-card">
              <text class="app-title-card">位置监测中心</text>
              <text class="app-subtitle-card">{{ currentTime }} • 实时定位</text>
            </view>
          </view>
          <view class="header-right">
            <view class="status-indicator-card animate-pulse" :class="{ active: gpsState.isConnected }">
              <view class="status-dot-card animate-glow"></view>
            </view>
            <view class="refresh-button-card animate-fade-scale" :class="{ 'refreshing': isRefreshing, 'success': refreshSuccess }" style="animation-delay: 1.2s;" @tap="refreshLocation">
              <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                <path d="M3 12a9 9 0 0 1 9-9 9.75 9.75 0 0 1 6.74 2.74L21 8"></path>
                <path d="M21 3v5h-5"></path>
                <path d="M21 12a9 9 0 0 1-9 9 9.75 9.75 0 0 1-6.74-2.74L3 16"></path>
                <path d="M3 21v-5h5"></path>
              </svg>
            </view>
          </view>
        </view>
      </view>
    </view>

    <!-- EC800M 4G定位信息卡片 - 重新设计 -->
    <view class="card-container animate-slide-in" style="animation-delay: 0.2s;">
      <view class="gps-card-enhanced">
        <view class="gps-header-enhanced">
          <view class="gps-icon-enhanced">
            <!-- 卫星图标 -->
            <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
              <path d="M13 7 9 3 5 7l4 4"></path>
              <path d="m17 11 4 4-4 4-4-4"></path>
              <path d="m8 12 4 4 6-6-4-4Z"></path>
              <path d="m16 8 3-3"></path>
              <path d="M9 21a6 6 0 0 0-6-6"></path>
            </svg>
          </view>
          <view class="gps-title-enhanced">
            <text class="gps-main-title">EC800M 4G定位</text>
            <text class="gps-subtitle">{{ gpsState.status }} • {{ formatAccuracy(gpsState.accuracy) }}</text>
          </view>
        </view>

        <view class="gps-signal-display">
          <view class="signal-value-container">
            <text class="signal-value animate-bounce-in counter-animation" style="animation-delay: 0.8s;">{{ gpsState.signalQuality }}</text>
            <text class="signal-unit animate-fade-scale" style="animation-delay: 0.9s;">%</text>
          </view>
        </view>

        <view class="signal-status-indicator">
          <text class="signal-icon animate-float">📡</text>
          <text class="signal-status-text" :class="getSignalClass(gpsState.signalQuality)">{{ getSignalText(gpsState.signalQuality) }}</text>
        </view>

        <!-- GPS详细信息 -->
        <view class="gps-details-grid">
          <view class="gps-detail-item">
            <view class="detail-icon-enhanced satellite">
              <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                <circle cx="12" cy="12" r="10"></circle>
                <path d="M12 6v6l4 2"></path>
              </svg>
            </view>
            <view class="detail-content-enhanced">
              <text class="detail-label-enhanced">卫星数量</text>
              <text class="detail-value-enhanced">{{ gpsState.satelliteCount }}颗</text>
            </view>
          </view>

          <view class="gps-detail-item">
            <view class="detail-icon-enhanced accuracy">
              <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                <path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"></path>
              </svg>
            </view>
            <view class="detail-content-enhanced">
              <text class="detail-label-enhanced">定位精度</text>
              <text class="detail-value-enhanced">{{ gpsState.accuracy }}m</text>
            </view>
          </view>

          <view class="gps-detail-item">
            <view class="detail-icon-enhanced signal">
              <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                <path d="M5 12.55a11 11 0 0 1 14.08 0"></path>
                <path d="M1.42 9a16 16 0 0 1 21.16 0"></path>
                <path d="M8.53 16.11a6 6 0 0 1 6.95 0"></path>
                <path d="M12 20h.01"></path>
              </svg>
            </view>
            <view class="detail-content-enhanced">
              <text class="detail-label-enhanced">4G信号</text>
              <text class="detail-value-enhanced">{{ gpsState.cellularSignal }}dBm</text>
            </view>
          </view>

          <view class="gps-detail-item">
            <view class="detail-icon-enhanced location">
              <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                <path d="M2 3h6a4 4 0 0 1 4 4v14a3 3 0 0 0-3-3H2z"></path>
                <path d="M22 3h-6a4 4 0 0 0-4 4v14a3 3 0 0 1 3-3h7z"></path>
              </svg>
            </view>
            <view class="detail-content-enhanced">
              <text class="detail-label-enhanced">坐标位置</text>
              <text class="detail-value-enhanced">{{ formatCoordinate(currentLocation.latitude, currentLocation.longitude) }}</text>
            </view>
          </view>
        </view>

        <!-- 当前地址 -->
        <view class="address-section-enhanced">
          <text class="address-label-enhanced">当前地址</text>
          <text class="address-text-enhanced">{{ currentLocation.address }}</text>
        </view>
      </view>
    </view>

    <!-- 搜索栏 - 参考数据页面设计 -->
    <view class="card-container animate-slide-in" style="animation-delay: 0.5s;">
      <view class="search-card-modern">
        <view class="search-input-container">
          <svg class="search-icon" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
            <circle cx="11" cy="11" r="8"></circle>
            <path d="M21 21l-4.35-4.35"></path>
          </svg>
          <input
            type="text"
            class="search-input"
            placeholder="搜索地点或地址"
            v-model="searchQuery"
            @confirm="searchLocation"
            @input="onSearchInput"
          />
          <view class="search-clear" v-if="searchQuery" @tap="clearSearch">
            <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
              <line x1="18" y1="6" x2="6" y2="18"></line>
              <line x1="6" y1="6" x2="18" y2="18"></line>
            </svg>
          </view>
        </view>

        <!-- 搜索建议 -->
        <view class="search-suggestions" v-if="searchSuggestions.length > 0">
          <view
            class="suggestion-item"
            v-for="(suggestion, index) in searchSuggestions"
            :key="index"
            @tap="selectSuggestion(suggestion)"
          >
            <view class="suggestion-icon">
              <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                <path d="M21 10c0 7-9 13-9 13s-9-6-9-13a9 9 0 0 1 18 0z"></path>
                <circle cx="12" cy="10" r="3"></circle>
              </svg>
            </view>
            <view class="suggestion-content">
              <text class="suggestion-name">{{ suggestion.name }}</text>
              <text class="suggestion-address">{{ suggestion.address }}</text>
            </view>
          </view>
        </view>
      </view>
    </view>

    <!-- 地图容器 - 参考数据页面设计 -->
    <view class="card-container animate-slide-in" style="animation-delay: 0.6s;">
      <view class="map-card-modern">
        <view class="map-header-enhanced">
          <view class="map-icon-container">
            <!-- 地球图标 -->
            <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
              <circle cx="12" cy="12" r="10"></circle>
              <path d="M2 12h20"></path>
              <path d="M12 2a15.3 15.3 0 0 1 4 10 15.3 15.3 0 0 1-4 10 15.3 15.3 0 0 1-4-10 15.3 15.3 0 0 1 4-10z"></path>
            </svg>
          </view>
          <view class="map-title-section">
            <text class="map-title-enhanced">地图</text>
          </view>
          <view class="map-controls-enhanced">
            <view class="control-btn-enhanced" :class="{ active: mapType === 'satellite' }" @tap="toggleMapType">
              <text>卫星</text>
            </view>
            <view class="control-btn-enhanced" :class="{ active: showTraffic }" @tap="toggleTraffic">
              <text>路况</text>
            </view>
            <view class="control-btn-enhanced" :class="{ active: showRadiationOverlay }" @tap="toggleRadiationOverlay">
              <text>标记</text>
            </view>
          </view>
        </view>

        <view class="map-wrapper-enhanced">
          <!-- 地图组件 - 始终显示 -->
          <map
            id="map"
            class="baidu-map-container"
            :longitude="mapCenter.lng"
            :latitude="mapCenter.lat"
            :scale="mapZoom"
            :markers="allMarkers"
            :show-location="true"
            :enable-3D="false"
            :show-compass="false"
            :enable-overlooking="false"
            :enable-zoom="true"
            :enable-scroll="true"
            :enable-rotate="false"
            @markertap="onMarkerTap"
            @tap="onMapTap"
            @regionchange="onRegionChange"
          >
          </map>

          <!-- 定位加载状态覆盖层 -->
          <view v-if="locationState.isLocating" class="map-loading-overlay">
            <view class="loading-spinner"></view>
            <text class="loading-text">正在定位...</text>
          </view>

          <!-- 权限错误提示覆盖层 -->
          <view v-if="!locationState.hasPermission && !locationState.isLocating" class="map-permission-overlay">
            <view class="permission-icon">📍</view>
            <text class="permission-title">需要定位权限</text>
            <text class="permission-desc">点击授权以获取精确位置</text>
            <button class="permission-btn" @tap="handleLocationPermission">授权定位</button>
          </view>

          <!-- 地图覆盖层控件 - 增强动画 -->
          <view class="map-overlay-controls animate-slide-in-right" :style="{ animationDelay: '0.8s' }">
            <view class="control-group animate-scale-in" :style="{ animationDelay: '1.0s' }">
              <view class="zoom-btn animated-button interactive-card animate-bounce-in button-feedback"
                    :style="{ animationDelay: '1.1s' }"
                    @tap="enhancedZoomIn">
                <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                  <line x1="12" y1="5" x2="12" y2="19"></line>
                  <line x1="5" y1="12" x2="19" y2="12"></line>
                </svg>
              </view>
              <view class="zoom-btn animated-button interactive-card animate-bounce-in button-feedback"
                    :style="{ animationDelay: '1.2s' }"
                    @tap="enhancedZoomOut">
                <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                  <line x1="5" y1="12" x2="19" y2="12"></line>
                </svg>
              </view>
            </view>

            <view class="locate-btn animated-button interactive-card animate-bounce-in animate-pulse-glow button-feedback"
                  :style="{ animationDelay: '1.3s' }"
                  :class="{ 'animate-heartbeat': locationState.isLocating, 'animate-glow-pulse': gpsState.isConnected }"
                  @tap="enhancedCenterToCurrentLocation">
              <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                <path d="M21 10c0 7-9 13-9 13s-9-6-9-13a9 9 0 0 1 18 0z"></path>
                <circle cx="12" cy="10" r="3"></circle>
              </svg>
            </view>
          </view>

          <!-- 辐射超标警告标记 - 增强动画 -->
          <view class="radiation-alert-markers" v-if="showRadiationOverlay">
            <view
              v-for="(alert, index) in radiationAlerts"
              :key="alert.id"
              class="radiation-marker animate-bounce-in interactive-card"
              :class="[alert.level, 'animate-pulse-glow']"
              :style="{
                ...getMarkerPosition(alert),
                animationDelay: `${1.5 + index * 0.2}s`
              }"
              @tap="showAlertDetails(alert)"
            >
              <view class="marker-icon animate-rotate-in" :style="{ animationDelay: `${1.7 + index * 0.2}s` }">
                <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                  <circle cx="12" cy="12" r="10"></circle>
                  <circle cx="12" cy="12" r="6"></circle>
                  <circle cx="12" cy="12" r="2"></circle>
                </svg>
              </view>
              <view class="marker-pulse animate-heartbeat"></view>
              <view class="marker-ripple"></view>
            </view>
          </view>
        </view>
      </view>
    </view>



    <!-- 附近地点 - 参考数据页面设计 -->
    <view class="card-container animate-slide-in" style="animation-delay: 0.8s;">
      <view class="nearby-section-enhanced">
        <view class="section-header-enhanced">
          <view class="section-icon-container">
            <!-- 建筑物图标 -->
            <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
              <path d="M6 22V4a2 2 0 0 1 2-2h8a2 2 0 0 1 2 2v18Z"></path>
              <path d="M6 12H4a2 2 0 0 0-2 2v8h20v-8a2 2 0 0 0-2-2h-2"></path>
              <path d="M10 6h4"></path>
              <path d="M10 10h4"></path>
              <path d="M10 14h4"></path>
              <path d="M10 18h4"></path>
            </svg>
          </view>
          <view class="section-title-section">
            <text class="section-title-enhanced">附近地点</text>
          </view>
        </view>

        <view class="section-filter-enhanced animate-slide-in-bottom" :style="{ animationDelay: '1.0s' }">
          <view
            class="filter-tab-enhanced animated-button interactive-card animate-scale-in"
            :class="{ active: nearbyFilter === item.key }"
            v-for="(item, index) in nearbyFilters"
            :key="item.key"
            :style="{ animationDelay: `${1.1 + index * 0.1}s` }"
            @tap="setNearbyFilter(item.key)"
          >
            <text>{{ item.label }}</text>
          </view>
        </view>

        <view class="nearby-list-enhanced">
          <view
            class="nearby-item-enhanced interactive-card animate-slide-in-left animate-zoom-hover"
            v-for="(place, index) in filteredNearbyPlaces"
            :key="index"
            :style="{ animationDelay: `${1.3 + index * 0.1}s` }"
            @tap="navigateToPlace(place)"
          >
            <view class="place-icon-enhanced" :class="place.type">
              <text>{{ getPlaceIcon(place.type) }}</text>
            </view>
            <view class="place-info-enhanced">
              <text class="place-name-enhanced">{{ place.name }}</text>
              <text class="place-address-enhanced">{{ place.address }}</text>
              <view class="place-meta-enhanced">
                <view class="meta-item">
                  <text class="meta-icon">📍</text>
                  <text class="meta-text">{{ place.distance }}m</text>
                </view>
                <view class="meta-item">
                  <text class="meta-icon">🚶</text>
                  <text class="meta-text">{{ place.walkTime }}分钟</text>
                </view>
              </view>
            </view>
            <view class="place-actions-enhanced">
              <view class="action-btn-enhanced" @tap.stop="getDirections(place)">
                <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                  <path d="M3 3l3 9 9 3 9-9-9-9-9 3z"></path>
                  <path d="M6 12l6-6"></path>
                </svg>
              </view>
            </view>
          </view>
        </view>
      </view>
    </view>

    <!-- 自定义Toast通知 -->
    <view v-if="toast.show" class="custom-toast" :class="toast.type">
      <view class="toast-content">
        <view class="toast-icon">
          <!-- 成功图标 -->
          <svg v-if="toast.type === 'success'" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
            <path d="M22 11.08V12a10 10 0 1 1-5.93-9.14"></path>
            <polyline points="22,4 12,14.01 9,11.01"></polyline>
          </svg>
          <!-- 错误图标 -->
          <svg v-else-if="toast.type === 'error'" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
            <circle cx="12" cy="12" r="10"></circle>
            <line x1="15" y1="9" x2="9" y2="15"></line>
            <line x1="9" y1="9" x2="15" y2="15"></line>
          </svg>
          <!-- 信息图标 -->
          <svg v-else xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
            <circle cx="12" cy="12" r="10"></circle>
            <line x1="12" y1="16" x2="12" y2="12"></line>
            <line x1="12" y1="8" x2="12.01" y2="8"></line>
          </svg>
        </view>
        <text class="toast-text">{{ toast.message }}</text>
      </view>
    </view>

    <!-- 底部导航栏 -->
    <BottomNavigation currentPage="map" />
  </scroll-view>
</template>

<script setup>
import { ref, reactive, computed, onMounted, onUnmounted } from 'vue'
import { radiationState } from '../../utils/dataStore.js'
import {
  locationState,
  currentLocation,
  initLocationService,
  startLocationWatch,
  stopLocationWatch,
  getCurrentLocation,
  calculateDistance,
  formatCoordinate,
  cleanupLocationService,
  requestLocationPermission
} from '../../utils/locationService.js'
import {
  alertState,
  activeAlerts,
  alertMarkers,
  startAlertMonitoring,
  stopAlertMonitoring,
  acknowledgeAlert,
  clearAllAlerts
} from '../../utils/alertService.js'
import BottomNavigation from '../../components/BottomNavigation.vue'

// 响应式数据
const searchQuery = ref('')
const mapScale = ref(16)
const mapType = ref('standard')
const showTraffic = ref(false)
const showRadiationOverlay = ref(true)

// Toast通知状态
const toast = ref({
  show: false,
  message: '',
  type: 'info' // success, error, info
})

// 动画状态管理
const animationState = reactive({
  isPageLoaded: false,
  isMapReady: false,
  isLocationReady: false,
  showAnimations: true,
  animationSpeed: 1, // 动画速度倍数
  reducedMotion: false // 减少动画（无障碍功能）
})
const nearbyFilter = ref('all')
const isRefreshing = ref(false)
const refreshSuccess = ref(false)

// 地图中心点和缩放级别 - 基于当前位置
const mapCenter = computed(() => ({
  lng: currentLocation.value.longitude || 116.4074, // 默认北京坐标
  lat: currentLocation.value.latitude || 39.9042
}))
const mapZoom = ref(16)

// 当前时间
const currentTime = ref('')

// 移除重复的currentLocation定义，使用从locationService导入的

// EC800M 4G定位状态
const gpsState = ref({
  isConnected: true,
  status: '定位成功',
  accuracy: 5,
  satelliteCount: 12,
  signalQuality: 85,
  cellularSignal: -65,
  lastUpdate: Date.now()
})

// 辐射超标警告记录
const radiationAlerts = ref([
  {
    id: 1,
    latitude: 39.9052,
    longitude: 116.4084,
    level: 'warning',
    doseRate: 0.85,
    timestamp: Date.now() - 3600000,
    address: '北京市朝阳区建国门外大街2号'
  },
  {
    id: 2,
    latitude: 39.9032,
    longitude: 116.4064,
    level: 'danger',
    doseRate: 1.25,
    timestamp: Date.now() - 7200000,
    address: '北京市朝阳区建国门外大街3号'
  }
])

// 搜索建议
const searchSuggestions = ref([])

// 附近筛选选项
const nearbyFilters = ref([
  { key: 'all', label: '全部' },
  { key: 'restaurant', label: '餐厅' },
  { key: 'hospital', label: '医院' },
  { key: 'gas', label: '加油站' },
  { key: 'shopping', label: '购物' }
])

// 附近地点
const nearbyPlaces = ref([
  {
    name: '中关村医院',
    address: '北京市海淀区中关村南大街',
    type: 'hospital',
    distance: 1200,
    walkTime: 15,
    latitude: 39.9042,
    longitude: 116.4074
  },
  {
    name: '肯德基(中关村店)',
    address: '北京市海淀区中关村大街',
    type: 'restaurant',
    distance: 800,
    walkTime: 10,
    latitude: 39.9052,
    longitude: 116.4084
  },
  {
    name: '中石油加油站',
    address: '北京市海淀区知春路',
    type: 'gas',
    distance: 600,
    walkTime: 8,
    latitude: 39.9062,
    longitude: 116.4094
  },
  {
    name: '华联超市',
    address: '北京市朝阳区建国门外大街',
    type: 'shopping',
    distance: 450,
    walkTime: 6,
    latitude: 39.9038,
    longitude: 116.4058
  }
])

// 计算属性
const radiationLevel = computed(() => {
  const doseRate = radiationState.currentData.doseRate
  if (doseRate > 1.0) return 'danger'
  if (doseRate > 0.5) return 'warning'
  return 'safe'
})

const radiationLevelClass = computed(() => {
  return `level-${radiationLevel.value}`
})

const radiationStatusText = computed(() => {
  const level = radiationLevel.value
  if (level === 'danger') return '超标警告'
  if (level === 'warning') return '注意监控'
  return '安全正常'
})

const filteredNearbyPlaces = computed(() => {
  if (nearbyFilter.value === 'all') {
    return nearbyPlaces.value
  }
  return nearbyPlaces.value.filter(place => place.type === nearbyFilter.value)
})

// 地图标记
const mapMarkers = computed(() => {
  const markers = [
    {
      id: 1,
      latitude: currentLocation.value.latitude,
      longitude: currentLocation.value.longitude,
      title: '当前位置',
      iconPath: '/static/icons/location.png',
      width: 30,
      height: 30
    }
  ]

  // 添加附近地点标记
  nearbyPlaces.value.forEach((place, index) => {
    markers.push({
      id: index + 2,
      latitude: place.latitude,
      longitude: place.longitude,
      title: place.name,
      iconPath: `/static/icons/${place.type}.png`,
      width: 25,
      height: 25
    })
  })

  // 添加辐射超标警告标记
  if (showRadiationOverlay.value) {
    radiationAlerts.value.forEach((alert, index) => {
      markers.push({
        id: index + 100,
        latitude: alert.latitude,
        longitude: alert.longitude,
        title: `辐射超标 - ${alert.doseRate} μSv/h`,
        iconPath: `/static/icons/radiation-${alert.level}.png`,
        width: 35,
        height: 35
      })
    })
  }

  return markers
})

// 地图路线
const mapPolylines = ref([])

// 合并所有标记点
const allMarkers = computed(() => {
  const markers = []

  // 添加当前位置标记
  if (currentLocation.value.latitude && currentLocation.value.longitude) {
    markers.push({
      id: 'current',
      latitude: currentLocation.value.latitude,
      longitude: currentLocation.value.longitude,
      iconPath: '/static/icons/location.png',
      width: 30,
      height: 30,
      title: '当前位置',
      callout: {
        content: currentLocation.value.name || '当前位置',
        color: '#333',
        fontSize: 12,
        borderRadius: 4,
        bgColor: '#fff',
        padding: 5,
        display: 'ALWAYS'
      }
    })
  }

  // 添加附近地点标记
  filteredNearbyPlaces.value.forEach(place => {
    markers.push({
      id: place.name,
      latitude: place.latitude,
      longitude: place.longitude,
      iconPath: '/static/icons/place.png',
      width: 25,
      height: 25,
      title: place.name,
      callout: {
        content: place.name,
        color: '#333',
        fontSize: 11,
        borderRadius: 4,
        bgColor: '#fff',
        padding: 4
      }
    })
  })

  // 添加辐射超标警告标记（历史数据）
  if (showRadiationOverlay.value) {
    radiationAlerts.value.forEach(alert => {
      markers.push({
        id: `alert_${alert.id}`,
        latitude: alert.latitude,
        longitude: alert.longitude,
        iconPath: `/static/icons/radiation-${alert.level}.png`,
        width: 35,
        height: 35,
        title: `辐射超标 - ${alert.doseRate} μSv/h`,
        callout: {
          content: `⚠️ ${alert.doseRate} μSv/h`,
          color: alert.level === 'danger' ? '#ff4444' : '#ff8800',
          fontSize: 12,
          borderRadius: 4,
          bgColor: '#fff',
          padding: 5,
          display: 'BYCLICK'
        }
      })
    })
  }

  // 添加实时报警标记
  if (showRadiationOverlay.value) {
    alertMarkers.value.forEach(marker => {
      markers.push(marker)
    })
  }

  return markers
})

// 地图事件处理
const onMarkerTap = (e) => {
  console.log('标记点击:', e)
  const markerId = e.detail.markerId

  // 处理不同类型的标记点击
  if (markerId === 'current') {
    // 当前位置标记
    showLocationInfo()
  } else if (markerId.startsWith('alert_')) {
    // 报警标记
    const alertId = markerId.replace('alert_', '')
    showAlertInfo(alertId)
  } else {
    // 其他标记（附近地点等）
    showPlaceInfo(markerId)
  }
}

const onMapTap = (e) => {
  console.log('地图点击:', e)
  // 可以在这里添加地图点击的处理逻辑
  // 比如添加自定义标记点等
}

const onRegionChange = (e) => {
  if (e.type === 'end') {
    // 地图区域变化时更新中心点（但不强制更新，避免与用户操作冲突）
    console.log('地图区域变化:', e.detail)
  }
}

// 显示位置信息
const showLocationInfo = () => {
  const location = currentLocation.value
  if (!location.latitude || !location.longitude) {
    showToast('位置信息不可用', 'info')
    return
  }

  const content = `位置: ${location.address || '未知位置'}\n坐标: ${formatCoordinate(location.latitude, location.longitude)}\n精度: ${location.accuracy ? location.accuracy + 'm' : '未知'}`

  uni.showModal({
    title: '当前位置',
    content: content,
    showCancel: true,
    cancelText: '关闭',
    confirmText: '分享',
    success: (res) => {
      if (res.confirm) {
        shareLocation()
      }
    }
  })
}

// 显示报警信息
const showAlertInfo = (alertId) => {
  // 查找历史报警
  const alert = radiationAlerts.value.find(a => a.id.toString() === alertId)
  if (!alert) {
    showToast('报警信息不存在', 'info')
    return
  }

  const timeStr = new Date(alert.timestamp).toLocaleString('zh-CN')
  const content = `时间: ${timeStr}\n位置: ${alert.address}\n剂量率: ${alert.doseRate} μSv/h\n级别: ${alert.level === 'danger' ? '危险' : '警告'}`

  uni.showModal({
    title: '辐射超标警告',
    content: content,
    showCancel: true,
    cancelText: '关闭',
    confirmText: '导航',
    success: (res) => {
      if (res.confirm) {
        // 导航到警告位置
        uni.openLocation({
          latitude: alert.latitude,
          longitude: alert.longitude,
          name: '辐射超标位置',
          address: alert.address
        })
      }
    }
  })
}

// 显示地点信息
const showPlaceInfo = (placeId) => {
  const place = nearbyPlaces.value.find(p => p.name === placeId)
  if (!place) {
    showToast('地点信息不存在', 'info')
    return
  }

  const content = `地址: ${place.address}\n距离: ${place.distance}m\n步行时间: ${place.walkTime}分钟`

  uni.showModal({
    title: place.name,
    content: content,
    showCancel: true,
    cancelText: '关闭',
    confirmText: '导航',
    success: (res) => {
      if (res.confirm) {
        // 导航到地点
        uni.openLocation({
          latitude: place.latitude,
          longitude: place.longitude,
          name: place.name,
          address: place.address
        })
      }
    }
  })
}

// 分享位置
const shareLocation = () => {
  const location = currentLocation.value
  const shareText = `我的位置: ${location.name || '当前位置'}\n地址: ${location.address || '未知位置'}\n坐标: ${formatCoordinate(location.latitude, location.longitude)}`

  uni.setClipboardData({
    data: shareText,
    success: () => {
      showToast('位置信息已复制到剪贴板', 'success')
    },
    fail: () => {
      showToast('复制失败', 'error')
    }
  })
}

// 处理权限请求
const handleLocationPermission = async () => {
  try {
    await requestLocationPermission()
    // 权限获取成功后重新初始化定位服务
    await initLocationService()
    showToast('定位权限已授权', 'success')
    // 触发成功动画
    triggerSuccessAnimation()
  } catch (error) {
    showToast('定位权限被拒绝', 'error')
    // 触发错误动画
    triggerErrorAnimation()
  }
}

// 动画触发函数
const triggerSuccessAnimation = () => {
  // 添加成功动画类
  const elements = document.querySelectorAll('.permission-btn')
  elements.forEach(el => {
    el.classList.add('animate-success-pulse')
    setTimeout(() => {
      el.classList.remove('animate-success-pulse')
    }, 1000)
  })
}

const triggerErrorAnimation = () => {
  // 添加错误动画类
  const elements = document.querySelectorAll('.permission-btn')
  elements.forEach(el => {
    el.classList.add('animate-error-shake')
    setTimeout(() => {
      el.classList.remove('animate-error-shake')
    }, 600)
  })
}

// 地图交互动画
const animateMapInteraction = (type) => {
  switch (type) {
    case 'zoom-in':
      showToast('放大地图', 'info', 1000)
      break
    case 'zoom-out':
      showToast('缩小地图', 'info', 1000)
      break
    case 'locate':
      showToast('定位中...', 'info', 1500)
      break
  }
}

// 增强的地图控制函数
const enhancedZoomIn = () => {
  zoomIn()
  animateMapInteraction('zoom-in')
}

const enhancedZoomOut = () => {
  zoomOut()
  animateMapInteraction('zoom-out')
}

const enhancedCenterToCurrentLocation = async () => {
  animateMapInteraction('locate')
  await centerToCurrentLocation()
}

// 位置更新动画
const triggerLocationUpdateAnimation = () => {
  // 为GPS卡片添加数据更新动画
  const gpsCard = document.querySelector('.gps-card-enhanced')
  if (gpsCard) {
    gpsCard.classList.add('data-update-animation')
    setTimeout(() => {
      gpsCard.classList.remove('data-update-animation')
    }, 500)
  }

  // 为定位按钮添加弹跳动画
  const locateBtn = document.querySelector('.locate-btn')
  if (locateBtn) {
    locateBtn.classList.add('marker-bounce')
    setTimeout(() => {
      locateBtn.classList.remove('marker-bounce')
    }, 1000)
  }
}

// 地图控制函数
const zoomIn = () => {
  mapZoom.value = Math.min(mapZoom.value + 1, 20)
  showToast(`缩放级别: ${mapZoom.value}`, 'info', 1000)
}

const zoomOut = () => {
  mapZoom.value = Math.max(mapZoom.value - 1, 3)
  showToast(`缩放级别: ${mapZoom.value}`, 'info', 1000)
}

const centerToCurrentLocation = async () => {
  if (!currentLocation.value.latitude || !currentLocation.value.longitude) {
    // 如果没有当前位置，尝试重新定位
    try {
      await getCurrentLocation()
      showToast('已定位到当前位置', 'success')
    } catch (error) {
      showToast('定位失败: ' + error.message, 'error')
      return
    }
  }

  // 设置合适的缩放级别
  mapZoom.value = 16
  showToast('已居中到当前位置', 'success')
}

// formatCoordinate 已从 locationService.js 导入，移除重复定义

// 获取标记位置样式
const getMarkerPosition = (alert) => {
  // 这里应该根据地图坐标转换为屏幕坐标
  // 简化处理，返回固定位置样式
  return {
    position: 'absolute',
    left: '50%',
    top: '50%',
    transform: 'translate(-50%, -50%)'
  }
}

const formatAccuracy = (accuracy) => {
  if (accuracy <= 5) return '高精度'
  if (accuracy <= 10) return '中等精度'
  return '低精度'
}

const getSignalClass = (quality) => {
  if (quality >= 80) return 'excellent'
  if (quality >= 60) return 'good'
  if (quality >= 40) return 'fair'
  return 'poor'
}

const getSignalText = (quality) => {
  if (quality >= 80) return '信号优秀'
  if (quality >= 60) return '信号良好'
  if (quality >= 40) return '信号一般'
  return '信号较弱'
}

const getPlaceIcon = (type) => {
  const icons = {
    hospital: '🏥',
    restaurant: '🍽️',
    gas: '⛽',
    shopping: '🛍️',
    default: '📍'
  }
  return icons[type] || icons.default
}

// 移除不再需要的格式化函数

// 更新当前时间
const updateCurrentTime = () => {
  const now = new Date()
  currentTime.value = now.toLocaleTimeString('zh-CN', {
    hour: '2-digit',
    minute: '2-digit'
  })
}

// 刷新位置
const refreshLocation = async () => {
  isRefreshing.value = true
  refreshSuccess.value = false

  try {
    const location = await getCurrentLocation()

    // 更新GPS状态
    gpsState.value.lastUpdate = Date.now()
    gpsState.value.isConnected = true
    gpsState.value.accuracy = location.accuracy || 5

    showToast('定位成功', 'success')

    isRefreshing.value = false
    refreshSuccess.value = true

    // 3秒后重置成功状态
    setTimeout(() => {
      refreshSuccess.value = false
    }, 3000)
  } catch (error) {
    showToast(error.message || '定位失败', 'error')
    isRefreshing.value = false
    gpsState.value.isConnected = false
  }
}

// 根据坐标获取地址
const getAddressFromCoordinates = (lat, lng) => {
  // 这里应该调用地图服务的逆地理编码API
  // 模拟获取地址
  setTimeout(() => {
    currentLocation.value.address = '北京市朝阳区建国门外大街1号'
  }, 1000)
}

// 移除重复的 shareLocation 函数

// 搜索输入处理
const onSearchInput = () => {
  if (searchQuery.value.length > 1) {
    // 简化搜索，使用本地数据或者可以集成其他地图服务API
    const mockSuggestions = [
      { name: '北京市朝阳区', address: '北京市朝阳区', latitude: 39.9042, longitude: 116.4074 },
      { name: '中关村', address: '北京市海淀区中关村', latitude: 39.9831, longitude: 116.3145 },
      { name: '天安门', address: '北京市东城区天安门', latitude: 39.9054, longitude: 116.3976 }
    ].filter(item => item.name.includes(searchQuery.value))

    searchSuggestions.value = mockSuggestions.slice(0, 5)
  } else {
    searchSuggestions.value = []
  }
}

// 清除搜索
const clearSearch = () => {
  searchQuery.value = ''
  searchSuggestions.value = []
}

// 选择搜索建议
const selectSuggestion = (suggestion) => {
  searchQuery.value = suggestion.name
  searchSuggestions.value = []

  if (suggestion.latitude && suggestion.longitude) {
    // 更新地图中心到搜索位置
    mapZoom.value = 15

    // 添加搜索标记到附近地点
    const searchPlace = {
      name: suggestion.name,
      address: suggestion.address,
      type: 'search',
      latitude: suggestion.latitude,
      longitude: suggestion.longitude,
      distance: calculateDistance(
        currentLocation.value.latitude || 39.9042,
        currentLocation.value.longitude || 116.4074,
        suggestion.latitude,
        suggestion.longitude
      )
    }

    // 添加到附近地点列表
    const existingIndex = nearbyPlaces.value.findIndex(place => place.name === suggestion.name)
    if (existingIndex === -1) {
      nearbyPlaces.value.unshift(searchPlace)
    }

    showToast(`已定位到${suggestion.name}`, 'success')
  }
}

// 搜索位置
const searchLocation = () => {
  if (!searchQuery.value) {
    showToast('请输入搜索内容', 'info')
    return
  }

  // 简化搜索，直接从建议中查找
  onSearchInput()

  if (searchSuggestions.value.length > 0) {
    selectSuggestion(searchSuggestions.value[0])
  } else {
    showToast('未找到相关位置', 'info')
  }
}

// 切换地图类型
const toggleMapType = () => {
  mapType.value = mapType.value === 'standard' ? 'satellite' : 'standard'
  showToast(`已切换到${mapType.value === 'satellite' ? '卫星' : '标准'}地图`, 'success')
}

// 切换路况
const toggleTraffic = () => {
  showTraffic.value = !showTraffic.value
  showToast(showTraffic.value ? '已开启路况' : '已关闭路况', 'success')
}

// 切换标记覆盖层
const toggleRadiationOverlay = () => {
  showRadiationOverlay.value = !showRadiationOverlay.value
  showToast(showRadiationOverlay.value ? '已显示地点标记' : '已隐藏地点标记', 'success')
}

// 显示警告详情
const showAlertDetails = (alert) => {
  const timeStr = new Date(alert.timestamp).toLocaleString('zh-CN')
  uni.showModal({
    title: '辐射超标警告',
    content: `时间: ${timeStr}\n位置: ${alert.address}\n剂量率: ${alert.doseRate} μSv/h\n级别: ${alert.level === 'danger' ? '危险' : '警告'}`,
    showCancel: true,
    cancelText: '关闭',
    confirmText: '导航',
    success: (res) => {
      if (res.confirm) {
        // 导航到警告位置
        uni.openLocation({
          latitude: alert.latitude,
          longitude: alert.longitude,
          name: '辐射超标位置',
          address: alert.address
        })
      }
    }
  })
}

// 删除重复的地图控制函数，使用前面已定义的版本

// 设置附近筛选
const setNearbyFilter = (filter) => {
  nearbyFilter.value = filter
  
  // 这里可以根据筛选条件重新获取附近地点
  if (filter === 'all') {
    // 显示所有地点
  } else {
    // 筛选特定类型的地点
  }
}

// 导航到地点
const navigateToPlace = (place) => {
  uni.openLocation({
    latitude: place.latitude,
    longitude: place.longitude,
    name: place.name,
    address: place.address,
    success: () => {
      console.log('导航成功')
    },
    fail: () => {
      showToast('导航失败', 'error')
    }
  })
}

// 获取路线指引
const getDirections = (place) => {
  uni.showActionSheet({
    itemList: ['步行路线', '驾车路线', '公交路线'],
    success: (res) => {
      const types = ['walking', 'driving', 'transit']
      const type = types[res.tapIndex]
      
      // 这里应该调用路线规划API
      showToast(`正在规划${['步行', '驾车', '公交'][res.tapIndex]}路线`, 'success')
    }
  })
}

// 删除重复的地图事件处理函数，使用前面已定义的版本

// 显示辐射数据
const showRadiationDataOverlay = () => {
  showRadiationOverlay.value = true
}

// 隐藏辐射数据
const hideRadiationData = () => {
  showRadiationOverlay.value = false
}

// 移除百度地图实例，使用uni-app原生地图

// 删除重复的mapCenter和mapZoom声明，使用前面已定义的版本

// 移除不再需要的变量

// 移除重复的toast定义，已在前面定义

// 显示Toast通知
const showToast = (message, type = 'info', duration = 3000) => {
  toast.value = {
    show: true,
    message,
    type
  }

  setTimeout(() => {
    toast.value.show = false
  }, duration)
}

// 移除不再需要的百度地图相关函数

// 生命周期
onMounted(() => {
  // 初始化动画状态
  animationState.isPageLoaded = true

  // 立即停止加载状态，显示地图
  setTimeout(() => {
    locationState.isLocating = false
    animationState.isMapReady = true
  }, 100)

  // 页面进入动画序列
  setTimeout(() => {
    const pageElement = document.querySelector('.page-container')
    if (pageElement) {
      pageElement.classList.add('page-enter-animation')
    }
  }, 50)

  // 初始化定位服务（异步，不阻塞地图显示）
  initLocationService().then(() => {
    console.log('定位服务初始化成功')
    animationState.isLocationReady = true

    // 开始位置监听
    startLocationWatch((location, error) => {
      if (error) {
        console.error('位置监听错误:', error)
        gpsState.value.isConnected = false
      } else if (location) {
        // 更新GPS状态
        gpsState.value.lastUpdate = Date.now()
        gpsState.value.isConnected = true
        gpsState.value.accuracy = location.accuracy || 5

        // 触发位置更新动画
        triggerLocationUpdateAnimation()
      }
    })
  }).catch((error) => {
    console.error('定位服务初始化失败:', error)
    // 即使定位失败，也要显示地图
    locationState.hasPermission = false
    locationState.isLocating = false
  })

  // 更新当前时间
  updateCurrentTime()
  const timeInterval = setInterval(updateCurrentTime, 60000)

  // 模拟GPS状态更新
  const gpsInterval = setInterval(() => {
    if (locationState.isEnabled) {
      gpsState.value.satelliteCount = 10 + Math.floor(Math.random() * 6)
      gpsState.value.signalQuality = 75 + Math.floor(Math.random() * 20)
      gpsState.value.cellularSignal = -70 + Math.floor(Math.random() * 20)
    }
  }, 10000)

  // 启动报警监控服务
  const alertMonitoringInterval = startAlertMonitoring()

  // 在同步代码中注册清理函数
  onUnmounted(() => {
    clearInterval(timeInterval)
    clearInterval(gpsInterval)
    stopAlertMonitoring(alertMonitoringInterval)
    stopLocationWatch()
    cleanupLocationService()
  })
})
</script>

<style scoped>
.map-container {
  display: flex;
  flex-direction: column;
  background: #ffffff;
  position: relative;
  box-sizing: border-box;
  min-height: 100vh;
}

.status-bar {
  height: 44px;
  background: transparent;
}

/* 卡片容器 - 统一间距 */
.card-container {
  margin: 16px 24px;
}

/* 第一个卡片容器 - 顶部间距稍大 */
.card-container:first-of-type {
  margin-top: 20px;
}

/* 头部卡片样式 - 圆角矩形，参考数据页面 */
.header-card-modern {
  background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
  border-radius: 24px;
  padding: 20px 24px;
  box-shadow: 0 8px 32px rgba(59, 130, 246, 0.3);
  border: 1px solid rgba(59, 130, 246, 0.2);
  position: relative;
  overflow: hidden;
}

.header-card-modern::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0.05) 100%);
  border-radius: 24px;
  pointer-events: none;
}

.header-content-card {
  position: relative;
  display: flex;
  justify-content: space-between;
  align-items: center;
  z-index: 1;
}

.header-left {
  display: flex;
  align-items: center;
  flex: 1;
}

.app-icon-container-card {
  width: 44px;
  height: 44px;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 14px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 16px;
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.3);
}

.app-icon-container-card svg {
  width: 24px;
  height: 24px;
  color: #ffffff;
}

.header-text-card {
  flex: 1;
}

.app-title-card {
  font-size: 20px;
  font-weight: 700;
  color: #ffffff;
  line-height: 1.2;
}

.app-subtitle-card {
  font-size: 13px;
  color: rgba(255, 255, 255, 0.8);
  display: block;
  margin-top: 2px;
}

.header-right {
  display: flex;
  align-items: center;
  gap: 12px;
}

.status-indicator-card {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  background: rgba(239, 68, 68, 0.8);
  position: relative;
}

.status-indicator-card.active {
  background: rgba(16, 185, 129, 0.8);
}

.status-dot-card {
  width: 100%;
  height: 100%;
  border-radius: 50%;
  background: #ef4444;
  animation: pulse 2s ease-in-out infinite;
}

.status-indicator-card.active .status-dot-card {
  background: #10b981;
  box-shadow: 0 0 8px rgba(16, 185, 129, 0.5);
}

.refresh-button-card {
  width: 44px;
  height: 44px;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 14px;
  display: flex;
  align-items: center;
  justify-content: center;
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.3);
  transition: all 0.3s ease;
}

.refresh-button-card:active {
  transform: scale(0.95);
}

.refresh-button-card.refreshing svg {
  animation: spin-refresh 1s linear infinite;
  color: #10b981;
}

.refresh-button-card.success {
  background: rgba(16, 185, 129, 0.2);
  border-color: rgba(16, 185, 129, 0.4);
}

.refresh-button-card.success svg {
  color: #10b981;
  animation: success-bounce 0.6s ease-out;
}

.refresh-button-card svg {
  width: 20px;
  height: 20px;
  color: #ffffff;
  transition: all 0.3s ease;
}

/* EC800M 4G定位卡片 - 重新设计 */
.gps-card-enhanced {
  background: linear-gradient(135deg, #f8fafc 0%, #ffffff 100%);
  border-radius: 24px;
  padding: 24px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.08);
  border: 1px solid rgba(226, 232, 240, 0.6);
  position: relative;
  overflow: hidden;
  transition: all 0.3s ease;
}

.gps-card-enhanced::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, #3b82f6 0%, #1d4ed8 50%, #3b82f6 100%);
  border-radius: 24px 24px 0 0;
}

.gps-header-enhanced {
  display: flex;
  align-items: center;
  margin-bottom: 20px;
}

.gps-icon-enhanced {
  width: 56px;
  height: 56px;
  background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
  border-radius: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 16px;
  box-shadow: 0 4px 20px rgba(59, 130, 246, 0.3);
}

.gps-icon-enhanced svg {
  width: 28px;
  height: 28px;
  color: #ffffff;
  filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.1));
}

.gps-title-enhanced {
  flex: 1;
}

.gps-main-title {
  font-size: 20px;
  font-weight: 700;
  color: #1f2937;
  line-height: 1.2;
  display: block;
  margin-bottom: 4px;
}

.gps-subtitle {
  font-size: 14px;
  color: #6b7280;
  font-weight: 500;
  display: block;
}

.gps-signal-display {
  text-align: center;
  margin-bottom: 20px;
  padding: 20px;
  background: rgba(59, 130, 246, 0.05);
  border-radius: 20px;
  border: 1px solid rgba(59, 130, 246, 0.1);
}

.signal-value-container {
  display: flex;
  align-items: baseline;
  justify-content: center;
  gap: 4px;
}

.signal-value {
  font-size: 48px;
  font-weight: 800;
  color: #3b82f6;
  line-height: 1;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.signal-unit {
  font-size: 20px;
  color: #6b7280;
  font-weight: 600;
}

.signal-status-indicator {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  margin-bottom: 24px;
}

.signal-icon {
  font-size: 16px;
}

.signal-status-text {
  font-size: 14px;
  font-weight: 600;
}

.signal-status-text.excellent { color: #10b981; }
.signal-status-text.good { color: #3b82f6; }
.signal-status-text.fair { color: #f59e0b; }
.signal-status-text.poor { color: #ef4444; }

.gps-details-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 16px;
  margin-bottom: 20px;
}

.gps-detail-item {
  display: flex;
  align-items: center;
  padding: 16px;
  background: rgba(248, 250, 252, 0.8);
  border-radius: 16px;
  border: 1px solid rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;
}

.gps-detail-item:active {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.detail-icon-enhanced {
  width: 40px;
  height: 40px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.detail-icon-enhanced.satellite {
  background: linear-gradient(135deg, #10b981 0%, #059669 100%);
}

.detail-icon-enhanced.accuracy {
  background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
}

.detail-icon-enhanced.signal {
  background: linear-gradient(135deg, #8b5cf6 0%, #7c3aed 100%);
}

.detail-icon-enhanced.location {
  background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
}

.detail-icon-enhanced svg {
  width: 20px;
  height: 20px;
  color: #ffffff;
}

.detail-content-enhanced {
  flex: 1;
}

.detail-label-enhanced {
  font-size: 12px;
  color: #6b7280;
  margin-bottom: 4px;
  display: block;
}

.detail-value-enhanced {
  font-size: 14px;
  font-weight: 600;
  color: #111827;
}

.address-section-enhanced {
  padding: 16px;
  background: rgba(16, 185, 129, 0.05);
  border-radius: 16px;
  border: 1px solid rgba(16, 185, 129, 0.1);
}

.address-label-enhanced {
  font-size: 12px;
  color: #10b981;
  font-weight: 600;
  display: block;
  margin-bottom: 8px;
}

.address-text-enhanced {
  font-size: 14px;
  color: #1f2937;
  line-height: 1.4;
  font-weight: 500;
}

/* 搜索卡片 - 参考数据页面设计 */
.search-card-modern {
  background: linear-gradient(135deg, #f8fafc 0%, #ffffff 100%);
  border-radius: 24px;
  padding: 20px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.08);
  border: 1px solid rgba(226, 232, 240, 0.6);
  position: relative;
  overflow: hidden;
}

.search-card-modern::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, #8b5cf6 0%, #7c3aed 100%);
  border-radius: 24px 24px 0 0;
}

.search-input-container {
  position: relative;
  background: rgba(248, 250, 252, 0.8);
  border-radius: 16px;
  padding: 16px;
  border: 1px solid rgba(0, 0, 0, 0.05);
  display: flex;
  align-items: center;
}

.search-icon {
  width: 20px;
  height: 20px;
  color: #64748b;
  margin-right: 12px;
}

.search-input {
  flex: 1;
  font-size: 16px;
  border: none;
  outline: none;
  background: transparent;
  color: #1e293b;
}

.search-input::placeholder {
  color: #94a3b8;
}

.search-clear {
  width: 24px;
  height: 24px;
  border-radius: 12px;
  background: rgba(59, 130, 246, 0.1);
  display: flex;
  align-items: center;
  justify-content: center;
  margin-left: 8px;
  transition: all 0.3s ease;
}

.search-clear:active {
  transform: scale(0.95);
}

.search-clear svg {
  width: 14px;
  height: 14px;
  color: #3b82f6;
}

/* 搜索建议 */
.search-suggestions {
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  border-radius: 12px;
  margin-top: 8px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(0, 0, 0, 0.05);
  overflow: hidden;
  z-index: 100;
}

.suggestion-item {
  display: flex;
  align-items: center;
  padding: 16px;
  border-bottom: 1px solid rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;
}

.suggestion-item:last-child {
  border-bottom: none;
}

.suggestion-item:active {
  background: rgba(59, 130, 246, 0.1);
}

.suggestion-icon {
  width: 36px;
  height: 36px;
  border-radius: 18px;
  background: rgba(59, 130, 246, 0.1);
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 12px;
}

.suggestion-icon svg {
  width: 18px;
  height: 18px;
  color: #3b82f6;
}

.suggestion-content {
  flex: 1;
}

.suggestion-name {
  font-size: 16px;
  font-weight: 600;
  color: #1e293b;
  margin-bottom: 4px;
}

.suggestion-address {
  font-size: 14px;
  color: #64748b;
}

/* 地图卡片 - 参考数据页面设计 */
.map-card-modern {
  background: linear-gradient(135deg, #f8fafc 0%, #ffffff 100%);
  border-radius: 24px;
  overflow: hidden;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.08);
  border: 1px solid rgba(226, 232, 240, 0.6);
  position: relative;
}

.map-card-modern::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, #3b82f6 0%, #1d4ed8 50%, #3b82f6 100%);
  border-radius: 24px 24px 0 0;
}

.map-header-enhanced {
  display: flex;
  align-items: center;
  padding: 20px 24px;
  border-bottom: 1px solid rgba(0, 0, 0, 0.05);
}

.map-icon-container {
  width: 44px;
  height: 44px;
  background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
  border-radius: 14px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 16px;
  box-shadow: 0 2px 8px rgba(59, 130, 246, 0.2);
}

.map-icon-container svg {
  width: 24px;
  height: 24px;
  color: #ffffff;
}

.map-title-section {
  flex: 1;
}

.map-title-enhanced {
  font-size: 18px;
  font-weight: 700;
  color: #1f2937;
  line-height: 1.2;
}

.map-subtitle-enhanced {
  font-size: 14px;
  color: #6b7280;
  font-weight: 500;
}

.map-controls-enhanced {
  display: flex;
  gap: 8px;
}

.control-btn-enhanced {
  padding: 8px 16px;
  border-radius: 12px;
  background: rgba(248, 250, 252, 0.8);
  border: 1px solid rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;
}

.control-btn-enhanced.active {
  background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
  border-color: transparent;
  box-shadow: 0 4px 20px rgba(59, 130, 246, 0.3);
}

.control-btn-enhanced text {
  font-size: 14px;
  font-weight: 500;
  color: #64748b;
}

.control-btn-enhanced.active text {
  color: white;
}

.control-btn-enhanced:active {
  transform: scale(0.95);
}

.map-wrapper-enhanced {
  position: relative;
  height: 350px;
}

.map-view {
  width: 100%;
  height: 100%;
  border-radius: 0 0 24px 24px;
}

/* 百度地图容器样式 */
.baidu-map-container {
  width: 100%;
  height: 100%;
  border-radius: 0 0 24px 24px;
  overflow: hidden;
}

/* 隐藏百度地图logo和版权信息 */
:deep(.BMap_cpyCtrl),
:deep(.anchorBL) {
  display: none !important;
}

/* 自定义Toast样式 */
.custom-toast {
  position: fixed;
  top: 20px;
  left: 50%;
  transform: translateX(-50%);
  z-index: 9999;
  min-width: 200px;
  max-width: 80%;
  padding: 0;
  border-radius: 12px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.15);
  backdrop-filter: blur(20px);
  animation: toastSlideIn 0.3s ease-out;
}

.custom-toast.success {
  background: linear-gradient(135deg, rgba(16, 185, 129, 0.95) 0%, rgba(5, 150, 105, 0.95) 100%);
  border: 1px solid rgba(16, 185, 129, 0.3);
}

.custom-toast.error {
  background: linear-gradient(135deg, rgba(239, 68, 68, 0.95) 0%, rgba(220, 38, 38, 0.95) 100%);
  border: 1px solid rgba(239, 68, 68, 0.3);
}

.custom-toast.info {
  background: linear-gradient(135deg, rgba(59, 130, 246, 0.95) 0%, rgba(29, 78, 216, 0.95) 100%);
  border: 1px solid rgba(59, 130, 246, 0.3);
}

.toast-content {
  display: flex;
  align-items: center;
  padding: 16px 20px;
  gap: 12px;
}

.toast-icon {
  width: 24px;
  height: 24px;
  flex-shrink: 0;
}

.toast-icon svg {
  width: 100%;
  height: 100%;
  color: #ffffff;
  filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.1));
}

.toast-text {
  color: #ffffff;
  font-size: 14px;
  font-weight: 500;
  line-height: 1.4;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

@keyframes toastSlideIn {
  from {
    opacity: 0;
    transform: translateX(-50%) translateY(-20px);
  }
  to {
    opacity: 1;
    transform: translateX(-50%) translateY(0);
  }
}

/* 地图覆盖层控件 */
.map-overlay-controls {
  position: absolute;
  top: 16px;
  right: 16px;
  display: flex;
  flex-direction: column;
  gap: 8px;
  z-index: 10;
}

.control-group {
  display: flex;
  flex-direction: column;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(0, 0, 0, 0.05);
}

.zoom-btn {
  width: 44px;
  height: 44px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: transparent;
  border-bottom: 1px solid rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;
}

.zoom-btn:last-child {
  border-bottom: none;
}

.zoom-btn:active {
  background: rgba(59, 130, 246, 0.1);
}

.zoom-btn svg {
  width: 20px;
  height: 20px;
  color: #3b82f6;
}

.locate-btn {
  width: 44px;
  height: 44px;
  background: linear-gradient(135deg, #3b82f6 0%, #10b981 100%);
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 4px 20px rgba(59, 130, 246, 0.3);
  transition: all 0.3s ease;
}

.locate-btn:active {
  transform: scale(0.95);
}

.locate-btn svg {
  width: 20px;
  height: 20px;
  color: white;
}

/* 辐射超标警告标记 */
.radiation-alert-markers {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  pointer-events: none;
  z-index: 5;
}

.radiation-marker {
  position: absolute;
  width: 40px;
  height: 40px;
  pointer-events: auto;
  cursor: pointer;
}

.marker-icon {
  width: 100%;
  height: 100%;
  border-radius: 50%;
  background: rgba(239, 68, 68, 0.9);
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 4px 20px rgba(239, 68, 68, 0.4);
  position: relative;
  z-index: 2;
}

.radiation-marker.warning .marker-icon {
  background: rgba(245, 158, 11, 0.9);
  box-shadow: 0 4px 20px rgba(245, 158, 11, 0.4);
}

.marker-icon svg {
  width: 20px;
  height: 20px;
  color: white;
}

.marker-pulse {
  position: absolute;
  top: -4px;
  left: -4px;
  right: -4px;
  bottom: -4px;
  border-radius: 50%;
  border: 2px solid rgba(239, 68, 68, 0.6);
  animation: pulse 2s ease-in-out infinite;
}

.radiation-marker.warning .marker-pulse {
  border-color: rgba(245, 158, 11, 0.6);
}

/* 辐射监测卡片 - 参考数据页面设计 */
.radiation-monitoring-card-redesigned {
  background: linear-gradient(135deg, #f8fafc 0%, #ffffff 100%);
  border-radius: 24px;
  padding: 24px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.08);
  border: 1px solid rgba(226, 232, 240, 0.6);
  position: relative;
  overflow: hidden;
}

.radiation-monitoring-card-redesigned::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, #10b981 0%, #059669 50%, #10b981 100%);
  border-radius: 24px 24px 0 0;
}

.radiation-monitoring-card-redesigned.level-warning::before {
  background: linear-gradient(90deg, #f59e0b 0%, #d97706 50%, #f59e0b 100%);
}

.radiation-monitoring-card-redesigned.level-danger::before {
  background: linear-gradient(90deg, #ef4444 0%, #dc2626 50%, #ef4444 100%);
}

.card-header-redesigned {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
}

.radiation-icon-large {
  width: 56px;
  height: 56px;
  border-radius: 16px;
  background: linear-gradient(135deg, #10b981 0%, #059669 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 16px;
  box-shadow: 0 4px 20px rgba(16, 185, 129, 0.3);
}

.radiation-icon-large.level-warning {
  background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
  box-shadow: 0 4px 20px rgba(245, 158, 11, 0.3);
}

.radiation-icon-large.level-danger {
  background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
  box-shadow: 0 4px 20px rgba(239, 68, 68, 0.3);
}

.radiation-icon-large svg {
  width: 28px;
  height: 28px;
  color: white;
}

.status-indicators-redesigned {
  display: flex;
  align-items: center;
  gap: 12px;
}

.status-badge {
  padding: 8px 16px;
  border-radius: 12px;
  background: rgba(16, 185, 129, 0.1);
  border: 1px solid rgba(16, 185, 129, 0.2);
}

.status-badge.level-warning {
  background: rgba(245, 158, 11, 0.1);
  border-color: rgba(245, 158, 11, 0.2);
}

.status-badge.level-danger {
  background: rgba(239, 68, 68, 0.1);
  border-color: rgba(239, 68, 68, 0.2);
}

.status-badge text {
  font-size: 14px;
  font-weight: 600;
  color: #10b981;
}

.status-badge.level-warning text {
  color: #f59e0b;
}

.status-badge.level-danger text {
  color: #ef4444;
}

/* 主要数据显示区域 */
.main-data-section {
  text-align: center;
}

.central-value-display {
  margin-bottom: 24px;
  padding: 20px;
  background: rgba(59, 130, 246, 0.05);
  border-radius: 20px;
  border: 1px solid rgba(59, 130, 246, 0.1);
}

.dose-rate-number {
  font-size: 48px;
  font-weight: 800;
  color: #10b981;
  line-height: 1;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
  display: block;
}

.dose-rate-number.level-warning {
  color: #f59e0b;
}

.dose-rate-number.level-danger {
  color: #ef4444;
}

.dose-rate-unit {
  font-size: 16px;
  color: #6b7280;
  font-weight: 600;
  margin-top: 8px;
  display: block;
}

.info-grid-redesigned {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 16px;
}

.info-card {
  display: flex;
  align-items: center;
  padding: 16px;
  background: rgba(248, 250, 252, 0.8);
  border-radius: 16px;
  border: 1px solid rgba(0, 0, 0, 0.05);
}

.info-icon {
  width: 40px;
  height: 40px;
  border-radius: 12px;
  background: linear-gradient(135deg, #e2e8f0 0%, #cbd5e1 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 12px;
}

.info-icon.shield {
  background: linear-gradient(135deg, #10b981 0%, #059669 100%);
}

.info-icon.dollar {
  background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
}

.info-icon.clock {
  background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
}

.info-icon.warning {
  background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
}

.info-icon svg {
  width: 20px;
  height: 20px;
  color: white;
}

.info-content {
  flex: 1;
}

.info-label {
  font-size: 12px;
  color: #6b7280;
  margin-bottom: 4px;
  display: block;
}

.info-value {
  font-size: 14px;
  font-weight: 600;
  color: #111827;
}

/* 附近地点 - 参考数据页面设计 */
.nearby-section-enhanced {
  background: linear-gradient(135deg, #f8fafc 0%, #ffffff 100%);
  border-radius: 24px;
  overflow: hidden;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.08);
  border: 1px solid rgba(226, 232, 240, 0.6);
  margin-bottom: 120px;
  position: relative;
}

.nearby-section-enhanced::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, #fbbf24 0%, #f59e0b 50%, #fbbf24 100%);
  border-radius: 24px 24px 0 0;
}

.section-header-enhanced {
  display: flex;
  align-items: center;
  padding: 20px 24px 16px;
  border-bottom: 1px solid rgba(0, 0, 0, 0.05);
}

.section-icon-container {
  width: 44px;
  height: 44px;
  background: linear-gradient(135deg, #fbbf24 0%, #f59e0b 100%);
  border-radius: 14px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 16px;
  box-shadow: 0 2px 8px rgba(139, 92, 246, 0.2);
}

.section-icon-container svg {
  width: 24px;
  height: 24px;
  color: #ffffff;
}

.section-title-section {
  flex: 1;
}

.section-title-enhanced {
  font-size: 18px;
  font-weight: 700;
  color: #1f2937;
  line-height: 1.2;
}

.section-subtitle-enhanced {
  font-size: 14px;
  color: #6b7280;
  font-weight: 500;
}

.section-filter-enhanced {
  display: flex;
  gap: 8px;
  overflow-x: auto;
  padding: 0 24px 16px;
  margin-bottom: 8px;
}

.filter-tab-enhanced {
  padding: 10px 16px;
  border-radius: 12px;
  background: rgba(248, 250, 252, 0.8);
  border: 1px solid rgba(0, 0, 0, 0.05);
  white-space: nowrap;
  transition: all 0.3s ease;
}

.filter-tab-enhanced.active {
  background: linear-gradient(135deg, #fbbf24 0%, #f59e0b 100%);
  border-color: transparent;
  box-shadow: 0 4px 20px rgba(251, 191, 36, 0.3);
}

.filter-tab-enhanced text {
  font-size: 14px;
  font-weight: 500;
  color: #64748b;
}

.filter-tab-enhanced.active text {
  color: white;
}

.filter-tab-enhanced:active {
  transform: scale(0.95);
}

.nearby-list-enhanced {
  max-height: 300px;
  padding: 0 24px 20px;
}

.nearby-item-enhanced {
  display: flex;
  align-items: center;
  padding: 16px 0;
  border-bottom: 1px solid rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;
}

.nearby-item-enhanced:last-child {
  border-bottom: none;
}

.nearby-item-enhanced:active {
  background: rgba(139, 92, 246, 0.05);
  transform: translateX(4px);
}

.place-icon-enhanced {
  width: 48px;
  height: 48px;
  border-radius: 16px;
  background: linear-gradient(135deg, #fbbf24 0%, #f59e0b 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 16px;
  font-size: 20px;
  box-shadow: 0 4px 20px rgba(251, 191, 36, 0.3);
}

.place-icon-enhanced.hospital {
  background: linear-gradient(135deg, #10b981 0%, #059669 100%);
  box-shadow: 0 4px 20px rgba(16, 185, 129, 0.3);
}

.place-icon-enhanced.restaurant {
  background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
  box-shadow: 0 4px 20px rgba(245, 158, 11, 0.3);
}

.place-icon-enhanced.gas {
  background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
  box-shadow: 0 4px 20px rgba(239, 68, 68, 0.3);
}

.place-icon-enhanced.shopping {
  background: linear-gradient(135deg, #fbbf24 0%, #f59e0b 100%);
  box-shadow: 0 4px 20px rgba(251, 191, 36, 0.3);
}

.place-info-enhanced {
  flex: 1;
}

.place-name-enhanced {
  font-size: 16px;
  font-weight: 600;
  color: #1f2937;
  margin-bottom: 4px;
}

.place-address-enhanced {
  font-size: 14px;
  color: #6b7280;
  margin-bottom: 8px;
}

.place-meta-enhanced {
  display: flex;
  gap: 16px;
}

.meta-item {
  display: flex;
  align-items: center;
  gap: 4px;
}

.meta-icon {
  font-size: 12px;
}

.meta-text {
  font-size: 12px;
  color: #9ca3af;
  font-weight: 500;
}

.place-actions-enhanced {
  margin-left: 16px;
}

.action-btn-enhanced {
  width: 36px;
  height: 36px;
  border-radius: 12px;
  background: rgba(139, 92, 246, 0.1);
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
}

.action-btn-enhanced:active {
  transform: scale(0.95);
  background: rgba(139, 92, 246, 0.2);
}

.action-btn-enhanced svg {
  width: 16px;
  height: 16px;
  color: #8b5cf6;
}

/* 动画效果 */
@keyframes pulse {
  0%, 100% {
    transform: scale(1);
    opacity: 1;
  }
  50% {
    transform: scale(1.05);
    opacity: 0.8;
  }
}

@keyframes spin-refresh {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

@keyframes success-bounce {
  0%, 20%, 50%, 80%, 100% {
    transform: translateY(0);
  }
  40% {
    transform: translateY(-4px);
  }
  60% {
    transform: translateY(-2px);
  }
}

@keyframes animate-slide-in {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes animate-bounce-in {
  0% {
    opacity: 0;
    transform: scale(0.3);
  }
  50% {
    opacity: 1;
    transform: scale(1.05);
  }
  70% {
    transform: scale(0.9);
  }
  100% {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes animate-fade-scale {
  from {
    opacity: 0;
    transform: scale(0.8);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes animate-float {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-4px);
  }
}

@keyframes animate-glow {
  0%, 100% {
    box-shadow: 0 0 5px rgba(16, 185, 129, 0.5);
  }
  50% {
    box-shadow: 0 0 20px rgba(16, 185, 129, 0.8);
  }
}

/* 应用动画类 */
.animate-slide-in {
  animation: animate-slide-in 0.8s ease;
  animation-fill-mode: both;
}

.animate-bounce-in {
  animation: animate-bounce-in 1.2s ease;
  animation-fill-mode: both;
}

.animate-fade-scale {
  animation: animate-fade-scale 0.6s ease;
  animation-fill-mode: both;
}

.animate-pulse {
  animation: pulse 2s ease-in-out infinite;
}

.animate-float {
  animation: animate-float 3s ease-in-out infinite;
}

.animate-glow {
  animation: animate-glow 2s ease-in-out infinite;
}

.pulse-icon {
  animation: pulse 2s ease-in-out infinite;
}

.counter-animation {
  animation: animate-bounce-in 1.5s ease;
}

.typewriter-text {
  overflow: hidden;
  border-right: 2px solid #3b82f6;
  white-space: nowrap;
  animation: typing 2s steps(8, end), blink-caret 0.75s step-end infinite;
}

@keyframes typing {
  from {
    width: 0;
  }
  to {
    width: 100%;
  }
}

@keyframes blink-caret {
  from, to {
    border-color: transparent;
  }
  50% {
    border-color: #3b82f6;
  }
}

/* 交互效果 */
.interactive-card {
  transition: all 0.3s ease;
}

.interactive-card:active {
  transform: translateY(-2px);
  box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);
}

.interactive-trigger:hover {
  transform: translateY(-1px);
  box-shadow: 0 10px 35px rgba(0, 0, 0, 0.12);
}

.gpu-accelerated {
  transform: translateZ(0);
  backface-visibility: hidden;
  perspective: 1000px;
}

/* 响应式设计 */
@media (max-width: 375px) {
  .card-container {
    margin: 12px 20px;
  }

  /* 第一个卡片容器 - 小屏幕顶部间距 */
  .card-container:first-of-type {
    margin-top: 16px;
  }

  .header-content-card {
    padding: 16px 20px 12px;
  }

  .app-title-card {
    font-size: 18px;
  }

  .app-icon-container-card {
    width: 40px;
    height: 40px;
  }

  .app-icon-container-card svg {
    width: 20px;
    height: 20px;
  }

  .data-cards-redesigned {
    grid-template-columns: 1fr;
  }

  .info-grid-redesigned {
    grid-template-columns: 1fr;
  }

  .dose-rate-number {
    font-size: 36px;
  }

  .map-wrapper-enhanced {
    height: 280px;
  }
}

/* 深色模式支持 */
@media (prefers-color-scheme: dark) {
  .map-container {
    background: #0f172a;
  }

  .header-card-modern {
    background: linear-gradient(135deg, #1e293b 0%, #334155 100%);
    border-color: rgba(255, 255, 255, 0.1);
  }

  .data-card-modern,
  .search-card-modern,
  .map-card-modern,
  .radiation-monitoring-card-redesigned,
  .nearby-section-enhanced,
  .location-address-card {
    background: linear-gradient(135deg, #1e293b 0%, #334155 100%);
    border-color: rgba(255, 255, 255, 0.1);
  }

  .title-modern,
  .section-title-enhanced,
  .place-name-enhanced,
  .value-modern,
  .info-value,
  .address-text,
  .coordinates-text {
    color: #f1f5f9;
  }

  .subtitle-modern,
  .section-subtitle-enhanced,
  .place-address-enhanced,
  .unit-modern,
  .info-label,
  .address-label,
  .coordinates-label {
    color: #94a3b8;
  }
}

/* 地图加载状态覆盖层 */
.map-loading-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background: rgba(255, 255, 255, 0.85);
  z-index: 10;
  backdrop-filter: blur(2px);
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 4px solid #e5e7eb;
  border-top: 4px solid #3b82f6;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 16px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-text {
  font-size: 16px;
  color: #6b7280;
  font-weight: 500;
}

/* 权限错误状态覆盖层 */
.map-permission-overlay {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background: rgba(255, 255, 255, 0.95);
  border-radius: 16px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  z-index: 15;
  padding: 32px 24px;
  max-width: 280px;
  backdrop-filter: blur(8px);
}

.permission-icon {
  font-size: 40px;
  margin-bottom: 12px;
}

.permission-title {
  font-size: 16px;
  font-weight: 600;
  color: #374151;
  margin-bottom: 6px;
  text-align: center;
}

.permission-desc {
  font-size: 13px;
  color: #6b7280;
  text-align: center;
  margin-bottom: 20px;
  line-height: 1.4;
}

.permission-btn {
  background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
  color: white;
  border: none;
  border-radius: 10px;
  padding: 10px 20px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  min-width: 100px;
}

.permission-btn:hover {
  background: linear-gradient(135deg, #1d4ed8 0%, #1e40af 100%);
  transform: translateY(-1px);
}

/* 高级动画系统 */
@keyframes slideInFromTop {
  0% {
    opacity: 0;
    transform: translateY(-30px);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideInFromBottom {
  0% {
    opacity: 0;
    transform: translateY(30px);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideInFromLeft {
  0% {
    opacity: 0;
    transform: translateX(-30px);
  }
  100% {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes slideInFromRight {
  0% {
    opacity: 0;
    transform: translateX(30px);
  }
  100% {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes scaleIn {
  0% {
    opacity: 0;
    transform: scale(0.8);
  }
  100% {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes bounceIn {
  0% {
    opacity: 0;
    transform: scale(0.3);
  }
  50% {
    opacity: 1;
    transform: scale(1.05);
  }
  70% {
    transform: scale(0.9);
  }
  100% {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes pulseGlow {
  0%, 100% {
    box-shadow: 0 0 5px rgba(59, 130, 246, 0.5);
  }
  50% {
    box-shadow: 0 0 20px rgba(59, 130, 246, 0.8), 0 0 30px rgba(59, 130, 246, 0.6);
  }
}

@keyframes floatUp {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-10px);
  }
}

@keyframes shimmer {
  0% {
    background-position: -200px 0;
  }
  100% {
    background-position: calc(200px + 100%) 0;
  }
}

@keyframes ripple {
  0% {
    transform: scale(0);
    opacity: 1;
  }
  100% {
    transform: scale(4);
    opacity: 0;
  }
}

@keyframes heartbeat {
  0%, 100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.1);
  }
}

@keyframes rotateIn {
  0% {
    opacity: 0;
    transform: rotate(-200deg);
  }
  100% {
    opacity: 1;
    transform: rotate(0deg);
  }
}

@keyframes zoomInOut {
  0%, 100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.1);
  }
}

/* 动画类 */
.animate-slide-in-top {
  animation: slideInFromTop 0.6s cubic-bezier(0.25, 0.46, 0.45, 0.94) forwards;
}

.animate-slide-in-bottom {
  animation: slideInFromBottom 0.6s cubic-bezier(0.25, 0.46, 0.45, 0.94) forwards;
}

.animate-slide-in-left {
  animation: slideInFromLeft 0.6s cubic-bezier(0.25, 0.46, 0.45, 0.94) forwards;
}

.animate-slide-in-right {
  animation: slideInFromRight 0.6s cubic-bezier(0.25, 0.46, 0.45, 0.94) forwards;
}

.animate-scale-in {
  animation: scaleIn 0.5s cubic-bezier(0.25, 0.46, 0.45, 0.94) forwards;
}

.animate-bounce-in {
  animation: bounceIn 0.8s cubic-bezier(0.68, -0.55, 0.265, 1.55) forwards;
}

.animate-pulse-glow {
  animation: pulseGlow 2s ease-in-out infinite;
}

.animate-float {
  animation: floatUp 3s ease-in-out infinite;
}

.animate-shimmer {
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
  background-size: 200px 100%;
  animation: shimmer 2s infinite;
}

.animate-heartbeat {
  animation: heartbeat 1.5s ease-in-out infinite;
}

.animate-rotate-in {
  animation: rotateIn 0.6s cubic-bezier(0.25, 0.46, 0.45, 0.94) forwards;
}

.animate-zoom-hover {
  transition: transform 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

.animate-zoom-hover:hover {
  transform: scale(1.05);
}

/* 交互动画 */
.interactive-card {
  transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  cursor: pointer;
}

.interactive-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
}

.interactive-card:active {
  transform: translateY(-2px);
  transition: all 0.1s ease;
}

/* 地图标记动画 */
.radiation-marker {
  position: relative;
  z-index: 5;
}

.radiation-marker.warning {
  animation: pulseWarning 2s ease-in-out infinite;
}

.radiation-marker.danger {
  animation: pulseDanger 1.5s ease-in-out infinite;
}

.radiation-marker.critical {
  animation: pulseCritical 1s ease-in-out infinite;
}

@keyframes pulseWarning {
  0%, 100% {
    box-shadow: 0 0 0 0 rgba(251, 191, 36, 0.7);
  }
  50% {
    box-shadow: 0 0 0 10px rgba(251, 191, 36, 0);
  }
}

@keyframes pulseDanger {
  0%, 100% {
    box-shadow: 0 0 0 0 rgba(239, 68, 68, 0.7);
  }
  50% {
    box-shadow: 0 0 0 15px rgba(239, 68, 68, 0);
  }
}

@keyframes pulseCritical {
  0%, 100% {
    box-shadow: 0 0 0 0 rgba(220, 38, 38, 0.8);
  }
  50% {
    box-shadow: 0 0 0 20px rgba(220, 38, 38, 0);
  }
}

.marker-ripple {
  position: absolute;
  top: 50%;
  left: 50%;
  width: 100%;
  height: 100%;
  border-radius: 50%;
  background: rgba(59, 130, 246, 0.3);
  transform: translate(-50%, -50%);
  animation: ripple 2s infinite;
}

/* 地图控制按钮增强动画 */
.map-overlay-controls .zoom-btn,
.map-overlay-controls .locate-btn {
  transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

.map-overlay-controls .zoom-btn:hover,
.map-overlay-controls .locate-btn:hover {
  transform: scale(1.1);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.map-overlay-controls .zoom-btn:active,
.map-overlay-controls .locate-btn:active {
  transform: scale(0.95);
}

/* 加载状态动画增强 */
.map-loading-overlay .loading-spinner {
  animation: spin 1s linear infinite, pulseGlow 2s ease-in-out infinite;
}

/* 权限覆盖层动画 */
.map-permission-overlay {
  animation: scaleIn 0.5s cubic-bezier(0.25, 0.46, 0.45, 0.94) forwards;
}

.permission-btn {
  transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

.permission-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(59, 130, 246, 0.3);
}

/* 成功和错误动画 */
@keyframes successPulse {
  0% {
    background: linear-gradient(135deg, #10b981 0%, #059669 100%);
    transform: scale(1);
  }
  50% {
    background: linear-gradient(135deg, #34d399 0%, #10b981 100%);
    transform: scale(1.05);
  }
  100% {
    background: linear-gradient(135deg, #10b981 0%, #059669 100%);
    transform: scale(1);
  }
}

@keyframes errorShake {
  0%, 100% {
    transform: translateX(0);
  }
  10%, 30%, 50%, 70%, 90% {
    transform: translateX(-5px);
  }
  20%, 40%, 60%, 80% {
    transform: translateX(5px);
  }
}

@keyframes glowPulse {
  0%, 100% {
    box-shadow: 0 0 5px rgba(59, 130, 246, 0.5);
  }
  50% {
    box-shadow: 0 0 20px rgba(59, 130, 246, 0.8), 0 0 30px rgba(59, 130, 246, 0.6);
  }
}

.animate-success-pulse {
  animation: successPulse 1s ease-in-out;
}

.animate-error-shake {
  animation: errorShake 0.6s ease-in-out;
}

.animate-glow-pulse {
  animation: glowPulse 2s ease-in-out infinite;
}

/* 页面进入动画序列 */
.page-enter-animation {
  animation: slideInFromBottom 0.8s cubic-bezier(0.25, 0.46, 0.45, 0.94) forwards;
}

/* 卡片悬停效果增强 */
.card-container:hover {
  transform: translateY(-3px);
  box-shadow: 0 12px 35px rgba(0, 0, 0, 0.1);
  transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

/* 按钮点击反馈 */
.button-feedback {
  position: relative;
  overflow: hidden;
}

.button-feedback::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 0;
  height: 0;
  background: rgba(255, 255, 255, 0.5);
  border-radius: 50%;
  transform: translate(-50%, -50%);
  transition: width 0.3s, height 0.3s;
}

.button-feedback:active::after {
  width: 200px;
  height: 200px;
}

/* 数据更新动画 */
@keyframes dataUpdate {
  0% {
    opacity: 1;
    transform: scale(1);
  }
  50% {
    opacity: 0.7;
    transform: scale(1.05);
  }
  100% {
    opacity: 1;
    transform: scale(1);
  }
}

.data-update-animation {
  animation: dataUpdate 0.5s ease-in-out;
}

/* 地图标记弹跳动画 */
@keyframes markerBounce {
  0%, 20%, 50%, 80%, 100% {
    transform: translateY(0);
  }
  40% {
    transform: translateY(-10px);
  }
  60% {
    transform: translateY(-5px);
  }
}

.marker-bounce {
  animation: markerBounce 1s ease-in-out;
}

/* 按钮动画增强 */
.animated-button {
  position: relative;
  overflow: hidden;
  transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

.animated-button::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 0;
  height: 0;
  background: rgba(255, 255, 255, 0.3);
  border-radius: 50%;
  transform: translate(-50%, -50%);
  transition: width 0.6s, height 0.6s;
}

.animated-button:active::before {
  width: 300px;
  height: 300px;
}

/* 加载动画增强 */
.enhanced-loading {
  position: relative;
}

.enhanced-loading::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
  animation: shimmer 1.5s infinite;
}

/* 滚动条样式 */
::-webkit-scrollbar {
  width: 4px;
}

::-webkit-scrollbar-track {
  background: rgba(0, 0, 0, 0.05);
  border-radius: 2px;
}

::-webkit-scrollbar-thumb {
  background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
  border-radius: 2px;
}

::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(135deg, #1d4ed8 0%, #1e40af 100%);
}

/* 性能优化 */
.map-container * {
  box-sizing: border-box;
}

.data-card-modern,
.search-card-modern,
.map-card-modern,
.radiation-monitoring-card-redesigned,
.nearby-section-enhanced,
.location-address-card {
  will-change: transform;
  transform: translateZ(0);
}
</style> 