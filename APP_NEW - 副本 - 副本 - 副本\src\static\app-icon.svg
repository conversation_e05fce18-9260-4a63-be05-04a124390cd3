<svg width="512" height="512" viewBox="0 0 512 512" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <!-- 主要渐变 -->
    <linearGradient id="mainGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#00b4d8;stop-opacity:1" />
      <stop offset="50%" style="stop-color:#0096c7;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#0077b6;stop-opacity:1" />
    </linearGradient>
    
    <!-- 光晕渐变 -->
    <radialGradient id="glowGradient" cx="50%" cy="30%" r="70%">
      <stop offset="0%" style="stop-color:rgba(255,255,255,0.4);stop-opacity:1" />
      <stop offset="70%" style="stop-color:rgba(255,255,255,0.1);stop-opacity:1" />
      <stop offset="100%" style="stop-color:rgba(255,255,255,0);stop-opacity:0" />
    </radialGradient>
    
    <!-- 辐射符号渐变 -->
    <linearGradient id="radiationGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#FFD700;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#FFA500;stop-opacity:1" />
    </linearGradient>
    
    <!-- 阴影滤镜 -->
    <filter id="shadow" x="-50%" y="-50%" width="200%" height="200%">
      <feDropShadow dx="0" dy="8" stdDeviation="16" flood-color="rgba(0,0,0,0.3)"/>
    </filter>
    
    <!-- 内阴影滤镜 -->
    <filter id="innerShadow" x="-50%" y="-50%" width="200%" height="200%">
      <feOffset dx="0" dy="2"/>
      <feGaussianBlur stdDeviation="4" result="offset-blur"/>
      <feFlood flood-color="rgba(0,0,0,0.2)"/>
      <feComposite in2="offset-blur" operator="in"/>
    </filter>
    
    <!-- 发光效果 -->
    <filter id="glow" x="-50%" y="-50%" width="200%" height="200%">
      <feGaussianBlur stdDeviation="8" result="coloredBlur"/>
      <feMerge> 
        <feMergeNode in="coloredBlur"/>
        <feMergeNode in="SourceGraphic"/>
      </feMerge>
    </filter>
  </defs>
  
  <!-- 背景圆角矩形 -->
  <rect x="0" y="0" width="512" height="512" rx="112" ry="112" fill="url(#mainGradient)" filter="url(#shadow)"/>
  
  <!-- 光晕效果 -->
  <rect x="0" y="0" width="512" height="512" rx="112" ry="112" fill="url(#glowGradient)"/>
  
  <!-- 外层信号波纹 -->
  <circle cx="256" cy="256" r="200" fill="none" stroke="rgba(255,255,255,0.15)" stroke-width="3" opacity="0.8">
    <animate attributeName="r" values="180;220;180" dur="3s" repeatCount="indefinite"/>
    <animate attributeName="opacity" values="0.8;0.3;0.8" dur="3s" repeatCount="indefinite"/>
  </circle>
  
  <circle cx="256" cy="256" r="170" fill="none" stroke="rgba(255,255,255,0.2)" stroke-width="2" opacity="0.6">
    <animate attributeName="r" values="150;190;150" dur="2.5s" repeatCount="indefinite"/>
    <animate attributeName="opacity" values="0.6;0.2;0.6" dur="2.5s" repeatCount="indefinite"/>
  </circle>
  
  <!-- 手表外圈 -->
  <circle cx="256" cy="256" r="120" fill="none" stroke="rgba(255,255,255,0.9)" stroke-width="8" filter="url(#innerShadow)"/>
  
  <!-- 手表内圈 -->
  <circle cx="256" cy="256" r="100" fill="rgba(255,255,255,0.1)" stroke="rgba(255,255,255,0.6)" stroke-width="2"/>
  
  <!-- 辐射符号背景圆 -->
  <circle cx="256" cy="240" r="45" fill="rgba(255,215,0,0.2)" filter="url(#glow)"/>
  
  <!-- 辐射符号 -->
  <g transform="translate(256,240)" filter="url(#glow)">
    <!-- 中心圆 -->
    <circle cx="0" cy="0" r="8" fill="url(#radiationGradient)"/>
    
    <!-- 辐射扇形 -->
    <g fill="url(#radiationGradient)">
      <path d="M 0,-35 A 35,35 0 0,1 30.31,-17.5 L 15.16,-8.75 A 17.5,17.5 0 0,0 0,-17.5 Z"/>
      <path d="M 30.31,17.5 A 35,35 0 0,1 -30.31,17.5 L -15.16,8.75 A 17.5,17.5 0 0,0 15.16,8.75 Z"/>
      <path d="M -30.31,-17.5 A 35,35 0 0,1 0,-35 L 0,-17.5 A 17.5,17.5 0 0,0 -15.16,-8.75 Z"/>
    </g>
    
    <!-- 辐射符号发光效果 -->
    <animateTransform attributeName="transform" type="rotate" values="0;360" dur="10s" repeatCount="indefinite"/>
  </g>
  
  <!-- 数据点指示器 -->
  <g transform="translate(256,320)">
    <rect x="-30" y="-8" width="60" height="16" rx="8" fill="rgba(255,255,255,0.9)"/>
    <text x="0" y="4" text-anchor="middle" font-family="Arial, sans-serif" font-size="12" font-weight="bold" fill="#0077b6">MONITOR</text>
  </g>
  
  <!-- 装饰性科技元素 -->
  <g opacity="0.6">
    <!-- 左上角科技线条 -->
    <path d="M 80,80 L 120,80 L 120,120" fill="none" stroke="rgba(255,255,255,0.4)" stroke-width="2"/>
    <circle cx="80" cy="80" r="3" fill="rgba(255,255,255,0.6)"/>
    
    <!-- 右上角科技线条 -->
    <path d="M 432,80 L 392,80 L 392,120" fill="none" stroke="rgba(255,255,255,0.4)" stroke-width="2"/>
    <circle cx="432" cy="80" r="3" fill="rgba(255,255,255,0.6)"/>
    
    <!-- 左下角科技线条 -->
    <path d="M 80,432 L 120,432 L 120,392" fill="none" stroke="rgba(255,255,255,0.4)" stroke-width="2"/>
    <circle cx="80" cy="432" r="3" fill="rgba(255,255,255,0.6)"/>
    
    <!-- 右下角科技线条 -->
    <path d="M 432,432 L 392,432 L 392,392" fill="none" stroke="rgba(255,255,255,0.4)" stroke-width="2"/>
    <circle cx="432" cy="432" r="3" fill="rgba(255,255,255,0.6)"/>
  </g>
  
  <!-- 脉冲效果 -->
  <circle cx="256" cy="240" r="45" fill="none" stroke="rgba(255,215,0,0.6)" stroke-width="2" opacity="0">
    <animate attributeName="r" values="45;60;45" dur="2s" repeatCount="indefinite"/>
    <animate attributeName="opacity" values="0;0.8;0" dur="2s" repeatCount="indefinite"/>
  </circle>
</svg>
