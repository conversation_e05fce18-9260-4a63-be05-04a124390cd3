<template>
  <view class="radiation-card" :class="levelClass">
    <view class="card-header">
      <view class="status-indicator" :class="levelClass">
        <text class="indicator-icon">{{ statusIcon }}</text>
      </view>
      <text class="card-title">{{ title }}</text>
      <view class="card-actions" v-if="showActions">
        <text class="action-btn" @tap="$emit('export')">📤</text>
        <text class="action-btn" @tap="$emit('settings')">⚙️</text>
      </view>
    </view>
    
    <view class="card-content">
      <view class="main-value">
        <text class="value-number">{{ formatValue(value) }}</text>
        <text class="value-unit">{{ unit }}</text>
      </view>
      
      <view class="status-text">
        <text :class="levelClass">{{ statusText }}</text>
      </view>
      
      <view class="additional-info" v-if="additionalData">
        <view class="info-item" v-for="(item, key) in additionalData" :key="key">
          <text class="info-label">{{ item.label }}</text>
          <text class="info-value">{{ item.value }} {{ item.unit }}</text>
        </view>
      </view>
    </view>
    
    <view class="card-footer" v-if="showTrend">
      <view class="trend-indicator" :class="trendClass">
        <text class="trend-icon">{{ trendIcon }}</text>
        <text class="trend-text">{{ trendText }}</text>
      </view>
      <text class="last-update">{{ lastUpdateText }}</text>
    </view>
  </view>
</template>

<script>
import { computed } from 'vue'

export default {
  name: 'RadiationCard',
  props: {
    title: {
      type: String,
      default: '辐射监测'
    },
    value: {
      type: Number,
      required: true
    },
    unit: {
      type: String,
      default: 'μSv/h'
    },
    level: {
      type: String,
      default: 'safe' // safe, warning, danger
    },
    additionalData: {
      type: Object,
      default: null
    },
    showActions: {
      type: Boolean,
      default: false
    },
    showTrend: {
      type: Boolean,
      default: true
    },
    trend: {
      type: String,
      default: 'stable' // up, down, stable
    },
    trendValue: {
      type: Number,
      default: 0
    },
    lastUpdate: {
      type: Number,
      default: Date.now
    }
  },
  emits: ['export', 'settings'],
  setup(props) {
    const levelClass = computed(() => `level-${props.level}`)
    
    const statusIcon = computed(() => {
      switch (props.level) {
        case 'danger': return '🚨'
        case 'warning': return '⚠️'
        default: return '✅'
      }
    })
    
    const statusText = computed(() => {
      switch (props.level) {
        case 'danger': return '危险水平'
        case 'warning': return '需要注意'
        default: return '安全水平'
      }
    })
    
    const trendClass = computed(() => `trend-${props.trend}`)
    
    const trendIcon = computed(() => {
      switch (props.trend) {
        case 'up': return '📈'
        case 'down': return '📉'
        default: return '➖'
      }
    })
    
    const trendText = computed(() => {
      if (props.trend === 'stable') return '数值稳定'
      const sign = props.trend === 'up' ? '+' : ''
      return `${sign}${props.trendValue.toFixed(2)}%`
    })
    
    const lastUpdateText = computed(() => {
      const now = Date.now()
      const diff = now - props.lastUpdate
      
      if (diff < 60000) return '刚刚更新'
      if (diff < 3600000) return `${Math.floor(diff / 60000)}分钟前`
      return `${Math.floor(diff / 3600000)}小时前`
    })
    
    const formatValue = (value) => {
      if (value < 0.001) return value.toExponential(2)
      return value.toFixed(2)
    }
    
    return {
      levelClass,
      statusIcon,
      statusText,
      trendClass,
      trendIcon,
      trendText,
      lastUpdateText,
      formatValue
    }
  }
}
</script>

<style scoped>
.radiation-card {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  border-radius: 24rpx;
  padding: 32rpx;
  border: 1px solid rgba(226, 232, 240, 0.8);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow: 0 4rpx 12rpx rgba(15, 23, 42, 0.06);
  position: relative;
  overflow: hidden;
}

.radiation-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 3rpx;
  border-radius: 24rpx 24rpx 0 0;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.radiation-card:hover {
  transform: translateY(-4rpx);
  box-shadow: 0 12rpx 32rpx rgba(15, 23, 42, 0.12);
  border-color: rgba(203, 213, 225, 0.9);
}

.radiation-card.level-safe {
  border-color: rgba(16, 185, 129, 0.2);
  box-shadow: 0 4rpx 12rpx rgba(16, 185, 129, 0.08);
}

.radiation-card.level-safe::before {
  background: linear-gradient(90deg, #10b981, #059669);
}

.radiation-card.level-safe:hover {
  box-shadow: 0 12rpx 32rpx rgba(16, 185, 129, 0.15);
}

.radiation-card.level-warning {
  border-color: rgba(245, 158, 11, 0.2);
  box-shadow: 0 4rpx 12rpx rgba(245, 158, 11, 0.08);
}

.radiation-card.level-warning::before {
  background: linear-gradient(90deg, #f59e0b, #d97706);
}

.radiation-card.level-warning:hover {
  box-shadow: 0 12rpx 32rpx rgba(245, 158, 11, 0.15);
}

.radiation-card.level-danger {
  border-color: rgba(239, 68, 68, 0.3);
  box-shadow: 0 4rpx 12rpx rgba(239, 68, 68, 0.12);
  animation: dangerPulse 3s infinite;
}

.radiation-card.level-danger::before {
  background: linear-gradient(90deg, #ef4444, #dc2626);
}

.radiation-card.level-danger:hover {
  box-shadow: 0 12rpx 32rpx rgba(239, 68, 68, 0.2);
}

@keyframes dangerPulse {
  0%, 100% { 
    box-shadow: 0 4rpx 12rpx rgba(239, 68, 68, 0.12);
  }
  50% { 
    box-shadow: 0 8rpx 20rpx rgba(239, 68, 68, 0.18);
  }
}

.card-header {
  display: flex;
  align-items: center;
  gap: 16rpx;
  margin-bottom: 28rpx;
}

.status-indicator {
  width: 64rpx;
  height: 64rpx;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  animation: pulse 3s infinite;
  position: relative;
  border: 2rpx solid;
}

.status-indicator::before {
  content: '';
  position: absolute;
  width: 100%;
  height: 100%;
  border-radius: 50%;
  border: 2rpx solid;
  animation: ripple 2s infinite;
}

.status-indicator.level-safe {
  background: linear-gradient(135deg, rgba(16, 185, 129, 0.15), rgba(5, 150, 105, 0.15));
  border-color: rgba(16, 185, 129, 0.3);
}

.status-indicator.level-safe::before {
  border-color: rgba(16, 185, 129, 0.4);
}

.status-indicator.level-warning {
  background: linear-gradient(135deg, rgba(245, 158, 11, 0.15), rgba(217, 119, 6, 0.15));
  border-color: rgba(245, 158, 11, 0.3);
}

.status-indicator.level-warning::before {
  border-color: rgba(245, 158, 11, 0.4);
}

.status-indicator.level-danger {
  background: linear-gradient(135deg, rgba(239, 68, 68, 0.15), rgba(220, 38, 38, 0.15));
  border-color: rgba(239, 68, 68, 0.3);
}

.status-indicator.level-danger::before {
  border-color: rgba(239, 68, 68, 0.4);
}

@keyframes pulse {
  0% { transform: scale(1); }
  50% { transform: scale(1.05); }
  100% { transform: scale(1); }
}

@keyframes ripple {
  0% { 
    transform: scale(1); 
    opacity: 1; 
  }
  100% { 
    transform: scale(1.3); 
    opacity: 0; 
  }
}

.indicator-icon {
  font-size: 28rpx;
  filter: drop-shadow(0 2rpx 4rpx rgba(0, 0, 0, 0.1));
}

.card-title {
  flex: 1;
  font-size: 30rpx;
  color: #0f172a;
  font-weight: 700;
}

.card-actions {
  display: flex;
  gap: 12rpx;
}

.action-btn {
  width: 48rpx;
  height: 48rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(248, 250, 252, 0.8);
  border-radius: 50%;
  font-size: 22rpx;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  border: 1px solid rgba(226, 232, 240, 0.6);
  color: #64748b;
}

.action-btn:active {
  transform: scale(0.92);
  background: rgba(226, 232, 240, 0.9);
  border-color: rgba(203, 213, 225, 0.8);
}

.action-btn:hover {
  background: rgba(255, 255, 255, 0.9);
  border-color: rgba(0, 180, 216, 0.3);
  color: #00b4d8;
  box-shadow: 0 2rpx 8rpx rgba(0, 180, 216, 0.15);
}

.card-content {
  text-align: center;
  margin-bottom: 28rpx;
}

.main-value {
  display: flex;
  align-items: baseline;
  justify-content: center;
  gap: 12rpx;
  margin-bottom: 16rpx;
}

.value-number {
  font-size: 80rpx;
  font-weight: 800;
  color: #0f172a;
  line-height: 0.9;
  text-shadow: 0 2rpx 4rpx rgba(15, 23, 42, 0.08);
  letter-spacing: -1rpx;
}

.value-unit {
  font-size: 28rpx;
  color: #64748b;
  font-weight: 600;
  margin-bottom: 8rpx;
}

.status-text {
  margin-bottom: 24rpx;
}

.status-text text {
  font-size: 26rpx;
  font-weight: 700;
  padding: 10rpx 24rpx;
  border-radius: 16rpx;
  letter-spacing: 0.5rpx;
  text-transform: uppercase;
  font-size: 22rpx;
  border: 1px solid;
}

.status-text .level-safe {
  color: #059669;
  background: linear-gradient(135deg, rgba(16, 185, 129, 0.1), rgba(5, 150, 105, 0.1));
  border-color: rgba(16, 185, 129, 0.2);
}

.status-text .level-warning {
  color: #d97706;
  background: linear-gradient(135deg, rgba(245, 158, 11, 0.1), rgba(217, 119, 6, 0.1));
  border-color: rgba(245, 158, 11, 0.2);
}

.status-text .level-danger {
  color: #dc2626;
  background: linear-gradient(135deg, rgba(239, 68, 68, 0.1), rgba(220, 38, 38, 0.1));
  border-color: rgba(239, 68, 68, 0.2);
}

.additional-info {
  display: flex;
  justify-content: space-around;
  gap: 16rpx;
}

.info-item {
  text-align: center;
  flex: 1;
  padding: 16rpx;
  background: rgba(248, 250, 252, 0.6);
  border-radius: 12rpx;
  border: 1px solid rgba(226, 232, 240, 0.6);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.info-item:hover {
  background: rgba(255, 255, 255, 0.8);
  border-color: rgba(203, 213, 225, 0.8);
  transform: translateY(-1rpx);
  box-shadow: 0 2rpx 8rpx rgba(15, 23, 42, 0.06);
}

.info-label {
  display: block;
  font-size: 20rpx;
  color: #64748b;
  margin-bottom: 6rpx;
  font-weight: 500;
}

.info-value {
  display: block;
  font-size: 26rpx;
  color: #0f172a;
  font-weight: 700;
}

.card-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-top: 20rpx;
  border-top: 1px solid rgba(226, 232, 240, 0.6);
}

.trend-indicator {
  display: flex;
  align-items: center;
  gap: 8rpx;
  padding: 8rpx 16rpx;
  border-radius: 12rpx;
  font-size: 20rpx;
  font-weight: 600;
  border: 1px solid;
}

.trend-indicator.trend-up {
  background: linear-gradient(135deg, rgba(239, 68, 68, 0.1), rgba(220, 38, 38, 0.1));
  color: #dc2626;
  border-color: rgba(239, 68, 68, 0.2);
}

.trend-indicator.trend-down {
  background: linear-gradient(135deg, rgba(16, 185, 129, 0.1), rgba(5, 150, 105, 0.1));
  color: #059669;
  border-color: rgba(16, 185, 129, 0.2);
}

.trend-indicator.trend-stable {
  background: linear-gradient(135deg, rgba(245, 158, 11, 0.1), rgba(217, 119, 6, 0.1));
  color: #d97706;
  border-color: rgba(245, 158, 11, 0.2);
}

.trend-icon {
  font-size: 18rpx;
}

.trend-text {
  font-weight: 600;
}

.last-update {
  font-size: 20rpx;
  color: #94a3b8;
  font-weight: 500;
}

/* 响应式优化 */
@media (max-width: 750rpx) {
  .radiation-card {
    padding: 24rpx;
  }
  
  .value-number {
    font-size: 64rpx;
  }
  
  .additional-info {
    flex-direction: column;
    gap: 12rpx;
  }
  
  .card-footer {
    flex-direction: column;
    gap: 12rpx;
    align-items: flex-start;
  }
  
  .card-header {
    flex-wrap: wrap;
    gap: 12rpx;
  }
  
  .card-actions {
    order: 3;
    width: 100%;
    justify-content: center;
  }
}
</style> 