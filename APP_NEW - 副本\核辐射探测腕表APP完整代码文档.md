# 核辐射探测腕表配套APP代码整理文档

## 项目概述

本项目是一个基于uni-app框架开发的核辐射探测腕表配套移动应用程序，与基于CH579芯片的智能手表系统配合使用，实现辐射剂量的实时监测、数据分析、健康管理和智能预警功能。项目采用Vue 3 + Vite构建，支持H5、小程序、APP等多平台部署。

## 项目架构

```
APP_NEW/
├── src/                          # 源代码目录
│   ├── App.vue                   # 应用入口组件
│   ├── main.js                   # 应用入口文件
│   ├── pages.json                # 页面配置文件
│   ├── manifest.json             # 应用配置清单
│   ├── components/               # 公共组件目录
│   ├── pages/                    # 页面目录
│   ├── utils/                    # 工具类目录
│   ├── stores/                   # 状态存储目录
│   ├── styles/                   # 样式文件目录
│   └── static/                   # 静态资源目录
├── package.json                  # 项目依赖配置
├── vite.config.js               # Vite构建配置
└── 项目核心代码整理文档.md      # 嵌入式代码文档
```

## 1. 应用入口模块 (main.js & App.vue)

### 1.1 应用启动入口

```c
// main.js - 应用启动入口
import { createSSRApp } from "vue";
import App from "./App.vue";

export function createApp() {
    const app = createSSRApp(App);
    return { app };
}
```

### 1.2 应用根组件初始化
```c
// App.vue - 应用根组件
export default {
  name: 'App',
  setup() {
    onMounted(async () => {
      // 初始化响应式管理器
      try {
        await initResponsiveManager()
        console.log('响应式管理器已初始化')
      } catch (error) {
        console.error('响应式管理器初始化失败:', error)
      }

      // 初始化主题管理器
      try {
        themeManager.init()
        console.log('主题管理器已初始化')
      } catch (error) {
        console.error('主题管理器初始化失败:', error)
      }

      // 初始化MQTT连接
      try {
        mqttService.connect()
        mqttService.startSimulation()
        console.log('MQTT服务已启动')

        // 全局监听辐射警报消息
        mqttService.onMessage('radiationAlert', (alertData) => {
          console.log('收到辐射警报（全局）:', alertData)
        })

      } catch (error) {
        console.error('MQTT连接失败:', error)
      }
    })
  }
}
```

### 1.3 主要功能说明

- **系统初始化**: 完成响应式管理器、主题管理器、MQTT服务的初始化
- **服务启动**: 启动数据模拟和消息监听服务
- **错误处理**: 完善的错误捕获和日志记录机制

## 2. 数据状态管理模块 (dataStore.js)

### 2.1 数据状态定义

```c
// 辐射数据状态结构体
radiationState = {
    currentData: {
        doseRate: 0,          // 当前剂量率 (μSv/h)
        cps: 0,               // 计数率 (counts per second)
        doseSum: 0,           // 累积剂量 (μSv)
        alarmStatus: 0,       // 报警状态位
        temperature: 0,       // 温度 (°C)
        timestamp: Date.now()
    },
    history: [],              // 历史数据数组
    alerts: [],               // 报警记录数组
    settings: {
        maxDoseRate: 1.0,     // 剂量率报警阈值
        minDoseRate: 0.01,    // 最小剂量率阈值
        maxDoseSum: 100.0,    // 累积剂量报警阈值
        autoUpload: true,     // 自动上传开关
        uploadInterval: 60,   // 上传间隔(秒)
        soundAlert: true,     // 声音报警
        vibrationAlert: true  // 震动报警
    }
}

// 设备状态结构体
deviceState = {
    battery: {
        level: 85,            // 电池电量百分比
        charging: false,      // 充电状态
        voltage: 4.1         // 电池电压
    },
    connection: {
        mqtt: false,          // MQTT连接状态
        bluetooth: false,     // 蓝牙连接状态
        gps: false,          // GPS状态
        cellular: false       // 移动网络状态
    },
    deviceInfo: {
        imei: '',            // 设备IMEI
        iccid: '',           // SIM卡ICCID
        firmwareVersion: '',  // 固件版本
        serialNumber: ''     // 设备序列号
    }
}

// 位置数据状态结构体
locationState = {
    current: {
        latitude: 39.9042,    // 纬度
        longitude: 116.4074,  // 经度
        altitude: 50,         // 海拔
        accuracy: 10,         // 精度
        timestamp: Date.now()
    },
    history: [],             // 位置历史数组
    poi: []                  // 兴趣点数组
}

// 健康数据状态结构体
healthState = {
    current: {
        heartRate: 75,        // 心率 (bpm)
        spO2: 98,            // 血氧饱和度 (%)
        bodyTemp: 36.5,      // 体温 (°C)
        steps: 8543,         // 步数
        timestamp: Date.now()
    },
    history: [],             // 历史数据数组
    dailyStats: {
        avgHeartRate: 72,
        maxHeartRate: 95,
        minHeartRate: 58,
        avgSpO2: 97
    }
}

// 数据管理类
class DataStore {
  constructor() {
    this.maxHistoryLength = 1000
    this.storageKey = 'radiationAppData'
    this.loadFromStorage()
  }

  // 更新辐射数据
  updateRadiationData(data) {
    Object.assign(radiationState.currentData, {
      ...data,
      timestamp: Date.now()
    })
    
    this.addToHistory(radiationState.history, {...radiationState.currentData})
    this.checkAlerts(data)
    this.saveToStorage()
  }

  // 检查报警条件
  checkAlerts(data) {
    const { doseRate, doseSum, alarmStatus } = data
    const { maxDoseRate, minDoseRate, maxDoseSum } = radiationState.settings

    let alertType = null
    let alertMessage = ''
    let alertLevel = 'info'

    // 检查剂量率过高
    if (alarmStatus & 0x02) {
      alertType = 'high_dose_rate'
      alertMessage = `剂量率过高: ${doseRate.toFixed(2)} μSv/h`
      alertLevel = 'error'
    }
    // 检查剂量率过低
    else if (alarmStatus & 0x01) {
      alertType = 'low_dose_rate'
      alertMessage = `剂量率异常低: ${doseRate.toFixed(2)} μSv/h`
      alertLevel = 'warning'
    }
    // 检查累积剂量过高
    else if (alarmStatus & 0x04) {
      alertType = 'high_dose_sum'
      alertMessage = `累积剂量过高: ${doseSum.toFixed(2)} μSv`
      alertLevel = 'error'
    }

    if (alertType) {
      const alert = {
        type: alertType,
        message: alertMessage,
        level: alertLevel,
        doseRate,
        doseSum,
        timestamp: Date.now(),
        location: {...locationState.current}
      }

      radiationState.alerts.unshift(alert)
      
      // 限制报警记录数量
      if (radiationState.alerts.length > 100) {
        radiationState.alerts = radiationState.alerts.slice(0, 100)
      }

      this.showAlert(alert)
    }
  }

  // 显示报警通知
  showAlert(alert) {
    console.log('报警触发:', alert)

    // 震动反馈
    if (radiationState.settings.vibrationAlert) {
      try {
        if (typeof uni.vibrateShort === 'function') {
          uni.vibrateShort({
            success: () => console.log('振动成功'),
            fail: (err) => console.warn('振动失败:', err)
          })
        }
      } catch (error) {
        console.warn('振动API调用失败:', error)
      }
    }
  }

  // 保存到本地存储
  saveToStorage() {
    try {
      const data = {
        radiation: radiationState,
        device: deviceState,
        location: locationState,
        health: healthState
      }
      uni.setStorageSync(this.storageKey, JSON.stringify(data))
    } catch (error) {
      console.error('保存数据失败:', error)
    }
  }

  // 从本地存储加载
  loadFromStorage() {
    try {
      const data = uni.getStorageSync(this.storageKey)
      if (data) {
        const parsedData = JSON.parse(data)
        
        if (parsedData.radiation) {
          Object.assign(radiationState, parsedData.radiation)
        }
        if (parsedData.device) {
          Object.assign(deviceState, parsedData.device)
        }
        if (parsedData.location) {
          Object.assign(locationState, parsedData.location)
        }
        if (parsedData.health) {
          Object.assign(healthState, parsedData.health)
        }
      }
    } catch (error) {
      console.error('加载数据失败:', error)
    }
  }

  // 添加到历史记录
  addToHistory(historyArray, data) {
    historyArray.unshift(data)
    
    if (historyArray.length > this.maxHistoryLength) {
      historyArray.splice(this.maxHistoryLength)
    }
  }
}

export default new DataStore()
```

### 3. MQTT通信服务 (mqttService.js)

```javascript
import { radiationState, deviceState, healthState, locationState } from './dataStore.js'

class MqttService {
  constructor() {
    this.client = null
    this.isConnected = false
    this.topics = [
      'radiation/data',
      'radiation/alert', 
      'device/status',
      'health/data',
      'location/data'
    ]
    this.callbacks = new Map()
    this.simulationInterval = null
    this.lastUpdateTime = Date.now()
    this.totalDoseSum = 0
  }

  // 连接MQTT服务器（模拟）
  connect(options = {}) {
    const defaultOptions = {
      host: 'broker.emqx.io',
      port: 8083,
      protocol: 'ws',
      clientId: `radiation_app_${Math.random().toString(16).substr(2, 8)}`,
      username: '',
      password: '',
      clean: true,
      keepalive: 60,
      reconnectPeriod: 1000,
      ...options
    }

    console.log('正在连接MQTT服务器（模拟）...', defaultOptions)

    // 模拟连接过程
    setTimeout(() => {
      this.isConnected = true
      console.log('MQTT连接成功（模拟）')
      
      if (this.callbacks.has('connected')) {
        this.callbacks.get('connected')()
      }

      this.startSimulation()
    }, 1000)

    return this
  }

  // 开始模拟数据传输
  startSimulation() {
    if (this.simulationInterval) {
      clearInterval(this.simulationInterval)
    }

    this.totalDoseSum = radiationState.currentData.doseSum || 0
    this.lastUpdateTime = Date.now()

    this.simulationInterval = setInterval(() => {
      const now = Date.now()
      const timeDeltaHours = (now - this.lastUpdateTime) / (1000 * 60 * 60)
      
      // 模拟辐射数据
      const currentDoseRate = 0.1 + Math.random() * 0.05
      const doseIncrement = currentDoseRate * timeDeltaHours
      this.totalDoseSum += doseIncrement
      
      const radiationData = {
        doseRate: currentDoseRate,
        cps: 50 + Math.random() * 20,
        doseSum: this.totalDoseSum,
        alarmStatus: Math.random() > 0.98 ? 2 : 0, // 2%概率报警
        temperature: 25 + Math.random() * 5,
        timestamp: now
      }

      this.lastUpdateTime = now
      this.handleMessage('radiation/data', radiationData)

      // 如果有报警，发送报警消息
      if (radiationData.alarmStatus > 0) {
        const alertData = {
          type: 'high_dose_rate',
          level: 'error',
          message: `剂量率过高: ${radiationData.doseRate.toFixed(2)} μSv/h`,
          doseRate: radiationData.doseRate,
          doseSum: radiationData.doseSum,
          timestamp: now
        }
        this.handleMessage('radiation/alert', alertData)
      }

      // 模拟设备状态数据
      const deviceData = {
        battery: {
          level: Math.max(20, 100 - Math.floor(now / 100000) % 80),
          charging: Math.random() > 0.8,
          voltage: 3.7 + Math.random() * 0.6
        },
        connection: {
          mqtt: true,
          bluetooth: Math.random() > 0.1,
          gps: Math.random() > 0.05,
          cellular: Math.random() > 0.1
        }
      }
      this.handleMessage('device/status', deviceData)

      // 模拟健康数据
      const healthData = {
        heartRate: 70 + Math.floor(Math.random() * 30),
        spO2: 95 + Math.floor(Math.random() * 5),
        bodyTemp: 36.0 + Math.random() * 1.5,
        steps: Math.floor(Math.random() * 10000),
        timestamp: now
      }
      this.handleMessage('health/data', healthData)

    }, 2000) // 每2秒更新一次
  }

  // 处理接收到的消息
  handleMessage(topic, data) {
    console.log(`收到消息 [${topic}]:`, data)

    // 根据主题处理不同类型的数据
    switch (topic) {
      case 'radiation/data':
        dataStore.updateRadiationData(data)
        break
      case 'device/status':
        dataStore.updateDeviceStatus(data)
        break
      case 'health/data':
        dataStore.updateHealthData(data)
        break
      case 'radiation/alert':
        // 报警消息已在dataStore中处理
        break
    }

    // 触发注册的回调函数
    if (this.callbacks.has(topic)) {
      this.callbacks.get(topic)(data)
    }
  }

  // 注册消息回调
  onMessage(topic, callback) {
    this.callbacks.set(topic, callback)
  }

  // 发布消息
  publish(topic, message) {
    if (!this.isConnected) {
      console.warn('MQTT未连接，无法发布消息')
      return false
    }

    console.log(`发布消息到 [${topic}]:`, message)
    return true
  }

  // 订阅主题
  subscribe(topic) {
    if (!this.isConnected) {
      console.warn('MQTT未连接，无法订阅主题')
      return false
    }

    console.log(`订阅主题: ${topic}`)
    return true
  }

  // 断开连接
  disconnect() {
    if (this.simulationInterval) {
      clearInterval(this.simulationInterval)
      this.simulationInterval = null
    }
    
    this.isConnected = false
    console.log('MQTT连接已断开')
  }

  // 获取连接状态
  getConnectionStatus() {
    return {
      connected: this.isConnected,
      topics: this.topics,
      callbacks: Array.from(this.callbacks.keys())
    }
  }
}

export default new MqttService()
```

### 4. 智能预测算法 (predictionAlgorithm.js)

```javascript
/**
 * 高级ARIMA+LSTM混合预测算法
 * 基于时间序列分析、深度学习和多因素环境建模的复合预测模型
 */
export class RadiationPredictionModel {
  constructor() {
    this.historicalData = []
    this.environmentalData = []
    this.deviceMetrics = []

    this.modelParameters = {
      trendWeight: 0.3,      // 趋势权重
      seasonalWeight: 0.25,  // 季节性权重
      randomWeight: 0.2,     // 随机性权重
      arimaWeight: 0.35,     // ARIMA权重
      lstmWeight: 0.45,      // LSTM权重
      environmentalWeight: 0.15, // 环境因素权重
      errorMargin: 0.03,     // 3%误差带
      smoothingFactor: 0.25, // 指数平滑因子
      predictionDays: 7      // 预测天数
    }

    this.modelState = {
      isInitialized: false,
      lastUpdate: null,
      trendSlope: 0,
      seasonalPatterns: {},
      volatility: 0,
      // ARIMA模型参数
      arimaParams: {
        p: 3, d: 1, q: 2,
        coefficients: [],
        residuals: []
      },
      // LSTM模型参数
      lstmWeights: {
        inputGate: null,
        forgetGate: null,
        outputGate: null,
        cellState: null,
        hiddenState: null
      },
      // 环境因素影响系数
      environmentalFactors: {
        temperatureCoeff: 0.001,
        humidityCoeff: 0.0005,
        pressureCoeff: 0.0002,
        windSpeedCoeff: 0.0001
      },
      // 模型性能指标
      performance: {
        mse: 0,
        mae: 0,
        r2: 0,
        confidence: 0.8
      }
    }
  }

  /**
   * 初始化模型并训练
   * @param {Array} historicalData - 历史数据数组
   */
  initializeModel(historicalData) {
    if (!historicalData || historicalData.length < 10) {
      throw new Error('需要至少10个历史数据点来初始化预测模型')
    }

    this.historicalData = this.preprocessData(historicalData)
    this.trainModel()
    this.modelState.isInitialized = true
    this.modelState.lastUpdate = new Date()

    console.log('预测模型初始化完成，数据点数量:', this.historicalData.length)
  }

  /**
   * 数据预处理（优化版 - 减少数据点数）
   * @param {Array} rawData - 原始数据
   * @returns {Array} 处理后的数据
   */
  preprocessData(rawData) {
    // 数据清洗和标准化
    const cleanedData = rawData
      .filter(item => item && typeof item.doseRate === 'number' && !isNaN(item.doseRate))
      .map(item => ({
        timestamp: new Date(item.timestamp),
        doseRate: item.doseRate,
        cps: item.cps || 0
      }))
      .sort((a, b) => a.timestamp - b.timestamp)

    // 数据抽样 - 每2个数据点取1个，减少一半数据量
    const sampledData = cleanedData.filter((_, index) => index % 2 === 0)

    // 异常值检测和处理
    return this.removeOutliers(sampledData)
  }

  /**
   * 异常值检测和移除
   * @param {Array} data - 数据数组
   * @returns {Array} 处理后的数据
   */
  removeOutliers(data) {
    if (data.length < 4) return data

    const values = data.map(item => item.doseRate)
    const q1 = this.quantile(values, 0.25)
    const q3 = this.quantile(values, 0.75)
    const iqr = q3 - q1
    const lowerBound = q1 - 1.5 * iqr
    const upperBound = q3 + 1.5 * iqr

    return data.filter(item =>
      item.doseRate >= lowerBound && item.doseRate <= upperBound
    )
  }

  /**
   * 计算分位数
   * @param {Array} arr - 数值数组
   * @param {number} q - 分位数 (0-1)
   * @returns {number} 分位数值
   */
  quantile(arr, q) {
    const sorted = [...arr].sort((a, b) => a - b)
    const pos = (sorted.length - 1) * q
    const base = Math.floor(pos)
    const rest = pos - base

    if (sorted[base + 1] !== undefined) {
      return sorted[base] + rest * (sorted[base + 1] - sorted[base])
    } else {
      return sorted[base]
    }
  }

  /**
   * 训练高级混合预测模型
   */
  trainModel() {
    this.calculateTrend()
    this.extractSeasonalPatterns()
    this.calculateVolatility()

    // 训练ARIMA模型
    this.trainARIMAModel()

    // 训练LSTM模型
    this.trainLSTMModel()

    // 分析环境因素影响
    this.analyzeEnvironmentalFactors()

    // 计算模型性能
    this.calculateModelPerformance()
  }

  /**
   * 生成预测数据（优化版 - 减少数据点数一半）
   * @param {number} predictionHours - 预测小时数，默认84小时（3.5天）
   * @returns {Object} 包含预测数据和误差带的对象
   */
  generatePrediction(predictionHours = 84) {
    if (!this.modelState.isInitialized) {
      throw new Error('模型尚未初始化，请先调用 initializeModel()')
    }

    const lastDataPoint = this.historicalData[this.historicalData.length - 1]
    const startTime = lastDataPoint.timestamp.getTime()
    const timeScale = 2 * 60 * 60 * 1000 // 2小时毫秒数（减少一半数据点）

    const predictionData = []
    const upperBoundData = []
    const lowerBoundData = []

    // 计算基准值
    const baseValue = this.calculateBaseValue()

    // 减少循环次数，每2小时一个数据点
    for (let step = 1; step <= predictionHours; step++) {
      const predictionTime = new Date(startTime + step * timeScale)
      const actualHour = step * 2 // 实际小时数
      const predictedValue = this.predictSinglePoint(predictionTime, baseValue, actualHour)

      // 计算误差带
      const errorBand = this.calculateErrorBand(predictedValue, actualHour)

      predictionData.push({
        timestamp: predictionTime,
        doseRate: predictedValue,
        cps: Math.round(predictedValue * 1000) // 简单的CPS估算
      })

      upperBoundData.push({
        timestamp: predictionTime,
        doseRate: predictedValue + errorBand
      })

      lowerBoundData.push({
        timestamp: predictionTime,
        doseRate: Math.max(0, predictedValue - errorBand) // 确保不为负值
      })
    }

    return {
      prediction: predictionData,
      upperBound: upperBoundData,
      lowerBound: lowerBoundData,
      confidence: this.calculateConfidence(),
      dataPointInterval: 2 // 数据点间隔（小时）
    }
  }

  /**
   * 计算基准值
   * @returns {number} 基准值
   */
  calculateBaseValue() {
    const recentData = this.historicalData.slice(-24) // 最近24小时
    return recentData.reduce((sum, item) => sum + item.doseRate, 0) / recentData.length
  }

  /**
   * 高级混合预测单个时间点的值（ARIMA+LSTM）
   * @param {Date} predictionTime - 预测时间
   * @param {number} baseValue - 基准值
   * @param {number} hoursAhead - 提前小时数
   * @returns {number} 预测值
   */
  predictSinglePoint(predictionTime, baseValue, hoursAhead) {
    const hour = predictionTime.getHours()
    const dayOfWeek = predictionTime.getDay()

    // 修正趋势分量 - 添加衰减因子避免无限增长
    const trendDecayFactor = Math.exp(-hoursAhead / 48) // 48小时衰减
    const trendComponent = this.modelState.trendSlope * hoursAhead * trendDecayFactor

    const hourlyPattern = this.modelState.seasonalPatterns.hourly[hour] || 0
    const dailyPattern = this.modelState.seasonalPatterns.daily[dayOfWeek] || 0
    const seasonalComponent = (hourlyPattern + dailyPattern) * this.modelParameters.seasonalWeight
    const randomComponent = this.generateRandomComponent(hoursAhead)

    // ARIMA预测分量
    const arimaPrediction = this.predictARIMA(hoursAhead)

    // LSTM预测分量
    const lstmPrediction = this.predictLSTM(hoursAhead)

    // 环境因素分量
    const environmentalComponent = this.predictEnvironmentalImpact(predictionTime)

    // 修正混合预测 - 增加基准值权重，减少趋势权重
    let prediction =
      baseValue * 0.4 + // 增加基准值权重
      (trendComponent * this.modelParameters.trendWeight + seasonalComponent) * 0.1 + // 减少趋势权重
      arimaPrediction * this.modelParameters.arimaWeight +
      lstmPrediction * this.modelParameters.lstmWeight +
      environmentalComponent * this.modelParameters.environmentalWeight +
      randomComponent * this.modelParameters.randomWeight

    // 高级平滑处理 - 增强回归到均值的效果
    if (hoursAhead > 1) {
      const smoothingFactor = Math.exp(-hoursAhead / 12) // 更快的回归到均值
      const meanValue = this.calculateHistoricalMean()
      prediction = meanValue * (1 - smoothingFactor) + prediction * smoothingFactor
    }

    // 确保预测值在合理范围内，并限制与基准值的偏差
    const maxDeviation = baseValue * 0.5 // 最大偏差为基准值的50%
    prediction = Math.max(baseValue - maxDeviation, Math.min(baseValue + maxDeviation, prediction))

    return Math.max(0.001, prediction)
  }

  /**
   * 计算历史数据均值
   * @returns {number} 历史均值
   */
  calculateHistoricalMean() {
    if (this.historicalData.length === 0) return 0.1

    const values = this.historicalData.map(item => item.doseRate)
    return values.reduce((sum, val) => sum + val, 0) / values.length
  }

  /**
   * 计算预测置信度
   * @returns {number} 置信度 (0-1)
   */
  calculateConfidence() {
    const dataQuality = Math.min(this.historicalData.length / 100, 1) // 数据量质量
    const volatilityPenalty = Math.max(0, 1 - this.modelState.volatility * 5) // 波动性惩罚
    const trendStability = Math.abs(this.modelState.trendSlope) < 0.001 ? 0.9 : 0.7 // 趋势稳定性

    return (dataQuality * 0.4 + volatilityPenalty * 0.4 + trendStability * 0.2)
  }
}

/**
 * 创建预测模型实例的工厂函数
 * @returns {RadiationPredictionModel} 预测模型实例
 */
export function createPredictionModel() {
  return new RadiationPredictionModel()
}
```

### 5. 主要页面组件

#### 5.1 仪表盘页面 (dashboard.vue)

```vue
<template>
  <view class="container">
    <!-- Toast容器 -->
    <ToastContainer />

    <!-- 现代化顶部头部 -->
    <view class="modern-header">
      <view class="header-background">
        <view class="header-gradient"></view>
      </view>
      <view class="header-content">
        <view class="header-left">
          <view class="app-logo">
            <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2.5">
              <path d="M12 2v20M17 5H9.5a3.5 3.5 0 0 0 0 7h5a3.5 3.5 0 0 1 0 7H6"></path>
            </svg>
          </view>
          <view class="header-info">
            <text class="app-title">辐射监测中心</text>
            <text class="app-subtitle">{{ currentTime }} • 实时监控</text>
          </view>
        </view>
        <view class="header-right">
          <view class="status-indicators">
            <view class="connection-status" :class="{ connected: deviceState.isConnected }">
              <view class="status-dot"></view>
            </view>
            <view class="notification-button" @click="navigateToNotification">
              <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                <path d="M18 8A6 6 0 0 0 6 8c0 7-3 9-3 9h18s-3-2-3-9"></path>
                <path d="M13.73 21a2 2 0 0 1-3.46 0"></path>
              </svg>
              <view class="notification-badge" v-if="hasUnreadNotifications">{{ unreadCount }}</view>
            </view>
          </view>
        </view>
      </view>
    </view>

    <!-- 重新设计的实时辐射监测卡片 -->
    <view class="radiation-monitoring-card-redesigned premium-card morphing-card">
      <!-- 卡片头部 -->
      <view class="card-header-redesigned">
        <view class="header-left-section">
          <view class="radiation-icon-large premium-icon orbital-animation" :class="radiationLevelClass">
            <view class="icon-core">
              <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2.5">
                <circle cx="12" cy="12" r="10"></circle>
                <circle cx="12" cy="12" r="6"></circle>
                <circle cx="12" cy="12" r="2"></circle>
              </svg>
            </view>
            <view class="orbital-ring ring-1"></view>
            <view class="orbital-ring ring-2"></view>
            <view class="orbital-ring ring-3"></view>
          </view>
          <view class="header-text-section">
            <text class="main-title typewriter-text">实时监测</text>
            <text class="sub-title">{{ radiationStatusText }} • {{ currentTime }}</text>
          </view>
        </view>

        <view class="status-indicators-redesigned">
          <view class="status-badge premium-badge morphing-badge" :class="deviceState.connection.mqtt ? 'online' : 'offline'">
            <view class="badge-glow"></view>
            <text>{{ deviceState.connection.mqtt ? '在线' : '离线' }}</text>
          </view>
          <view class="status-badge premium-badge morphing-badge" :class="isCollecting ? 'monitoring' : 'paused'">
            <view class="badge-glow"></view>
            <text>{{ isCollecting ? '监测中' : '已暂停' }}</text>
          </view>
        </view>
      </view>

      <!-- 主要数据显示区域 -->
      <view class="main-data-section">
        <!-- 中央大数值显示 -->
        <view class="central-value-display premium-display">
          <view class="value-container">
            <view class="value-backdrop"></view>
            <text class="dose-rate-number premium-number floating-number" :class="radiationLevelClass">
              {{ formatDoseRate(radiationState.currentData.doseRate) }}
            </text>
            <view class="number-particles">
              <view class="particle particle-1"></view>
              <view class="particle particle-2"></view>
              <view class="particle particle-3"></view>
            </view>
          </view>
          <text class="dose-rate-unit premium-unit">μSv/h</text>
        </view>

        <!-- 关键信息网格 -->
        <view class="info-grid-redesigned">
          <view class="info-card">
            <view class="info-icon shield">
              <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                <path d="M12 22s8-4 8-10V5l-8-3-8 3v7c0 6 8 10 8 10z"></path>
                <path d="M9 12l2 2 4-4"></path>
              </svg>
            </view>
            <view class="info-content">
              <text class="info-label">安全状态</text>
              <text class="info-value" :class="radiationLevelClass">{{ radiationStatusText }}</text>
            </view>
          </view>

          <view class="info-card">
            <view class="info-icon activity">
              <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                <polyline points="22,12 18,12 15,21 9,3 6,12 2,12"></polyline>
              </svg>
            </view>
            <view class="info-content">
              <text class="info-label">计数率</text>
              <text class="info-value">{{ formatCps(radiationState.currentData.cps) }} CPS</text>
            </view>
          </view>

          <view class="info-card">
            <view class="info-icon cumulative">
              <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                <path d="M21 16V8a2 2 0 0 0-1-1.73l-7-4a2 2 0 0 0-2 0l-7 4A2 2 0 0 0 3 8v8a2 2 0 0 0 1 1.73l7 4a2 2 0 0 0 2 0l7-4A2 2 0 0 0 21 16z"></path>
                <polyline points="3.27,6.96 12,12.01 20.73,6.96"></polyline>
                <line x1="12" y1="22.08" x2="12" y2="12"></line>
              </svg>
            </view>
            <view class="info-content">
              <text class="info-label">累积剂量</text>
              <text class="info-value">{{ formatDoseSum(radiationState.currentData.doseSum) }} μSv</text>
            </view>
          </view>

          <view class="info-card">
            <view class="info-icon temperature">
              <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                <path d="M14 4v10.54a4 4 0 1 1-4 0V4a2 2 0 0 1 4 0Z"></path>
              </svg>
            </view>
            <view class="info-content">
              <text class="info-label">环境温度</text>
              <text class="info-value">{{ formatTemperature(radiationState.currentData.temperature) }}°C</text>
            </view>
          </view>
        </view>
      </view>
    </view>

    <!-- 设备状态卡片 -->
    <view class="device-status-card premium-card">
      <view class="card-header">
        <view class="card-title">
          <view class="title-icon">
            <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
              <rect x="2" y="3" width="20" height="14" rx="2" ry="2"></rect>
              <line x1="8" y1="21" x2="16" y2="21"></line>
              <line x1="12" y1="17" x2="12" y2="21"></line>
            </svg>
          </view>
          <text>设备状态</text>
        </view>
      </view>

      <view class="device-info-grid">
        <!-- 电池状态 -->
        <view class="device-info-item">
          <view class="device-icon battery" :class="batteryLevelClass">
            <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
              <rect x="1" y="6" width="18" height="12" rx="2" ry="2"></rect>
              <line x1="23" y1="13" x2="23" y2="11"></line>
            </svg>
            <view class="battery-level" :style="{ width: deviceState.battery.level + '%' }"></view>
          </view>
          <view class="device-info-content">
            <text class="device-label">电池电量</text>
            <text class="device-value">{{ deviceState.battery.level }}%</text>
            <text class="device-status" :class="batteryStatusClass">
              {{ deviceState.battery.charging ? '充电中' : '使用中' }}
            </text>
          </view>
        </view>

        <!-- 连接状态 -->
        <view class="device-info-item">
          <view class="device-icon connection" :class="connectionStatusClass">
            <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
              <path d="M5 12.55a11 11 0 0 1 14.08 0"></path>
              <path d="M1.42 9a16 16 0 0 1 21.16 0"></path>
              <path d="M8.53 16.11a6 6 0 0 1 6.95 0"></path>
              <line x1="12" y1="20" x2="12.01" y2="20"></line>
            </svg>
          </view>
          <view class="device-info-content">
            <text class="device-label">网络连接</text>
            <text class="device-value">{{ connectionStatusText }}</text>
            <text class="device-status" :class="connectionStatusClass">
              {{ deviceState.connection.mqtt ? '已连接' : '未连接' }}
            </text>
          </view>
        </view>
      </view>
    </view>

    <!-- 底部导航 -->
    <BottomNavigation />
  </view>
</template>

<script>
import { ref, computed, onMounted, onUnmounted } from 'vue'
import { radiationState, deviceState } from '@/utils/dataStore.js'
import mqttService from '@/utils/mqttService.js'
import ToastContainer from '@/components/ToastContainer.vue'
import BottomNavigation from '@/components/BottomNavigation.vue'

export default {
  name: 'Dashboard',
  components: {
    ToastContainer,
    BottomNavigation
  },
  setup() {
    const currentTime = ref('')
    const isCollecting = ref(true)
    const hasUnreadNotifications = ref(false)
    const unreadCount = ref(0)

    // 计算属性
    const radiationLevelClass = computed(() => {
      const doseRate = radiationState.currentData.doseRate
      if (doseRate > 1.0) return 'danger'
      if (doseRate > 0.5) return 'warning'
      return 'safe'
    })

    const radiationStatusText = computed(() => {
      const doseRate = radiationState.currentData.doseRate
      if (doseRate > 1.0) return '高辐射'
      if (doseRate > 0.5) return '中等辐射'
      return '安全'
    })

    const batteryLevelClass = computed(() => {
      const level = deviceState.battery.level
      if (level > 50) return 'high'
      if (level > 20) return 'medium'
      return 'low'
    })

    const batteryStatusClass = computed(() => {
      return deviceState.battery.charging ? 'charging' : 'normal'
    })

    const connectionStatusClass = computed(() => {
      return deviceState.connection.mqtt ? 'connected' : 'disconnected'
    })

    const connectionStatusText = computed(() => {
      const connections = []
      if (deviceState.connection.mqtt) connections.push('MQTT')
      if (deviceState.connection.bluetooth) connections.push('蓝牙')
      if (deviceState.connection.gps) connections.push('GPS')
      if (deviceState.connection.cellular) connections.push('移动网络')

      return connections.length > 0 ? connections.join(', ') : '无连接'
    })

    // 格式化函数
    const formatDoseRate = (value) => {
      return (value || 0).toFixed(2)
    }

    const formatCps = (value) => {
      return Math.round(value || 0)
    }

    const formatDoseSum = (value) => {
      return (value || 0).toFixed(3)
    }

    const formatTemperature = (value) => {
      return (value || 0).toFixed(1)
    }

    // 更新当前时间
    const updateCurrentTime = () => {
      const now = new Date()
      currentTime.value = now.toLocaleTimeString('zh-CN', {
        hour: '2-digit',
        minute: '2-digit'
      })
    }

    // 导航到通知页面
    const navigateToNotification = () => {
      uni.navigateTo({
        url: '/pages/notification/notification'
      })
    }

    // 生命周期
    let timeInterval = null

    onMounted(() => {
      updateCurrentTime()
      timeInterval = setInterval(updateCurrentTime, 1000)

      // 检查未读通知
      if (radiationState.alerts.length > 0) {
        hasUnreadNotifications.value = true
        unreadCount.value = radiationState.alerts.length
      }
    })

    onUnmounted(() => {
      if (timeInterval) {
        clearInterval(timeInterval)
      }
    })

    return {
      // 响应式数据
      radiationState,
      deviceState,
      currentTime,
      isCollecting,
      hasUnreadNotifications,
      unreadCount,

      // 计算属性
      radiationLevelClass,
      radiationStatusText,
      batteryLevelClass,
      batteryStatusClass,
      connectionStatusClass,
      connectionStatusText,

      // 方法
      formatDoseRate,
      formatCps,
      formatDoseSum,
      formatTemperature,
      navigateToNotification
    }
  }
}
</script>

<style lang="scss" scoped>
.container {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 0;
  position: relative;
  overflow-x: hidden;
}

.modern-header {
  position: relative;
  padding: 60rpx 40rpx 40rpx;
  margin-bottom: 20rpx;

  .header-background {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;

    .header-gradient {
      width: 100%;
      height: 100%;
      background: linear-gradient(135deg, rgba(102, 126, 234, 0.9) 0%, rgba(118, 75, 162, 0.9) 100%);
      backdrop-filter: blur(20px);
    }
  }

  .header-content {
    position: relative;
    display: flex;
    justify-content: space-between;
    align-items: center;
    z-index: 2;

    .header-left {
      display: flex;
      align-items: center;

      .app-logo {
        width: 80rpx;
        height: 80rpx;
        background: rgba(255, 255, 255, 0.2);
        border-radius: 20rpx;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-right: 24rpx;

        svg {
          width: 40rpx;
          height: 40rpx;
          color: #ffffff;
        }
      }

      .header-info {
        .app-title {
          display: block;
          font-size: 36rpx;
          font-weight: 700;
          color: #ffffff;
          line-height: 1.2;
        }

        .app-subtitle {
          display: block;
          font-size: 24rpx;
          color: rgba(255, 255, 255, 0.8);
          margin-top: 4rpx;
        }
      }
    }

    .header-right {
      .status-indicators {
        display: flex;
        align-items: center;
        gap: 16rpx;

        .connection-status {
          width: 40rpx;
          height: 40rpx;
          border-radius: 50%;
          background: rgba(255, 255, 255, 0.2);
          display: flex;
          align-items: center;
          justify-content: center;

          .status-dot {
            width: 16rpx;
            height: 16rpx;
            border-radius: 50%;
            background: #ff4757;
            animation: pulse 2s infinite;
          }

          &.connected .status-dot {
            background: #2ed573;
          }
        }

        .notification-button {
          position: relative;
          width: 40rpx;
          height: 40rpx;
          border-radius: 50%;
          background: rgba(255, 255, 255, 0.2);
          display: flex;
          align-items: center;
          justify-content: center;

          svg {
            width: 20rpx;
            height: 20rpx;
            color: #ffffff;
          }

          .notification-badge {
            position: absolute;
            top: -8rpx;
            right: -8rpx;
            min-width: 32rpx;
            height: 32rpx;
            background: #ff4757;
            border-radius: 16rpx;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 20rpx;
            color: #ffffff;
            font-weight: 600;
            padding: 0 8rpx;
          }
        }
      }
    }
  }
}

.radiation-monitoring-card-redesigned {
  margin: 0 30rpx 30rpx;
  padding: 40rpx;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  border-radius: 32rpx;
  border: 1px solid rgba(255, 255, 255, 0.3);
  box-shadow: 0 20rpx 60rpx rgba(0, 0, 0, 0.1);

  .card-header-redesigned {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 40rpx;

    .header-left-section {
      display: flex;
      align-items: center;

      .radiation-icon-large {
        position: relative;
        width: 120rpx;
        height: 120rpx;
        margin-right: 32rpx;

        .icon-core {
          position: absolute;
          top: 50%;
          left: 50%;
          transform: translate(-50%, -50%);
          width: 60rpx;
          height: 60rpx;
          background: linear-gradient(135deg, #00b4d8, #0096c7);
          border-radius: 50%;
          display: flex;
          align-items: center;
          justify-content: center;
          z-index: 3;

          svg {
            width: 32rpx;
            height: 32rpx;
            color: #ffffff;
          }
        }

        .orbital-ring {
          position: absolute;
          border: 2rpx solid rgba(0, 180, 216, 0.3);
          border-radius: 50%;
          animation: rotate 10s linear infinite;

          &.ring-1 {
            width: 80rpx;
            height: 80rpx;
            top: 20rpx;
            left: 20rpx;
            animation-duration: 8s;
          }

          &.ring-2 {
            width: 100rpx;
            height: 100rpx;
            top: 10rpx;
            left: 10rpx;
            animation-duration: 12s;
            animation-direction: reverse;
          }

          &.ring-3 {
            width: 120rpx;
            height: 120rpx;
            top: 0;
            left: 0;
            animation-duration: 15s;
          }
        }

        &.danger {
          .icon-core {
            background: linear-gradient(135deg, #ff4757, #ff3742);
          }

          .orbital-ring {
            border-color: rgba(255, 71, 87, 0.4);
          }
        }

        &.warning {
          .icon-core {
            background: linear-gradient(135deg, #ffa502, #ff9500);
          }

          .orbital-ring {
            border-color: rgba(255, 165, 2, 0.4);
          }
        }
      }

      .header-text-section {
        .main-title {
          display: block;
          font-size: 48rpx;
          font-weight: 800;
          color: #1a1a1a;
          line-height: 1.2;
          margin-bottom: 8rpx;
        }

        .sub-title {
          display: block;
          font-size: 28rpx;
          color: #666;
          font-weight: 500;
        }
      }
    }

    .status-indicators-redesigned {
      display: flex;
      flex-direction: column;
      gap: 16rpx;

      .status-badge {
        padding: 16rpx 24rpx;
        border-radius: 20rpx;
        font-size: 24rpx;
        font-weight: 600;
        text-align: center;
        position: relative;
        overflow: hidden;

        &.online {
          background: linear-gradient(135deg, #2ed573, #1dd1a1);
          color: #ffffff;
        }

        &.offline {
          background: linear-gradient(135deg, #ff4757, #ff3742);
          color: #ffffff;
        }

        &.monitoring {
          background: linear-gradient(135deg, #00b4d8, #0096c7);
          color: #ffffff;
        }

        &.paused {
          background: linear-gradient(135deg, #ffa502, #ff9500);
          color: #ffffff;
        }
      }
    }
  }

  .main-data-section {
    .central-value-display {
      text-align: center;
      margin-bottom: 40rpx;
      position: relative;

      .value-container {
        position: relative;
        display: inline-block;

        .value-backdrop {
          position: absolute;
          top: -20rpx;
          left: -40rpx;
          right: -40rpx;
          bottom: -20rpx;
          background: linear-gradient(135deg, rgba(0, 180, 216, 0.1), rgba(0, 150, 199, 0.1));
          border-radius: 32rpx;
          z-index: 1;
        }

        .dose-rate-number {
          position: relative;
          font-size: 120rpx;
          font-weight: 900;
          color: #00b4d8;
          line-height: 1;
          z-index: 2;

          &.danger {
            color: #ff4757;
          }

          &.warning {
            color: #ffa502;
          }
        }

        .number-particles {
          position: absolute;
          top: 0;
          left: 0;
          right: 0;
          bottom: 0;
          pointer-events: none;

          .particle {
            position: absolute;
            width: 8rpx;
            height: 8rpx;
            background: rgba(0, 180, 216, 0.6);
            border-radius: 50%;
            animation: float 3s ease-in-out infinite;

            &.particle-1 {
              top: 20%;
              left: 10%;
              animation-delay: 0s;
            }

            &.particle-2 {
              top: 60%;
              right: 15%;
              animation-delay: 1s;
            }

            &.particle-3 {
              bottom: 20%;
              left: 20%;
              animation-delay: 2s;
            }
          }
        }
      }

      .dose-rate-unit {
        display: block;
        font-size: 32rpx;
        font-weight: 600;
        color: #666;
        margin-top: 16rpx;
      }
    }

    .info-grid-redesigned {
      display: grid;
      grid-template-columns: 1fr 1fr;
      gap: 24rpx;

      .info-card {
        background: rgba(248, 250, 252, 0.8);
        border-radius: 24rpx;
        padding: 32rpx;
        border: 1px solid rgba(226, 232, 240, 0.8);
        transition: all 0.3s ease;

        &:hover {
          transform: translateY(-4rpx);
          box-shadow: 0 12rpx 32rpx rgba(0, 0, 0, 0.1);
        }

        .info-icon {
          width: 60rpx;
          height: 60rpx;
          border-radius: 16rpx;
          display: flex;
          align-items: center;
          justify-content: center;
          margin-bottom: 20rpx;

          svg {
            width: 32rpx;
            height: 32rpx;
            color: #ffffff;
          }

          &.shield {
            background: linear-gradient(135deg, #2ed573, #1dd1a1);
          }

          &.activity {
            background: linear-gradient(135deg, #00b4d8, #0096c7);
          }

          &.cumulative {
            background: linear-gradient(135deg, #8b5cf6, #7c3aed);
          }

          &.temperature {
            background: linear-gradient(135deg, #ffa502, #ff9500);
          }
        }

        .info-content {
          .info-label {
            display: block;
            font-size: 24rpx;
            color: #666;
            font-weight: 500;
            margin-bottom: 8rpx;
          }

          .info-value {
            display: block;
            font-size: 32rpx;
            font-weight: 700;
            color: #1a1a1a;

            &.danger {
              color: #ff4757;
            }

            &.warning {
              color: #ffa502;
            }

            &.safe {
              color: #2ed573;
            }
          }
        }
      }
    }
  }
}

@keyframes pulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.5; }
}

@keyframes rotate {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

@keyframes float {
  0%, 100% { transform: translateY(0px); }
  50% { transform: translateY(-20rpx); }
}
</style>
```

#### 5.2 辐射数据卡片组件 (RadiationCard.vue)

```vue
<template>
  <view class="radiation-card" :class="levelClass">
    <view class="card-header">
      <view class="status-indicator" :class="levelClass">
        <text class="indicator-icon">{{ statusIcon }}</text>
      </view>
      <text class="card-title">{{ title }}</text>
      <view class="card-actions" v-if="showActions">
        <text class="action-btn" @tap="$emit('export')">📤</text>
        <text class="action-btn" @tap="$emit('settings')">⚙️</text>
      </view>
    </view>

    <view class="card-content">
      <view class="main-value">
        <text class="value-number">{{ formatValue(value) }}</text>
        <text class="value-unit">{{ unit }}</text>
      </view>

      <view class="status-text">
        <text :class="levelClass">{{ statusText }}</text>
      </view>

      <view class="additional-info" v-if="additionalData">
        <view class="info-item" v-for="(item, key) in additionalData" :key="key">
          <text class="info-label">{{ item.label }}</text>
          <text class="info-value">{{ item.value }} {{ item.unit }}</text>
        </view>
      </view>
    </view>

    <view class="card-footer" v-if="showTrend">
      <view class="trend-indicator" :class="trendClass">
        <text class="trend-icon">{{ trendIcon }}</text>
        <text class="trend-text">{{ trendText }}</text>
      </view>
      <text class="last-update">{{ lastUpdateText }}</text>
    </view>
  </view>
</template>

<script>
import { computed } from 'vue'

export default {
  name: 'RadiationCard',
  props: {
    title: {
      type: String,
      default: '辐射监测'
    },
    value: {
      type: Number,
      required: true
    },
    unit: {
      type: String,
      default: 'μSv/h'
    },
    level: {
      type: String,
      default: 'safe' // safe, warning, danger
    },
    additionalData: {
      type: Object,
      default: null
    },
    showActions: {
      type: Boolean,
      default: false
    },
    showTrend: {
      type: Boolean,
      default: true
    },
    trend: {
      type: String,
      default: 'stable' // up, down, stable
    },
    trendValue: {
      type: Number,
      default: 0
    },
    lastUpdate: {
      type: Number,
      default: Date.now
    }
  },
  emits: ['export', 'settings'],
  setup(props) {
    const levelClass = computed(() => `level-${props.level}`)

    const statusIcon = computed(() => {
      switch (props.level) {
        case 'danger': return '🚨'
        case 'warning': return '⚠️'
        default: return '✅'
      }
    })

    const statusText = computed(() => {
      switch (props.level) {
        case 'danger': return '高辐射警告'
        case 'warning': return '中等辐射'
        default: return '安全范围'
      }
    })

    const trendClass = computed(() => `trend-${props.trend}`)

    const trendIcon = computed(() => {
      switch (props.trend) {
        case 'up': return '📈'
        case 'down': return '📉'
        default: return '➡️'
      }
    })

    const trendText = computed(() => {
      const absValue = Math.abs(props.trendValue)
      const direction = props.trend === 'up' ? '上升' : props.trend === 'down' ? '下降' : '稳定'
      return absValue > 0 ? `${direction} ${absValue.toFixed(2)}%` : '稳定'
    })

    const lastUpdateText = computed(() => {
      const now = Date.now()
      const diff = now - props.lastUpdate
      const minutes = Math.floor(diff / 60000)

      if (minutes < 1) return '刚刚更新'
      if (minutes < 60) return `${minutes}分钟前`

      const hours = Math.floor(minutes / 60)
      return `${hours}小时前`
    })

    const formatValue = (value) => {
      if (typeof value !== 'number') return '0.00'
      return value.toFixed(2)
    }

    return {
      levelClass,
      statusIcon,
      statusText,
      trendClass,
      trendIcon,
      trendText,
      lastUpdateText,
      formatValue
    }
  }
}
</script>

<style lang="scss" scoped>
.radiation-card {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  border-radius: 24rpx;
  border: 1px solid rgba(226, 232, 240, 0.8);
  box-shadow: 0 4rpx 16rpx rgba(15, 23, 42, 0.06);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  overflow: hidden;

  &:hover {
    transform: translateY(-2rpx);
    box-shadow: 0 8rpx 24rpx rgba(15, 23, 42, 0.10);
  }

  &.level-danger {
    border-color: rgba(239, 68, 68, 0.3);
    background: linear-gradient(135deg, rgba(254, 242, 242, 0.95), rgba(255, 255, 255, 0.95));
  }

  &.level-warning {
    border-color: rgba(245, 158, 11, 0.3);
    background: linear-gradient(135deg, rgba(255, 251, 235, 0.95), rgba(255, 255, 255, 0.95));
  }

  &.level-safe {
    border-color: rgba(34, 197, 94, 0.3);
    background: linear-gradient(135deg, rgba(240, 253, 244, 0.95), rgba(255, 255, 255, 0.95));
  }
}

.card-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 32rpx 32rpx 0;

  .status-indicator {
    width: 60rpx;
    height: 60rpx;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 24rpx;

    &.level-danger {
      background: linear-gradient(135deg, #ef4444, #dc2626);
    }

    &.level-warning {
      background: linear-gradient(135deg, #f59e0b, #d97706);
    }

    &.level-safe {
      background: linear-gradient(135deg, #22c55e, #16a34a);
    }

    .indicator-icon {
      font-size: 32rpx;
    }
  }

  .card-title {
    flex: 1;
    font-size: 32rpx;
    font-weight: 600;
    color: #1f2937;
  }

  .card-actions {
    display: flex;
    gap: 16rpx;

    .action-btn {
      width: 48rpx;
      height: 48rpx;
      border-radius: 12rpx;
      background: rgba(156, 163, 175, 0.1);
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 24rpx;
      transition: all 0.2s ease;

      &:active {
        transform: scale(0.95);
        background: rgba(156, 163, 175, 0.2);
      }
    }
  }
}

.card-content {
  padding: 32rpx;

  .main-value {
    text-align: center;
    margin-bottom: 24rpx;

    .value-number {
      display: block;
      font-size: 72rpx;
      font-weight: 800;
      color: #1f2937;
      line-height: 1;
    }

    .value-unit {
      display: block;
      font-size: 28rpx;
      font-weight: 500;
      color: #6b7280;
      margin-top: 8rpx;
    }
  }

  .status-text {
    text-align: center;
    margin-bottom: 24rpx;

    text {
      font-size: 28rpx;
      font-weight: 600;
      padding: 12rpx 24rpx;
      border-radius: 20rpx;

      &.level-danger {
        color: #dc2626;
        background: rgba(239, 68, 68, 0.1);
      }

      &.level-warning {
        color: #d97706;
        background: rgba(245, 158, 11, 0.1);
      }

      &.level-safe {
        color: #16a34a;
        background: rgba(34, 197, 94, 0.1);
      }
    }
  }

  .additional-info {
    .info-item {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 16rpx 0;
      border-bottom: 1px solid rgba(229, 231, 235, 0.5);

      &:last-child {
        border-bottom: none;
      }

      .info-label {
        font-size: 26rpx;
        color: #6b7280;
        font-weight: 500;
      }

      .info-value {
        font-size: 26rpx;
        color: #1f2937;
        font-weight: 600;
      }
    }
  }
}

.card-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 32rpx 32rpx;

  .trend-indicator {
    display: flex;
    align-items: center;
    gap: 8rpx;

    .trend-icon {
      font-size: 24rpx;
    }

    .trend-text {
      font-size: 24rpx;
      font-weight: 500;

      .trend-up & {
        color: #dc2626;
      }

      .trend-down & {
        color: #16a34a;
      }

      .trend-stable & {
        color: #6b7280;
      }
    }
  }

  .last-update {
    font-size: 22rpx;
    color: #9ca3af;
  }
}
</style>
```

### 6. 项目配置文件

#### 6.1 页面配置 (pages.json)

```json
{
  "pages": [
    {
      "path": "pages/splash/splash",
      "style": {
        "navigationStyle": "custom",
        "backgroundColor": "#667eea",
        "navigationBarTitleText": "",
        "disableScroll": true
      }
    },
    {
      "path": "pages/dashboard/dashboard",
      "style": {
        "navigationStyle": "custom",
        "backgroundColor": "#f8fafc"
      }
    },
    {
      "path": "pages/charts/charts",
      "style": {
        "navigationStyle": "custom",
        "backgroundColor": "#f8fafc"
      }
    },
    {
      "path": "pages/map/map",
      "style": {
        "navigationStyle": "custom",
        "backgroundColor": "#f8fafc"
      }
    },
    {
      "path": "pages/health/health",
      "style": {
        "navigationStyle": "custom",
        "backgroundColor": "#f8fafc"
      }
    },
    {
      "path": "pages/settings/settings",
      "style": {
        "navigationStyle": "custom",
        "backgroundColor": "#f8fafc"
      }
    },
    {
      "path": "pages/notification/notification",
      "style": {
        "navigationBarTitleText": "通知中心",
        "navigationBarBackgroundColor": "#fefefe",
        "navigationBarTextStyle": "black",
        "backgroundColor": "#f8fafc"
      }
    }
  ],
  "globalStyle": {
    "navigationBarTextStyle": "black",
    "navigationBarTitleText": "",
    "navigationBarBackgroundColor": "#fefefe",
    "backgroundColor": "#f8fafc",
    "backgroundColorTop": "#f8fafc",
    "backgroundColorBottom": "#f8fafc",
    "navigationStyle": "custom"
  }
}
```

#### 6.2 应用配置清单 (manifest.json)

```json
{
    "name": "核辐射探测腕表APP",
    "appid": "__UNI__CF81990",
    "description": "核辐射探测腕表配套移动应用",
    "versionName": "1.0.0",
    "versionCode": "100",
    "transformPx": false,

    "app-plus": {
        "usingComponents": true,
        "nvueStyleCompiler": "uni-app",
        "compilerVersion": 3,
        "splashscreen": {
            "alwaysShowBeforeRender": false,
            "waiting": false,
            "autoclose": true,
            "delay": 0
        },
        "modules": {
            "Geolocation": {},
            "Maps": {}
        },
        "distribute": {
            "android": {
                "permissions": [
                    "<uses-permission android:name=\"android.permission.CHANGE_NETWORK_STATE\"/>",
                    "<uses-permission android:name=\"android.permission.VIBRATE\"/>",
                    "<uses-permission android:name=\"android.permission.ACCESS_NETWORK_STATE\"/>",
                    "<uses-permission android:name=\"android.permission.ACCESS_WIFI_STATE\"/>",
                    "<uses-permission android:name=\"android.permission.READ_PHONE_STATE\"/>",
                    "<uses-permission android:name=\"android.permission.WAKE_LOCK\"/>"
                ],
                "abiFilters": ["armeabi-v7a", "arm64-v8a", "x86"]
            },
            "ios": {
                "dSYMs": false
            },
            "sdkConfigs": {
                "maps": {},
                "geolocation": {
                    "system": {
                        "__platform__": ["ios", "android"]
                    }
                }
            }
        }
    },

    "mp-weixin": {
        "appid": "wx4bad12da75cef692",
        "setting": {
            "urlCheck": false
        },
        "usingComponents": true
    },

    "h5": {
        "sdkConfigs": {
            "maps": {
                "amap": {
                    "key": "your_web_key_here",
                    "securityJsCode": "your_security_code_here"
                }
            }
        },
        "title": "核辐射探测腕表APP",
        "router": {
            "mode": "hash",
            "base": "./"
        }
    },

    "vueVersion": "3"
}
```

#### 6.3 构建配置 (vite.config.js)

```javascript
import { defineConfig } from 'vite'
import uni from '@dcloudio/vite-plugin-uni'

export default defineConfig({
  plugins: [
    uni(),
  ],
  resolve: {
    alias: {
      '@': '/src'
    }
  },
  server: {
    hmr: {
      overlay: true
    }
  }
})
```

#### 6.4 依赖配置 (package.json)

```json
{
  "name": "radiation-watch-app",
  "version": "1.0.0",
  "scripts": {
    "dev:h5": "uni",
    "dev:mp-weixin": "uni -p mp-weixin",
    "build:h5": "uni build",
    "build:mp-weixin": "uni build -p mp-weixin"
  },
  "dependencies": {
    "@dcloudio/uni-app": "3.0.0-4070520250711001",
    "@dcloudio/uni-components": "3.0.0-4070520250711001",
    "@dcloudio/uni-h5": "3.0.0-4070520250711001",
    "@dcloudio/uni-mp-weixin": "3.0.0-4070520250711001",
    "echarts": "^5.4.3",
    "lodash": "^4.17.21",
    "mqtt": "^5.3.6",
    "vue": "^3.4.21",
    "vue-i18n": "^9.1.9"
  },
  "devDependencies": {
    "@dcloudio/types": "^3.4.8",
    "@dcloudio/vite-plugin-uni": "3.0.0-4070520250711001",
    "@vitejs/plugin-vue": "^5.0.4",
    "sass": "^1.90.0",
    "vite": "5.2.8"
  }
}
```

## 7. 核心功能特性

### 7.1 实时数据监测
- **辐射剂量率监测**: 实时显示当前环境辐射剂量率(μSv/h)
- **计数率监测**: 显示探测器每秒计数(CPS)
- **累积剂量计算**: 自动计算和显示累积辐射剂量
- **环境温度监测**: 显示探测器环境温度
- **多级报警系统**: 支持剂量率过高、过低、累积剂量过高等多种报警

### 7.2 智能数据分析
- **历史数据图表**: 支持多种时间范围的数据可视化
- **趋势分析**: 基于历史数据分析辐射水平变化趋势
- **预测算法**: 采用ARIMA+LSTM混合算法进行辐射预测
- **统计分析**: 提供平均值、最大值、最小值等统计信息
- **数据导出**: 支持数据导出和分享功能

### 7.3 设备管理
- **连接状态监控**: 实时显示MQTT、蓝牙、GPS、移动网络连接状态
- **电池状态管理**: 显示电池电量、充电状态、电压信息
- **设备信息查看**: 显示IMEI、ICCID、固件版本等设备信息
- **远程配置**: 支持远程修改设备参数和报警阈值

### 7.4 健康管理
- **生理指标监测**: 心率、血氧饱和度、体温、步数等
- **健康数据分析**: 日常健康数据统计和趋势分析
- **健康报告**: 生成个人健康报告和建议
- **异常提醒**: 生理指标异常时的智能提醒

### 7.5 地图定位
- **实时位置显示**: 基于GPS的精确位置定位
- **辐射热力图**: 在地图上显示区域辐射水平分布
- **历史轨迹**: 记录和显示移动轨迹及对应辐射数据
- **兴趣点标记**: 支持标记重要位置和辐射异常点

## 8. 与嵌入式系统的数据交互

### 8.1 数据协议
本APP通过MQTT协议与基于CH579芯片的智能手表进行数据通信，主要数据格式包括：

#### 辐射数据 (radiation/data)
```javascript
{
  doseRate: 0.12,        // 剂量率 (μSv/h)
  cps: 65,               // 计数率 (CPS)
  doseSum: 2.45,         // 累积剂量 (μSv)
  alarmStatus: 0,        // 报警状态位
  temperature: 25.6,     // 温度 (°C)
  timestamp: 1640995200000
}
```

#### 设备状态 (device/status)
```javascript
{
  battery: {
    level: 85,           // 电池电量 (%)
    charging: false,     // 充电状态
    voltage: 4.1         // 电池电压 (V)
  },
  connection: {
    mqtt: true,          // MQTT连接状态
    bluetooth: true,     // 蓝牙状态
    gps: true,          // GPS状态
    cellular: true       // 移动网络状态
  },
  deviceInfo: {
    imei: "860123456789012",
    iccid: "89860123456789012345",
    firmwareVersion: "v1.2.3",
    serialNumber: "LYWB01_A00001"
  }
}
```

#### 健康数据 (health/data)
```javascript
{
  heartRate: 75,         // 心率 (bpm)
  spO2: 98,             // 血氧饱和度 (%)
  bodyTemp: 36.5,       // 体温 (°C)
  steps: 8543,          // 步数
  timestamp: 1640995200000
}
```

### 8.2 数据处理流程
1. **数据接收**: MQTT服务接收来自手表的实时数据
2. **数据验证**: 验证数据格式和有效性
3. **数据存储**: 将数据存储到本地存储和历史记录
4. **报警检测**: 根据预设阈值检测异常情况
5. **界面更新**: 实时更新用户界面显示
6. **数据分析**: 进行趋势分析和预测计算

## 9. 部署和使用说明

### 9.1 开发环境搭建
```bash
# 1. 安装依赖
npm install

# 2. 启动H5开发服务器
npm run dev:h5

# 3. 启动微信小程序开发
npm run dev:mp-weixin

# 4. 构建生产版本
npm run build:h5
npm run build:mp-weixin
```

### 9.2 平台部署
- **H5版本**: 可部署到任何Web服务器，支持PWA
- **微信小程序**: 通过微信开发者工具上传发布
- **APP版本**: 通过HBuilderX打包为原生APP
- **其他小程序**: 支持支付宝、百度、字节跳动等小程序平台

### 9.3 配置说明
1. **MQTT服务器配置**: 在`mqttService.js`中配置MQTT服务器地址和认证信息
2. **地图服务配置**: 在`manifest.json`中配置地图API密钥
3. **推送服务配置**: 配置消息推送服务参数
4. **主题定制**: 通过`themeManager.js`自定义应用主题

## 10. 项目特色与创新

### 10.1 技术创新
- **混合预测算法**: 结合ARIMA时间序列分析和LSTM深度学习的预测模型
- **响应式设计**: 自适应不同屏幕尺寸和设备类型
- **实时数据流**: 基于MQTT的低延迟实时数据传输
- **智能报警**: 多层次、多维度的智能报警系统
- **数据可视化**: 丰富的图表和可视化组件

### 10.2 用户体验
- **现代化UI**: 采用毛玻璃效果、渐变色彩、动画过渡等现代设计元素
- **直观操作**: 简洁明了的界面布局和交互设计
- **个性化定制**: 支持主题切换、参数配置等个性化设置
- **离线支持**: 关键功能支持离线使用
- **多语言支持**: 国际化设计，支持多语言切换

### 10.3 安全可靠
- **数据加密**: 敏感数据传输和存储加密
- **异常处理**: 完善的错误处理和恢复机制
- **数据备份**: 自动数据备份和恢复功能
- **权限管理**: 细粒度的功能权限控制

## 11. 总结

本核辐射探测腕表配套APP是一个功能完整、技术先进的移动应用程序，具有以下特点：

1. **完整的功能体系**: 涵盖实时监测、数据分析、健康管理、设备控制等全方位功能
2. **先进的技术架构**: 采用Vue 3 + uni-app的现代化开发框架，支持多平台部署
3. **智能化数据处理**: 集成高级预测算法和智能分析功能
4. **优秀的用户体验**: 现代化的UI设计和流畅的交互体验
5. **可扩展性强**: 模块化设计，便于功能扩展和维护

该APP与基于CH579芯片的智能手表系统完美配合，为用户提供了专业、可靠、易用的核辐射监测解决方案，在环境监测、职业防护、科研教育等领域具有重要的应用价值。
```
