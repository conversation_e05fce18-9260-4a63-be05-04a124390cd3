<template>
  <view class="health-container">
    <!-- Toast容器 -->
    <ToastContainer />

    <!-- 左侧日期，居中标题的蓝色头部区域 -->
    <view class="blue-rounded-header">
      <view class="header-content-balanced">
        <view class="date-badge">
          <text class="date-text-new">{{ formatCurrentDate() }}</text>
        </view>
        <text class="main-title-new">健康活动</text>
        <view class="header-avatar">
          <view class="avatar-circle" @tap="onProfileTap">
            <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2.5" stroke-linecap="round" stroke-linejoin="round">
              <path d="M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2"></path>
              <circle cx="12" cy="7" r="4"></circle>
            </svg>
          </view>
        </view>
      </view>
      <!-- 添加头部装饰动画 -->
      <view class="header-decoration">
        <view class="floating-particle particle-1"></view>
        <view class="floating-particle particle-2"></view>
        <view class="floating-particle particle-3"></view>
      </view>
    </view>

    <!-- 优化的日期选择器 -->
    <view class="date-selector-container">
      <view class="date-selector">
        <view
          v-for="(day, index) in weekDays"
          :key="index"
          class="date-item"
          :class="{ active: day.isToday }"
          @tap="selectDate(day, index)"
        >
          <view class="date-item-content">
            <text class="day-name">{{ day.name }}</text>
            <text class="day-number">{{ day.number }}</text>
          </view>
          <!-- 添加选中状态的波纹效果 -->
          <view class="ripple-effect" v-if="day.isToday"></view>
        </view>
      </view>
    </view>

    <!-- 重新设计的健康指标卡片 - 2x2布局 -->
    <view class="health-metrics-container">
      <!-- 心率卡片 - 左上角，使用红色主题 -->
      <view class="health-card heart-rate-card interactive-card"
            :class="{ 'card-active': cardTouchActive }"
            @tap="onMetricTap('heartRate')"
            @touchstart="onCardTouchStart('heart')"
            @touchend="onCardTouchEnd"
            @longpress="onCardLongPress('heartRate')"
            @mouseenter="onCardHover('heart', true)"
            @mouseleave="onCardHover('heart', false)">
        <view class="card-header">
          <view class="header-left-section">
            <view class="metric-icon heart-icon">
              <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2.5" stroke-linecap="round" stroke-linejoin="round">
                <path d="M19 14c1.49-1.46 3-3.21 3-5.5A5.5 5.5 0 0 0 16.5 3c-1.76 0-3 .5-4.5 2-1.5-1.5-2.74-2-4.5-2A5.5 5.5 0 0 0 2 8.5c0 2.29 1.51 4.04 3 5.5l7 7z"></path>
              </svg>
            </view>
            <text class="card-label">心率</text>
          </view>
          <!-- 心率实时折线图 -->
          <view class="mini-chart heart-rate-mini-chart">
            <svg viewBox="0 0 40 20" xmlns="http://www.w3.org/2000/svg">
              <defs>
                <linearGradient id="heartRateLineGradient" x1="0%" y1="0%" x2="100%" y2="0%">
                  <stop offset="0%" style="stop-color:rgba(255,255,255,0.6);stop-opacity:1" />
                  <stop offset="100%" style="stop-color:rgba(255,255,255,1);stop-opacity:1" />
                </linearGradient>
                <filter id="glow">
                  <feGaussianBlur stdDeviation="2" result="coloredBlur"/>
                  <feMerge>
                    <feMergeNode in="coloredBlur"/>
                    <feMergeNode in="SourceGraphic"/>
                  </feMerge>
                </filter>
              </defs>
              <!-- 动态心率曲线 -->
              <polyline
                :points="heartRateChartPoints"
                fill="none"
                stroke="url(#heartRateLineGradient)"
                stroke-width="2"
                stroke-linecap="round"
                stroke-linejoin="round"
                filter="url(#glow)"
                class="heart-rate-line"
              >
                <animate attributeName="stroke-dasharray" values="0,100;100,0" dur="2s" repeatCount="indefinite"/>
              </polyline>
              <!-- 实时数据点 -->
              <circle :cx="38" :cy="heartRateLastPoint" r="2" fill="rgba(255,255,255,0.9)" class="data-point">
                <animate attributeName="r" :values="`2;${3 + heartRate/100};2`" :dur="`${60/heartRate}s`" repeatCount="indefinite"/>
                <animate attributeName="opacity" values="0.7;1;0.7" :dur="`${60/heartRate}s`" repeatCount="indefinite"/>
              </circle>
              <!-- 心跳脉冲效果 -->
              <circle :cx="38" :cy="heartRateLastPoint" r="0" fill="rgba(255,255,255,0.3)" class="pulse-ring">
                <animate attributeName="r" values="0;8;0" :dur="`${60/heartRate}s`" repeatCount="indefinite"/>
                <animate attributeName="opacity" values="0.8;0;0.8" :dur="`${60/heartRate}s`" repeatCount="indefinite"/>
              </circle>
            </svg>
          </view>
        </view>
        <view class="card-main">
          <view class="heart-rate-value-section">
            <text class="card-value">{{ heartRate }}</text>
            <text class="card-unit">BPM</text>
          </view>
          <view class="heart-rate-status">
            <text class="status-text">{{ getHeartRateStatus(heartRate) }}</text>
          </view>
        </view>
        <!-- 实时心率波形 -->
        <view class="heart-rate-waveform">
          <canvas
            canvas-id="heartWaveCanvas"
            class="heart-wave-canvas"
            :style="{ width: '100%', height: '40px' }"
          ></canvas>
        </view>
        <view class="heart-rate-stats">
          <view class="stat-item">
            <text class="stat-label">静息</text>
            <text class="stat-value">{{ restingHeartRate }}</text>
          </view>
          <view class="stat-item">
            <text class="stat-label">最高</text>
            <text class="stat-value">{{ maxHeartRate }}</text>
          </view>
        </view>
      </view>

      <!-- 步数卡片 - 使用绿色主题 -->
      <view class="health-card steps-card interactive-card"
            :class="{ 'card-active': cardTouchActive }"
            @tap="onMetricTap('steps')"
            @touchstart="onCardTouchStart('steps')"
            @touchend="onCardTouchEnd"
            @longpress="onCardLongPress('steps')"
            @mouseenter="onCardHover('steps', true)"
            @mouseleave="onCardHover('steps', false)">
        <view class="card-header">
          <view class="header-left-section">
            <view class="metric-icon steps-icon">
              <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2.5" stroke-linecap="round" stroke-linejoin="round">
                <path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"></path>
              </svg>
            </view>
            <text class="card-label">步数</text>
          </view>
          <!-- 步数实时柱状图 -->
          <view class="mini-chart steps-mini-chart">
            <svg viewBox="0 0 40 20" xmlns="http://www.w3.org/2000/svg">
              <defs>
                <linearGradient id="stepsBarsGradient" x1="0%" y1="100%" x2="0%" y2="0%">
                  <stop offset="0%" style="stop-color:rgba(255,255,255,0.6);stop-opacity:1" />
                  <stop offset="100%" style="stop-color:rgba(255,255,255,1);stop-opacity:1" />
                </linearGradient>
                <filter id="stepsGlow">
                  <feGaussianBlur stdDeviation="1" result="coloredBlur"/>
                  <feMerge>
                    <feMergeNode in="coloredBlur"/>
                    <feMergeNode in="SourceGraphic"/>
                  </feMerge>
                </filter>
              </defs>
              <!-- 动态步数柱状图 -->
              <rect v-for="(bar, index) in stepsChartBars"
                    :key="index"
                    :x="bar.x"
                    :y="bar.y"
                    :width="bar.width"
                    :height="bar.height"
                    fill="url(#stepsBarsGradient)"
                    rx="2"
                    filter="url(#stepsGlow)"
                    class="steps-bar">
                <animate attributeName="height"
                         :values="`${bar.height};${bar.height + 2};${bar.height}`"
                         :dur="`${2 + index * 0.3}s`"
                         repeatCount="indefinite"/>
                <animate attributeName="y"
                         :values="`${bar.y};${bar.y - 2};${bar.y}`"
                         :dur="`${2 + index * 0.3}s`"
                         repeatCount="indefinite"/>
                <animate attributeName="opacity"
                         values="0.7;1;0.7"
                         :dur="`${1.5 + index * 0.2}s`"
                         repeatCount="indefinite"/>
              </rect>
              <!-- 当前步数指示器 -->
              <circle :cx="36" :cy="4" r="1.5" fill="rgba(255,255,255,0.9)" class="current-indicator">
                <animate attributeName="r" values="1.5;2.5;1.5" dur="1s" repeatCount="indefinite"/>
                <animate attributeName="opacity" values="0.8;1;0.8" dur="1s" repeatCount="indefinite"/>
              </circle>
            </svg>
          </view>
        </view>
        <view class="card-content">
          <view class="steps-value-section-inline">
            <text class="card-value-small">{{ todaySteps }}</text>
            <text class="card-unit-small">STEPS</text>
          </view>
          <view class="steps-progress">
            <view class="progress-bar">
              <view class="progress-fill" :style="{ width: stepsProgress + '%' }"></view>
            </view>
            <text class="progress-text">{{ stepsProgress }}% 目标</text>
          </view>
        </view>
      </view>

      <!-- 血氧卡片 - 左侧标题设计 -->
      <view class="health-card oxygen-card" @tap="onMetricTap('oxygen')">
        <view class="card-header-left">
          <view class="metric-icon oxygen-icon-white">
            <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2.5" stroke-linecap="round" stroke-linejoin="round">
              <circle cx="12" cy="12" r="10"></circle>
              <path d="M8 14s1.5 2 4 2 4-2 4-2"></path>
              <line x1="9" y1="9" x2="9.01" y2="9"></line>
              <line x1="15" y1="9" x2="15.01" y2="9"></line>
            </svg>
          </view>
          <text class="card-label-white">血氧</text>
        </view>
        <view class="card-content">
          <view class="oxygen-value-section">
            <text class="card-value">{{ bloodOxygen }}</text>
            <text class="card-unit">%</text>
          </view>
          <view class="oxygen-status">
            <text class="status-text">{{ getOxygenStatus(bloodOxygen) }}</text>
          </view>
        </view>
        <!-- 血氧环形图 -->
        <view class="oxygen-ring-chart">
          <svg class="ring-svg" viewBox="0 0 80 80" xmlns="http://www.w3.org/2000/svg">
            <defs>
              <linearGradient id="oxygenRingGradient" x1="0%" y1="0%" x2="100%" y2="100%">
                <stop offset="0%" style="stop-color:#2196F3;stop-opacity:1" />
                <stop offset="50%" style="stop-color:#1976D2;stop-opacity:1" />
                <stop offset="100%" style="stop-color:#0D47A1;stop-opacity:1" />
              </linearGradient>
              <filter id="oxygenGlow">
                <feGaussianBlur stdDeviation="2" result="coloredBlur"/>
                <feMerge>
                  <feMergeNode in="coloredBlur"/>
                  <feMergeNode in="SourceGraphic"/>
                </feMerge>
              </filter>
            </defs>
            <!-- 背景圆环 -->
            <circle
              cx="40"
              cy="40"
              r="28"
              fill="none"
              stroke="rgba(255, 255, 255, 0.2)"
              stroke-width="6"
            />
            <!-- 进度圆环 -->
            <circle
              cx="40"
              cy="40"
              r="28"
              fill="none"
              stroke="rgba(255, 255, 255, 0.9)"
              stroke-width="6"
              stroke-linecap="round"
              :stroke-dasharray="`${(bloodOxygen / 100) * 176} 176`"
              stroke-dashoffset="44"
              transform="rotate(-90 40 40)"
              filter="url(#oxygenGlow)"
              class="oxygen-progress-ring"
            />
            <!-- 内部装饰圆点 -->
            <circle cx="40" cy="40" r="2" fill="rgba(255, 255, 255, 0.9)">
              <animate attributeName="r" values="2;4;2" dur="2s" repeatCount="indefinite"/>
              <animate attributeName="opacity" values="0.9;0.5;0.9" dur="2s" repeatCount="indefinite"/>
            </circle>
          </svg>

        </view>
      </view>

      <!-- 辐射剂量卡片 - 左侧标题设计 -->
      <view class="health-card radiation-card" @tap="onMetricTap('radiation')">
        <view class="card-header-left">
          <view class="metric-icon radiation-icon-white">
            <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2.5" stroke-linecap="round" stroke-linejoin="round">
              <circle cx="12" cy="12" r="1"></circle>
              <path d="M20.2 20.2c2.04-2.03.02-7.36-4.5-11.9-4.54-4.52-9.87-6.54-11.9-4.5-2.04 2.03-.02 7.36 4.5 11.9 4.54 4.52 9.87 6.54 11.9 4.5z"></path>
              <path d="M15.7 15.7c4.52-4.54 6.54-9.87 4.5-11.9-2.03-2.04-7.36-.02-11.9 4.5-4.52 4.54-6.54 9.87-4.5 11.9 2.03 2.04 7.36.02 11.9-4.5z"></path>
            </svg>
          </view>
          <text class="card-label-white">辐射剂量</text>
        </view>
        <view class="card-content">
          <view class="radiation-value-section-inline">
            <text class="card-value-small">{{ exposureLevel.toFixed(1) }}</text>
            <text class="card-unit-small">μSv/h</text>
          </view>
          <view class="radiation-progress">
            <view class="progress-bar">
              <view class="progress-fill radiation-fill" :style="{ width: Math.min((exposureLevel / 50) * 100, 100) + '%' }"></view>
            </view>
            <text class="progress-text">{{ getRadiationStatus(exposureLevel) }}</text>
          </view>
        </view>
      </view>
    </view>

    <!-- 健康摘要卡片 -->
    <view class="health-summary-card">
      <view class="summary-header">
        <view class="summary-icon">
          <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2.5" stroke-linecap="round" stroke-linejoin="round">
            <path d="M22 12h-4l-3 9L9 3l-3 9H2"></path>
          </svg>
        </view>
        <view class="summary-title-section">
          <text class="summary-title">今日健康摘要</text>
          <text class="summary-subtitle">{{ formatCurrentDate() }} 数据概览</text>
        </view>
      </view>

      <view class="summary-metrics">
        <view class="summary-metric">
          <view class="metric-icon-small">
            <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
              <path d="M22 12h-4l-3 9L9 3l-3 9H2"></path>
            </svg>
          </view>
          <view class="metric-info">
            <text class="metric-label">平均心率</text>
            <text class="metric-value">{{ averageHeartRate }} BPM</text>
          </view>
        </view>

        <view class="summary-metric">
          <view class="metric-icon-small">
            <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
              <path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"></path>
            </svg>
          </view>
          <view class="metric-info">
            <text class="metric-label">活跃时间</text>
            <text class="metric-value">{{ activeTime }} 分钟</text>
          </view>
        </view>

        <view class="summary-metric">
          <view class="metric-icon-small">
            <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
              <path d="M13 2L3 14h9l-1 8 10-12h-9l1-8z"></path>
            </svg>
          </view>
          <view class="metric-info">
            <text class="metric-label">消耗卡路里</text>
            <text class="metric-value">{{ caloriesBurned }} kcal</text>
          </view>
        </view>
      </view>
    </view>

    <!-- 重新设计的趋势图表卡片 -->
    <view class="chart-card-modern">
      <view class="chart-header-modern">
        <view class="chart-title-section">
          <view class="chart-icon">
            <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2.5" stroke-linecap="round" stroke-linejoin="round">
              <polyline points="22,6 13.5,15.5 8.5,10.5 2,17"></polyline>
              <polyline points="16,6 22,6 22,12"></polyline>
            </svg>
          </view>
          <view class="chart-title-text">
            <text class="chart-title">健康趋势</text>
            <text class="chart-subtitle">{{ selectedPeriod }}数据分析</text>
          </view>
        </view>
        <view class="time-selector">
          <text
            class="time-option"
            :class="{ active: selectedPeriod === '24小时' }"
            @tap="selectPeriod('24小时')"
          >24小时</text>
          <text
            class="time-option"
            :class="{ active: selectedPeriod === '3天' }"
            @tap="selectPeriod('3天')"
          >3天</text>
        </view>
      </view>

      <!-- 数据类型选择器 -->
      <view class="data-type-selector">
        <view
          class="data-type-option"
          :class="{ active: selectedDataType === 'heartRate' }"
          @tap="selectDataType('heartRate')"
        >
          <view class="data-type-icon heart-rate-icon-small">
            <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
              <path d="M19 14c1.49-1.46 3-3.21 3-5.5A5.5 5.5 0 0 0 16.5 3c-1.76 0-3 .5-4.5 2-1.5-1.5-2.74-2-4.5-2A5.5 5.5 0 0 0 2 8.5c0 2.29 1.51 4.04 3 5.5l7 7z"></path>
            </svg>
          </view>
          <text class="data-type-text">心率</text>
        </view>
        <view
          class="data-type-option"
          :class="{ active: selectedDataType === 'bloodOxygen' }"
          @tap="selectDataType('bloodOxygen')"
        >
          <view class="data-type-icon blood-oxygen-icon-small">
            <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
              <circle cx="12" cy="12" r="3"></circle>
              <path d="M12 1v6m0 6v6m11-7h-6m-6 0H1"></path>
            </svg>
          </view>
          <text class="data-type-text">血氧</text>
        </view>
        <view
          class="data-type-option"
          :class="{ active: selectedDataType === 'steps' }"
          @tap="selectDataType('steps')"
        >
          <view class="data-type-icon steps-icon-small">
            <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
              <path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"></path>
            </svg>
          </view>
          <text class="data-type-text">步数</text>
        </view>
      </view>

      <!-- 图表类型选择器 -->
      <view class="chart-type-selector">
        <view
          class="chart-type-option"
          :class="{ active: selectedChartType === 'line' }"
          @tap="selectChartType('line')"
        >
          <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
            <path d="M3 3v18h18"></path>
            <path d="M18.7 8l-5.1 5.2-2.8-2.7L7 14.3"></path>
          </svg>
          <text>折线图</text>
        </view>
        <view
          class="chart-type-option"
          :class="{ active: selectedChartType === 'bar' }"
          @tap="selectChartType('bar')"
        >
          <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
            <path d="M3 3v18h18"></path>
            <rect x="7" y="10" width="3" height="8"></rect>
            <rect x="12" y="6" width="3" height="12"></rect>
            <rect x="17" y="8" width="3" height="10"></rect>
          </svg>
          <text>柱状图</text>
        </view>
      </view>

      <view class="chart-container-modern">
        <canvas
          class="trend-chart"
          canvas-id="healthTrendChart"
          :style="{ width: chartWidth + 'px', height: '280px' }"
        ></canvas>
        <view v-if="isLoadingChart" class="chart-loading">
          <view class="loading-spinner"></view>
          <text class="loading-text">分析中...</text>
        </view>


        <!-- 健康趋势SVG图表 - 已替换为Canvas -->
        <view class="health-trend-svg-container" v-if="false">
          <svg class="health-trend-svg" viewBox="0 0 400 240" xmlns="http://www.w3.org/2000/svg">
            <defs>
              <linearGradient id="healthScoreGradient" x1="0%" y1="0%" x2="0%" y2="100%">
                <stop offset="0%" style="stop-color:rgba(244,67,54,0.3);stop-opacity:1" />
                <stop offset="100%" style="stop-color:rgba(244,67,54,0.05);stop-opacity:1" />
              </linearGradient>
              <linearGradient id="exposureGradient" x1="0%" y1="0%" x2="0%" y2="100%">
                <stop offset="0%" style="stop-color:rgba(33,150,243,0.3);stop-opacity:1" />
                <stop offset="100%" style="stop-color:rgba(33,150,243,0.05);stop-opacity:1" />
              </linearGradient>
              <filter id="chartGlow">
                <feGaussianBlur stdDeviation="2" result="coloredBlur"/>
                <feMerge>
                  <feMergeNode in="coloredBlur"/>
                  <feMergeNode in="SourceGraphic"/>
                </feMerge>
              </filter>
            </defs>

            <!-- 坐标轴 -->
            <g class="chart-axes">
              <!-- Y轴 -->
              <line x1="60" y1="40" x2="60" y2="160" stroke="#64748b" stroke-width="2"/>
              <!-- X轴 -->
              <line x1="60" y1="160" x2="360" y2="160" stroke="#64748b" stroke-width="2"/>

              <!-- Y轴箭头 -->
              <polygon points="60,40 55,50 65,50" fill="#64748b"/>
              <!-- X轴箭头 -->
              <polygon points="360,160 350,155 350,165" fill="#64748b"/>
            </g>

            <!-- 网格线 -->
            <g class="grid-lines" opacity="0.15">
              <!-- 垂直网格线 -->
              <line x1="110" y1="40" x2="110" y2="200" stroke="#64748b" stroke-width="0.5" stroke-dasharray="2,2"/>
              <line x1="160" y1="40" x2="160" y2="200" stroke="#64748b" stroke-width="0.5" stroke-dasharray="2,2"/>
              <line x1="210" y1="40" x2="210" y2="200" stroke="#64748b" stroke-width="0.5" stroke-dasharray="2,2"/>
              <line x1="260" y1="40" x2="260" y2="200" stroke="#64748b" stroke-width="0.5" stroke-dasharray="2,2"/>
              <line x1="310" y1="40" x2="310" y2="200" stroke="#64748b" stroke-width="0.5" stroke-dasharray="2,2"/>

              <!-- 水平网格线 -->
              <line x1="60" y1="80" x2="360" y2="80" stroke="#64748b" stroke-width="0.5" stroke-dasharray="2,2"/>
              <line x1="60" y1="120" x2="360" y2="120" stroke="#64748b" stroke-width="0.5" stroke-dasharray="2,2"/>
              <line x1="60" y1="160" x2="360" y2="160" stroke="#64748b" stroke-width="0.5" stroke-dasharray="2,2"/>
            </g>

            <!-- Y轴刻度和标签 -->
            <g class="y-axis-labels">
              <line x1="55" y1="70" x2="65" y2="70" stroke="#64748b" stroke-width="1"/>
              <text x="50" y="75" text-anchor="end" font-size="11" fill="#64748b" font-weight="500">90</text>

              <line x1="55" y1="100" x2="65" y2="100" stroke="#64748b" stroke-width="1"/>
              <text x="50" y="105" text-anchor="end" font-size="11" fill="#64748b" font-weight="500">70</text>

              <line x1="55" y1="130" x2="65" y2="130" stroke="#64748b" stroke-width="1"/>
              <text x="50" y="135" text-anchor="end" font-size="11" fill="#64748b" font-weight="500">50</text>

              <line x1="55" y1="160" x2="65" y2="160" stroke="#64748b" stroke-width="1"/>
              <text x="50" y="165" text-anchor="end" font-size="11" fill="#64748b" font-weight="500">30</text>
            </g>

            <!-- 健康分数区域 -->
            <polygon
              points="85,110 135,90 185,100 235,80 285,85 335,75 335,160 85,160"
              fill="url(#healthScoreGradient)"
            />

            <!-- 暴露水平区域 -->
            <polygon
              points="85,130 135,125 185,120 235,115 285,110 335,105 335,160 85,160"
              fill="url(#exposureGradient)"
            />

            <!-- 健康分数线 -->
            <polyline
              points="85,110 135,90 185,100 235,80 285,85 335,75"
              fill="none"
              stroke="#F44336"
              stroke-width="3"
              stroke-linecap="round"
              stroke-linejoin="round"
              filter="url(#chartGlow)"
            />

            <!-- 暴露水平线 -->
            <polyline
              points="85,130 135,125 185,120 235,115 285,110 335,105"
              fill="none"
              stroke="#2196F3"
              stroke-width="3"
              stroke-linecap="round"
              stroke-linejoin="round"
              filter="url(#chartGlow)"
            />

            <!-- 数据点 -->
            <circle cx="85" cy="110" r="4" fill="#F44336">
              <animate attributeName="r" values="4;6;4" dur="2s" repeatCount="indefinite"/>
            </circle>
            <circle cx="135" cy="90" r="4" fill="#F44336"/>
            <circle cx="185" cy="100" r="4" fill="#F44336"/>
            <circle cx="235" cy="80" r="4" fill="#F44336"/>
            <circle cx="285" cy="85" r="4" fill="#F44336"/>
            <circle cx="335" cy="75" r="4" fill="#F44336">
              <animate attributeName="r" values="4;6;4" dur="2s" repeatCount="indefinite"/>
            </circle>

            <circle cx="85" cy="130" r="4" fill="#2196F3"/>
            <circle cx="135" cy="125" r="4" fill="#2196F3"/>
            <circle cx="185" cy="120" r="4" fill="#2196F3"/>
            <circle cx="235" cy="115" r="4" fill="#2196F3"/>
            <circle cx="285" cy="110" r="4" fill="#2196F3"/>
            <circle cx="335" cy="105" r="4" fill="#2196F3">
              <animate attributeName="r" values="4;6;4" dur="2s" repeatCount="indefinite"/>
            </circle>

            <!-- X轴刻度和标签 -->
            <g class="x-axis-labels">
              <line x1="85" y1="160" x2="85" y2="170" stroke="#64748b" stroke-width="1"/>
              <text x="85" y="185" text-anchor="middle" font-size="11" fill="#64748b" font-weight="500">周一</text>

              <line x1="135" y1="160" x2="135" y2="170" stroke="#64748b" stroke-width="1"/>
              <text x="135" y="185" text-anchor="middle" font-size="11" fill="#64748b" font-weight="500">周二</text>

              <line x1="185" y1="160" x2="185" y2="170" stroke="#64748b" stroke-width="1"/>
              <text x="185" y="185" text-anchor="middle" font-size="11" fill="#64748b" font-weight="500">周三</text>

              <line x1="235" y1="160" x2="235" y2="170" stroke="#64748b" stroke-width="1"/>
              <text x="235" y="185" text-anchor="middle" font-size="11" fill="#64748b" font-weight="500">周四</text>

              <line x1="285" y1="160" x2="285" y2="170" stroke="#64748b" stroke-width="1"/>
              <text x="285" y="185" text-anchor="middle" font-size="11" fill="#64748b" font-weight="500">周五</text>

              <line x1="335" y1="160" x2="335" y2="170" stroke="#64748b" stroke-width="1"/>
              <text x="335" y="185" text-anchor="middle" font-size="11" fill="#64748b" font-weight="500">今日</text>
            </g>

            <!-- 坐标轴标题 -->
            <text x="210" y="210" text-anchor="middle" font-size="12" fill="#64748b" font-weight="600">时间</text>
            <text x="25" y="100" text-anchor="middle" font-size="12" fill="#64748b" font-weight="600" transform="rotate(-90 25 100)">健康指数</text>
          </svg>
        </view>
      </view>


    </view>



    <!-- 底部导航 -->
    <BottomNavigation :currentPage="'health'" />
  </view>
</template>

<script setup>
import { ref, computed, onMounted, onUnmounted } from 'vue'
import { radiationState } from '../../utils/dataStore.js'
import BottomNavigation from '../../components/BottomNavigation.vue'
import ToastContainer from '../../components/ToastContainer.vue'
import { toastManager } from '../../utils/toastManager.js'

// 响应式数据
const exposureLevel = ref(15.6)
const chartWidth = ref(320)
const isLoadingChart = ref(false)
const scrollLeft = ref(0)

// 图表选择器状态
const selectedPeriod = ref('24小时')
const selectedDataType = ref('heartRate')
const selectedChartType = ref('line')

// 健康指标数据
const heartRate = ref(88)
const todaySteps = ref(450)
const bloodOxygen = ref(98)
const restingHeartRate = ref(65)
const maxHeartRate = ref(125)
const heartRateTrend = ref('up') // 'up', 'down', 'stable'
const stepsGoal = ref(10000)
const averageHeartRate = ref(82)
const activeTime = ref(45)
const caloriesBurned = ref(320)

// 动画控制
const cardTouchActive = ref(false)

// 实时图表数据
const heartRateHistory = ref([72, 75, 73, 78, 82, 79, 88])
const stepsHistory = ref([120, 340, 180, 450, 280, 380, 450])

// 计算步数进度
const stepsProgress = computed(() => {
  return Math.min(Math.round((todaySteps.value / stepsGoal.value) * 100), 100)
})

// 心率图表数据点
const heartRateChartPoints = computed(() => {
  const points = []
  const width = 40
  const height = 20
  const padding = 2

  heartRateHistory.value.forEach((rate, index) => {
    const x = padding + (index / (heartRateHistory.value.length - 1)) * (width - padding * 2)
    const normalizedRate = (rate - 60) / 60 // 假设心率范围60-120
    const y = height - padding - (normalizedRate * (height - padding * 2))
    points.push(`${x},${Math.max(padding, Math.min(height - padding, y))}`)
  })

  return points.join(' ')
})

// 心率图表最后一个点的Y坐标
const heartRateLastPoint = computed(() => {
  const lastRate = heartRateHistory.value[heartRateHistory.value.length - 1]
  const normalizedRate = (lastRate - 60) / 60
  const y = 20 - 2 - (normalizedRate * 16)
  return Math.max(2, Math.min(18, y))
})

// 步数图表柱状数据
const stepsChartBars = computed(() => {
  const bars = []
  const maxSteps = Math.max(...stepsHistory.value)

  stepsHistory.value.forEach((steps, index) => {
    const x = 4 + index * 6
    const normalizedHeight = (steps / maxSteps) * 14
    const height = Math.max(2, normalizedHeight)
    const y = 18 - height

    bars.push({
      x,
      y,
      width: 4,
      height
    })
  })

  return bars
})

// 波形图表数据
const healthTrendData = ref([])
const healthTrendContext = ref(null)

// 生成实际日期数据
const generateWeekDays = () => {
  const days = []
  const today = new Date()
  const dayNames = ['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat']

  // 生成前后各3天，共7天
  for (let i = -3; i <= 3; i++) {
    const date = new Date(today)
    date.setDate(today.getDate() + i)

    days.push({
      name: dayNames[date.getDay()],
      number: date.getDate().toString(),
      fullDate: date.toISOString().split('T')[0],
      isToday: i === 0
    })
  }

  return days
}

const weekDays = ref(generateWeekDays())

// 监测时长
const monitoringDuration = computed(() => {
  const hours = Math.floor(Math.random() * 12) + 6
  return { hours }
})

// 风险等级
const riskLevel = computed(() => {
  const level = Math.floor(exposureLevel.value / 10) + 1
  return {
    level: Math.min(level, 4),
    icon: level <= 2 ? '✅' : level === 3 ? '⚠️' : '🚨'
  }
})

// 健康建议列表
const healthAdviceList = computed(() => {
  const advice = []

  // 心率相关建议
  if (heartRate.value > 100) {
    advice.push({
      icon: '❤️',
      text: '心率偏高提醒',
      detail: `当前心率${heartRate.value}BPM，建议适当休息放松`,
      type: 'important',
      priority: 'high',
      actionText: '查看详情'
    })
  } else if (heartRate.value < 60) {
    advice.push({
      icon: '💙',
      text: '心率偏低提醒',
      detail: `当前心率${heartRate.value}BPM，建议适当运动`,
      type: 'suggestion',
      priority: 'medium',
      actionText: '运动建议'
    })
  }

  // 步数相关建议
  if (todaySteps.value < stepsGoal.value * 0.5) {
    advice.push({
      icon: '🏃',
      text: '运动量不足',
      detail: `今日步数${todaySteps.value}步，距离目标还差${stepsGoal.value - todaySteps.value}步`,
      type: 'suggestion',
      priority: 'medium',
      actionText: '开始运动'
    })
  }

  // 血氧相关建议
  if (bloodOxygen.value < 95) {
    advice.push({
      icon: '🫁',
      text: '血氧偏低提醒',
      detail: `当前血氧${bloodOxygen.value}%，建议深呼吸或到户外活动`,
      type: 'important',
      priority: 'high',
      actionText: '呼吸练习'
    })
  }

  // 辐射暴露建议
  if (exposureLevel.value > 20) {
    advice.push({
      icon: '⚠️',
      text: '辐射暴露偏高',
      detail: '建议减少在高辐射环境中的停留时间',
      type: 'important',
      priority: 'high',
      actionText: '防护建议'
    })
  }

  // 水分补充建议
  advice.push({
    icon: '💧',
    text: '保持充足水分',
    detail: '今日饮水量不足，建议再饮用500ml水',
    type: 'info',
    priority: 'low',
    actionText: '设置提醒'
  })

  // 睡眠建议
  advice.push({
    icon: '😴',
    text: '睡眠质量分析',
    detail: '昨晚深度睡眠时间较短，建议调整作息',
    type: 'suggestion',
    priority: 'medium',
    actionText: '睡眠建议'
  })

  // 营养建议
  if (Math.random() > 0.5) {
    advice.push({
      icon: '🍎',
      text: '营养摄入建议',
      detail: '今日蛋白质摄入不足，建议增加优质蛋白',
      type: 'info',
      priority: 'low',
      actionText: '营养计划'
    })
  }

  return advice.slice(0, 5) // 最多显示5条建议
})

// 日期选择函数
const selectDate = (day, index) => {
  weekDays.value.forEach(d => d.isToday = false)
  day.isToday = true

  // 滚动到选中的日期
  scrollLeft.value = index * 80 - 160

  console.log('选择日期:', day)
  toastManager.success(`已切换到 ${day.name} ${day.number}日 的健康数据`, {
    duration: 2000,
    showCountdown: false
  })
}

// 当前日期格式化
const formatCurrentDate = () => {
  const today = new Date()
  const month = today.getMonth() + 1
  const date = today.getDate()
  return `${month}月${date}日`
}

// 时间格式化函数
const formatTime = (timestamp) => {
  const date = new Date(timestamp)
  const hours = date.getHours()
  const minutes = date.getMinutes()
  const ampm = hours >= 12 ? 'PM' : 'AM'
  const displayHours = hours % 12 || 12
  return `${displayHours}:${minutes.toString().padStart(2, '0')} ${ampm} - ${displayHours + 1}:${minutes.toString().padStart(2, '0')} ${ampm}`
}

// 日期格式化函数
const formatDate = (timestamp) => {
  const date = new Date(timestamp)
  const options = { 
    weekday: 'long', 
    year: 'numeric', 
    month: 'long', 
    day: 'numeric' 
  }
  return date.toLocaleDateString('zh-CN', options)
}

// 健康状态文本
const getHealthStatusText = (score) => {
  if (score >= 90) return '优秀状态'
  if (score >= 80) return '良好状态'
  if (score >= 70) return '一般状态'
  return '需要关注'
}

// 心率状态判断
const getHeartRateStatus = (rate) => {
  if (rate < 60) return '偏低'
  if (rate > 100) return '偏高'
  return '正常'
}

// 血氧状态判断
const getOxygenStatus = (oxygen) => {
  if (oxygen >= 98) return '优秀'
  if (oxygen >= 95) return '正常'
  if (oxygen >= 90) return '偏低'
  return '异常'
}

// 获取辐射状态
const getRadiationStatus = (value) => {
  if (value <= 5) return '安全'
  if (value <= 15) return '正常'
  if (value <= 25) return '注意'
  if (value <= 35) return '警告'
  return '危险'
}

// 获取辐射级别
const getRadiationLevel = (value) => {
  if (value <= 5) return '低'
  if (value <= 15) return '中低'
  if (value <= 25) return '中'
  if (value <= 35) return '中高'
  return '高'
}

// 指标卡片点击事件
const onMetricTap = (metricType) => {
  console.log('点击了指标:', metricType)

  const metricNames = {
    heartRate: '心率监测',
    steps: '步数统计',
    oxygen: '血氧饱和度'
  }

  const metricName = metricNames[metricType] || metricType
  toastManager.info(`正在打开${metricName}详情页面...`, {
    duration: 2500,
    showCountdown: false
  })
}

// 用户头像点击事件
const onProfileTap = () => {
  toastManager.info('个人健康档案功能开发中...', {
    duration: 2000,
    showCountdown: false
  })
}

// 增强的交互动画方法
const activeCard = ref('')
const hoveredCard = ref('')

const onCardTouchStart = (cardType) => {
  cardTouchActive.value = true
  activeCard.value = cardType

  // 触觉反馈
  if (uni.vibrateShort) {
    uni.vibrateShort({
      type: 'light'
    })
  }
}

const onCardTouchEnd = () => {
  setTimeout(() => {
    cardTouchActive.value = false
    activeCard.value = ''
  }, 150)
}

const onCardHover = (cardType, isHover) => {
  if (isHover) {
    hoveredCard.value = cardType
  } else {
    hoveredCard.value = ''
  }
}

const onCardLongPress = (metricType) => {
  // 长按显示详细信息
  toastManager.info(`长按查看${metricType === 'heartRate' ? '心率' : '步数'}详细数据`, {
    duration: 2000,
    showCountdown: false
  })

  // 触觉反馈
  if (uni.vibrateLong) {
    uni.vibrateLong()
  }
}

// 实时数据更新
const updateRealTimeData = () => {
  // 模拟心率变化
  const newHeartRate = heartRate.value + (Math.random() - 0.5) * 10
  heartRate.value = Math.max(60, Math.min(120, Math.round(newHeartRate)))

  // 更新心率历史数据
  heartRateHistory.value.shift()
  heartRateHistory.value.push(heartRate.value)

  // 模拟步数增加
  if (Math.random() > 0.7) {
    const increment = Math.floor(Math.random() * 5) + 1
    todaySteps.value += increment

    // 更新步数历史数据
    stepsHistory.value.shift()
    stepsHistory.value.push(todaySteps.value)
  }
}

// 启动实时数据更新
let dataUpdateInterval = null
const startRealTimeUpdates = () => {
  dataUpdateInterval = setInterval(updateRealTimeData, 3000) // 每3秒更新一次
}

const stopRealTimeUpdates = () => {
  if (dataUpdateInterval) {
    clearInterval(dataUpdateInterval)
    dataUpdateInterval = null
  }
}







// 初始化健康趋势图表 - 参考仪表盘实现
const initHealthTrendChart = () => {
  const ctx = uni.createCanvasContext('healthTrendChart')
  healthTrendContext.value = ctx

  // 生成波形数据
  generateHealthTrendData()

  // 绘制波形图表
  drawHealthTrendChart()
}

// 生成健康波形数据
const generateHealthTrendData = () => {
  const hours = selectedPeriod.value === '24小时' ? 24 : 72 // 3天 = 72小时
  const pointsPerHour = 4 // 每小时4个数据点
  const totalPoints = hours * pointsPerHour
  const data = []

  for (let i = 0; i < totalPoints; i++) {
    const date = new Date()
    date.setMinutes(date.getMinutes() - (totalPoints - 1 - i) * 15) // 每15分钟一个点

    // 根据选择的数据类型生成不同的波形数据
    let value
    const timeOfDay = (date.getHours() + date.getMinutes() / 60) % 24 // 一天中的小时

    switch (selectedDataType.value) {
      case 'heartRate':
        // 心率：白天高，夜晚低，有自然波动
        const baseHeartRate = 60 + Math.sin((timeOfDay - 6) * Math.PI / 12) * 15
        value = baseHeartRate + Math.sin(i * 0.1) * 8 + Math.random() * 6
        value = Math.max(50, Math.min(120, value))
        break
      case 'bloodOxygen':
        // 血氧：相对稳定，轻微波动
        value = 97 + Math.sin(i * 0.05) * 1.5 + Math.random() * 1
        value = Math.max(94, Math.min(100, value))
        break
      case 'steps':
        // 步数：白天活跃，夜晚为0，累积性质
        if (timeOfDay >= 22 || timeOfDay <= 6) {
          value = Math.random() * 50 // 夜晚很少步数
        } else {
          const activityLevel = Math.sin((timeOfDay - 6) * Math.PI / 16) * 0.8 + 0.2
          value = activityLevel * (200 + Math.random() * 100)
        }
        break
      default:
        value = 80 + Math.sin(i * 0.3) * 15 + Math.random() * 10
    }

    data.push({
      timestamp: date.getTime(),
      value: Math.max(0, value),
      hour: timeOfDay,
      date: date
    })
  }

  healthTrendData.value = data
}

// 绘制健康波形图表 - 完全参考仪表盘样式
const drawHealthTrendChart = () => {
  if (!healthTrendContext.value || !healthTrendData.value.length) return

  const ctx = healthTrendContext.value
  const width = chartWidth.value
  const height = 280
  const padding = 40
  const chartWidth_inner = width - padding * 2
  const chartHeight = height - padding * 2

  // 清空画布
  ctx.clearRect(0, 0, width, height)

  const data = healthTrendData.value
  if (data.length < 2) return

  // 根据数据类型设置不同的Y轴范围和颜色 - 参考仪表盘配色
  let minValue, maxValue, strokeColor, gradientColor1, gradientColor2

  switch (selectedDataType.value) {
    case 'heartRate':
      minValue = 50
      maxValue = 120
      strokeColor = '#ef4444'
      gradientColor1 = 'rgba(239, 68, 68, 0.8)'
      gradientColor2 = 'rgba(239, 68, 68, 0.1)'
      break
    case 'bloodOxygen':
      minValue = 94
      maxValue = 100
      strokeColor = '#0288D1'  // 使用仪表盘的蓝色
      gradientColor1 = 'rgba(2, 136, 209, 0.8)'
      gradientColor2 = 'rgba(2, 136, 209, 0.1)'
      break
    case 'steps':
      const values = data.map(d => d.value)
      minValue = 0
      maxValue = Math.max(...values) * 1.1 || 1000
      strokeColor = '#4CAF50'  // 使用仪表盘的绿色
      gradientColor1 = 'rgba(76, 175, 80, 0.6)'
      gradientColor2 = 'rgba(76, 175, 80, 0.1)'
      break
    default:
      const allValues = data.map(d => d.value)
      minValue = Math.min(...allValues)
      maxValue = Math.max(...allValues)
      strokeColor = '#0288D1'
      gradientColor1 = 'rgba(2, 136, 209, 0.8)'
      gradientColor2 = 'rgba(2, 136, 209, 0.1)'
  }

  const valueRange = maxValue - minValue || 1

  // 绘制背景网格 - 与仪表盘完全一致
  ctx.setStrokeStyle('rgba(0, 0, 0, 0.05)')
  ctx.setLineWidth(0.5)

  // 绘制水平网格线
  for (let i = 1; i < 5; i++) {
    const y = padding + (i * chartHeight / 5)
    ctx.beginPath()
    ctx.moveTo(padding, y)
    ctx.lineTo(width - padding, y)
    ctx.stroke()
  }

  // 绘制垂直网格线
  const verticalLines = 6
  for (let i = 1; i < verticalLines; i++) {
    const x = padding + (i * chartWidth_inner / (verticalLines - 1))
    ctx.beginPath()
    ctx.moveTo(x, padding)
    ctx.lineTo(x, height - padding)
    ctx.stroke()
  }

  // 绘制坐标轴 - 与仪表盘样式一致
  ctx.setStrokeStyle('#e5e7eb')
  ctx.setLineWidth(1)

  // X轴
  ctx.beginPath()
  ctx.moveTo(padding, height - padding)
  ctx.lineTo(width - padding, height - padding)
  ctx.stroke()

  // Y轴
  ctx.beginPath()
  ctx.moveTo(padding, padding)
  ctx.lineTo(padding, height - padding)
  ctx.stroke()

  // Y轴刻度和标签 - 与仪表盘样式一致
  ctx.setFillStyle('#64748b')
  ctx.setFontSize(11)
  ctx.setTextAlign('right')

  for (let i = 0; i <= 4; i++) {
    let value = maxValue - (i / 4) * valueRange
    let displayValue

    // 根据数据类型格式化显示值
    switch (selectedDataType.value) {
      case 'heartRate':
        displayValue = Math.round(value).toString()
        break
      case 'bloodOxygen':
        displayValue = value.toFixed(2)
        break
      case 'steps':
        displayValue = Math.round(value).toString()
        break
      default:
        displayValue = value.toFixed(2)
    }

    const y = padding + (i / 4) * chartHeight

    // 绘制Y轴刻度线
    ctx.setStrokeStyle('#e5e7eb')
    ctx.setLineWidth(1)
    ctx.beginPath()
    ctx.moveTo(padding - 5, y)
    ctx.lineTo(padding, y)
    ctx.stroke()

    // 绘制Y轴标签
    ctx.setFillStyle('#64748b')
    ctx.fillText(displayValue, padding - 8, y + 4)
  }

  // X轴刻度和标签 - 与仪表盘样式一致
  ctx.setTextAlign('center')
  ctx.setFillStyle('#64748b')
  ctx.setFontSize(11)

  const timeLabels = 5
  for (let i = 0; i < timeLabels; i++) {
    const dataIndex = Math.floor(i * (data.length - 1) / (timeLabels - 1))
    const item = data[dataIndex]
    const x = padding + (i * chartWidth_inner / (timeLabels - 1))

    // 绘制X轴刻度线
    ctx.setStrokeStyle('#e5e7eb')
    ctx.setLineWidth(1)
    ctx.beginPath()
    ctx.moveTo(x, height - padding)
    ctx.lineTo(x, height - padding + 5)
    ctx.stroke()

    if (item && item.date) {
      let timeStr
      if (selectedPeriod.value === '24小时') {
        timeStr = item.date.getHours().toString().padStart(2, '0') + ':' +
                  item.date.getMinutes().toString().padStart(2, '0')
      } else {
        timeStr = (item.date.getMonth() + 1) + '/' + item.date.getDate()
      }

      // 绘制X轴标签
      ctx.setFillStyle('#64748b')
      ctx.fillText(timeStr, x, height - padding + 18)
    }
  }

  // 根据图表类型绘制不同的图形
  if (selectedChartType.value === 'bar') {
    // 全新设计的现代化柱状图
    const totalBars = data.length
    const availableWidth = chartWidth_inner * 0.95
    const barWidth = Math.max(3, Math.min(12, availableWidth / totalBars * 0.8))
    const barSpacing = availableWidth / totalBars
    const startX = padding + (chartWidth_inner - availableWidth) / 2

    data.forEach((item, index) => {
      const x = startX + (index * barSpacing) + (barSpacing - barWidth) / 2
      const barHeight = Math.max(2, ((item.value - minValue) / valueRange) * chartHeight)
      const y = height - padding - barHeight

      // 简洁的单色渐变
      const gradient = ctx.createLinearGradient(0, y, 0, height - padding)
      gradient.addColorStop(0, strokeColor)
      gradient.addColorStop(1, gradientColor2)

      // 绘制简洁的矩形柱子
      ctx.setFillStyle(gradient)
      ctx.fillRect(x, y, barWidth, barHeight)

      // 添加顶部圆角效果
      if (barHeight > 4) {
        const radius = Math.min(barWidth / 2, 2)
        ctx.beginPath()
        ctx.moveTo(x, y + radius)
        ctx.quadraticCurveTo(x, y, x + radius, y)
        ctx.lineTo(x + barWidth - radius, y)
        ctx.quadraticCurveTo(x + barWidth, y, x + barWidth, y + radius)
        ctx.lineTo(x + barWidth, y + radius * 2)
        ctx.lineTo(x, y + radius * 2)
        ctx.closePath()
        ctx.setFillStyle(strokeColor)
        ctx.fill()
      }

      // 添加微妙的高亮
      if (barHeight > 3) {
        const highlightGradient = ctx.createLinearGradient(0, y, 0, y + Math.min(barHeight * 0.3, 8))
        highlightGradient.addColorStop(0, 'rgba(255, 255, 255, 0.3)')
        highlightGradient.addColorStop(1, 'rgba(255, 255, 255, 0)')

        ctx.setFillStyle(highlightGradient)
        ctx.fillRect(x, y, barWidth, Math.min(barHeight * 0.3, 8))
      }
    })
  } else {
    // 绘制折线图 - 完全参考仪表盘的绘制方式

    // 第一步：绘制填充区域
    ctx.beginPath()
    ctx.moveTo(padding, height - padding)

    data.forEach((item, index) => {
      const x = padding + (index / (data.length - 1)) * chartWidth_inner
      const y = height - padding - ((item.value - minValue) / valueRange) * chartHeight

      if (index === 0) {
        ctx.lineTo(x, y)
      } else {
        // 使用贝塞尔曲线创建平滑效果
        const prevX = padding + ((index - 1) / (data.length - 1)) * chartWidth_inner
        const prevY = height - padding - ((data[index - 1].value - minValue) / valueRange) * chartHeight
        const cpX = (prevX + x) / 2
        ctx.bezierCurveTo(cpX, prevY, cpX, y, x, y)
      }
    })

    ctx.lineTo(width - padding, height - padding)

    // 创建渐变填充
    const gradient = ctx.createLinearGradient(0, padding, 0, height - padding)
    gradient.addColorStop(0, gradientColor1)
    gradient.addColorStop(1, gradientColor2)
    ctx.setFillStyle(gradient)
    ctx.fill()

    // 第二步：绘制线条
    ctx.beginPath()
    ctx.setStrokeStyle(strokeColor)
    ctx.setLineWidth(3)
    ctx.setLineCap('round')
    ctx.setLineJoin('round')

    data.forEach((item, index) => {
      const x = padding + (index / (data.length - 1)) * chartWidth_inner
      const y = height - padding - ((item.value - minValue) / valueRange) * chartHeight

      if (index === 0) {
        ctx.moveTo(x, y)
      } else {
        const prevX = padding + ((index - 1) / (data.length - 1)) * chartWidth_inner
        const prevY = height - padding - ((data[index - 1].value - minValue) / valueRange) * chartHeight
        const cpX = (prevX + x) / 2
        ctx.bezierCurveTo(cpX, prevY, cpX, y, x, y)
      }
    })
    ctx.stroke()

    // 第三步：绘制数据点
    const pointsToShow = Math.min(5, data.length)
    for (let i = data.length - pointsToShow; i < data.length; i++) {
      const x = padding + (i / (data.length - 1)) * chartWidth_inner
      const y = height - padding - ((data[i].value - minValue) / valueRange) * chartHeight

      // 外圈白色
      ctx.beginPath()
      ctx.arc(x, y, 4, 0, 2 * Math.PI)
      ctx.setFillStyle('#ffffff')
      ctx.fill()

      // 内圈颜色
      ctx.beginPath()
      ctx.arc(x, y, 3, 0, 2 * Math.PI)
      ctx.setFillStyle(strokeColor)
      ctx.fill()
    }
  }

  // 绘制完成 - 参考仪表盘，不显示坐标轴标签，保持简洁
  ctx.draw()
}

// 初始化心率波形
const initHeartWave = () => {
  const canvas = uni.createCanvasContext('heartWaveCanvas')
  heartWaveContext.value = canvas

  // 初始化波形数据
  heartWaveData.value = Array(100).fill(0).map(() => Math.random() * 20 + 10)

  // 开始绘制动画
  drawHeartWave()
}

// 绘制心率波形
const drawHeartWave = () => {
  if (!heartWaveContext.value) return

  const ctx = heartWaveContext.value

  // 清除画布
  ctx.clearRect(0, 0, 300, 40)

  // 设置样式
  ctx.setStrokeStyle('rgba(255, 255, 255, 0.9)')
  ctx.setLineWidth(2)
  ctx.setLineCap('round')
  ctx.setLineJoin('round')

  // 绘制心率波形
  ctx.beginPath()

  const width = 300
  const height = 40
  const dataLength = heartWaveData.value.length

  for (let i = 0; i < dataLength; i++) {
    const x = (i / dataLength) * width
    const y = height - (heartWaveData.value[i] / 40) * height

    if (i === 0) {
      ctx.moveTo(x, y)
    } else {
      ctx.lineTo(x, y)
    }
  }

  ctx.stroke()
  ctx.draw()

  // 更新数据（模拟实时心率）
  updateHeartWaveData()

  // 继续动画
  heartWaveAnimationId.value = setTimeout(() => {
    drawHeartWave()
  }, 100)
}

// 更新心率波形数据
const updateHeartWaveData = () => {
  // 移除第一个数据点
  heartWaveData.value.shift()

  // 添加新的数据点（基于当前心率生成）
  const baseValue = heartRate.value / 4 // 将心率映射到合适的范围
  const variation = Math.random() * 10 - 5 // 添加随机变化
  const newValue = Math.max(5, Math.min(35, baseValue + variation))

  heartWaveData.value.push(newValue)
}

// 图表选择器函数
const selectPeriod = (period) => {
  selectedPeriod.value = period
  // 重新生成数据并绘制图表
  generateHealthTrendData()
  setTimeout(() => {
    drawHealthTrendChart()
  }, 100)
}

const selectDataType = (dataType) => {
  selectedDataType.value = dataType
  // 重新生成数据并绘制图表
  generateHealthTrendData()
  setTimeout(() => {
    drawHealthTrendChart()
  }, 100)
}

const selectChartType = (chartType) => {
  selectedChartType.value = chartType
  // 重新绘制图表
  setTimeout(() => {
    drawHealthTrendChart()
  }, 100)
}

// 页面生命周期
onMounted(() => {
  console.log('健康页面已加载')

  // 启动实时数据更新
  startRealTimeUpdates()

  // 设置响应式图表宽度
  const setChartWidth = () => {
    try {
      const systemInfo = uni.getSystemInfoSync()
      const screenWidth = systemInfo.screenWidth
      // 减去容器的padding和margin
      chartWidth.value = Math.min(screenWidth - 72, 320)
    } catch (e) {
      chartWidth.value = 320
    }
  }

  setChartWidth()

  // 初始化健康趋势图表
  setTimeout(() => {
    initHealthTrendChart()
  }, 300)

  // 初始化心率波形
  setTimeout(() => {
    initHeartWave()
  }, 500)

  // 模拟数据更新
  setInterval(() => {
    heartRate.value = 85 + Math.floor(Math.random() * 10)
    bloodOxygen.value = 96 + Math.floor(Math.random() * 4)
  }, 3000)
})

onUnmounted(() => {
  console.log('健康页面已卸载')

  // 停止实时数据更新
  stopRealTimeUpdates()

  // 清理动画
  if (heartWaveAnimationId.value) {
    clearTimeout(heartWaveAnimationId.value)
  }
})
</script>

<style scoped>
/* 健康页面容器 - 参考仪表盘设计风格 */
.health-container {
  width: 100%;
  min-height: 100vh;
  margin: 0;
  padding: 0 0 120px 0;
  display: flex;
  flex-direction: column;
  background: #ffffff;
  position: relative;
  box-sizing: border-box;
  font-family: -apple-system, BlinkMacSystemFont, 'SF Pro Display', sans-serif;
  overflow-x: hidden;
  transition: all 0.6s cubic-bezier(0.4, 0, 0.2, 1);
}



.health-container::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 200px;
  background: radial-gradient(ellipse at top, rgba(33, 150, 243, 0.05) 0%, transparent 70%);
  pointer-events: none;
}

/* 红色圆角头部区域 */
.blue-rounded-header {
  background: linear-gradient(135deg, #ef4444 0%, #dc2626 50%, #b91c1c 100%);
  margin: 16px;
  border-radius: 16px;
  padding: 20px;
  position: relative;
  box-shadow: 0 6px 24px rgba(239, 68, 68, 0.25);
  overflow: hidden;
}



/* 头部装饰粒子 */
.header-decoration {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  pointer-events: none;
  overflow: hidden;
}

.floating-particle {
  position: absolute;
  width: 6px;
  height: 6px;
  background: rgba(255, 255, 255, 0.3);
  border-radius: 50%;
  animation: float 4s ease-in-out infinite;
}

.particle-1 {
  top: 20%;
  left: 15%;
  animation-delay: 0s;
}

.particle-2 {
  top: 60%;
  right: 20%;
  animation-delay: 1.5s;
}

.particle-3 {
  bottom: 30%;
  left: 70%;
  animation-delay: 3s;
}



.blue-rounded-header::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(45deg, rgba(255, 255, 255, 0.1) 0%, transparent 50%);
  pointer-events: none;
}

.header-content-new {
  display: flex;
  justify-content: space-between;
  align-items: center;
  position: relative;
  z-index: 1;
}

.header-content-centered {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  position: relative;
  z-index: 1;
  text-align: center;
}

.header-content-centered .header-avatar {
  position: absolute;
  top: 0;
  right: 0;
}

.header-content-centered .date-badge {
  margin-bottom: 8px;
}

.header-content-centered .main-title-new {
  margin: 0;
}

.header-content-inline {
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  z-index: 1;
  gap: 12px;
}

.header-content-inline .header-avatar {
  position: absolute;
  top: 0;
  right: 0;
}

.header-content-balanced {
  display: flex;
  align-items: center;
  justify-content: space-between;
  position: relative;
  z-index: 1;
  width: 100%;
  height: 100%;
}

.header-content-balanced .date-badge {
  align-self: center;
}

.header-content-balanced .main-title-new {
  position: absolute;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
  margin: 0;
}

.header-content-balanced .header-avatar {
  align-self: center;
}

.header-main-info {
  flex: 1;
}

.date-badge {
  display: inline-block;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 12px;
  padding: 6px 12px;
  margin-bottom: 8px;
  backdrop-filter: blur(10px);
}

.date-text-new {
  font-size: 12px;
  font-weight: 600;
  color: rgba(255, 255, 255, 0.9);
  letter-spacing: 0.5px;
}

.main-title-new {
  font-size: 24px;
  font-weight: 700;
  color: #ffffff;
  text-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
}

.header-avatar {
  margin-left: 16px;
}

.avatar-circle {
  width: 48px;
  height: 48px;
  border-radius: 24px;
  background: rgba(255, 255, 255, 0.15);
  backdrop-filter: blur(10px);
  display: flex;
  align-items: center;
  justify-content: center;
  border: 2px solid rgba(255, 255, 255, 0.2);
  transition: all 0.3s ease;
}

.avatar-circle:active {
  transform: scale(0.95);
  background: rgba(255, 255, 255, 0.25);
}

.avatar-circle svg {
  width: 24px;
  height: 24px;
  color: #ffffff;
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  max-width: 100%;
}

.header-left {
  flex: 1;
}

/* 日期信息卡片 - 圆角矩形设计 */
.date-info-card {
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(10px);
  border-radius: 12px;
  padding: 8px 16px;
  margin-bottom: 12px;
  border: 1px solid rgba(0, 0, 0, 0.05);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  display: inline-block;
}

.date-text {
  font-size: 14px;
  color: #64748b;
  font-weight: 600;
  letter-spacing: 0.5px;
}

.main-title {
  font-size: 32px;
  font-weight: 700;
  color: #0f172a;
  letter-spacing: -0.5px;
  line-height: 1.2;
}

.header-right {
  display: flex;
  align-items: center;
}

.profile-avatar {
  width: 44px;
  height: 44px;
  border-radius: 22px;
  background: linear-gradient(135deg, #2196F3 0%, #1976D2 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 4px 16px rgba(33, 150, 243, 0.3);
  transition: all 0.3s ease;
  cursor: pointer;
  border: 2px solid rgba(255, 255, 255, 0.8);
}

.profile-avatar:active {
  transform: scale(0.95);
}

.profile-avatar:hover {
  box-shadow: 0 6px 20px rgba(33, 150, 243, 0.4);
  transform: translateY(-1px);
}

.profile-avatar svg {
  width: 20px;
  height: 20px;
  color: #ffffff;
}
/* 优化的日期选择器 - 参考仪表盘设计 */
.date-selector-container {
  padding: 20px 0;
  background: linear-gradient(180deg, #FFFFFF 0%, #F8FAFC 100%);
  position: relative;
}



.date-selector {
  display: flex;
  gap: 8px;
  padding: 0 15px;
  overflow-x: auto;
  scrollbar-width: none;
  -ms-overflow-style: none;
  justify-content: space-between;
}

.date-selector::-webkit-scrollbar {
  display: none;
}

.date-item {
  min-width: 72px;
  background: rgba(255, 255, 255, 0.9);
  border-radius: 16px;
  padding: 0;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  flex-shrink: 0;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  border: 1px solid rgba(0, 0, 0, 0.04);
  backdrop-filter: blur(10px);
  height: 82px;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  overflow: hidden;
}

/* 日期项动画 */
.date-item-animate {
  opacity: 0;
  transform: translateY(30px) scale(0.8);
  transition: all 0.5s cubic-bezier(0.4, 0, 0.2, 1);
}

.date-item-animate.item-visible {
  opacity: 1;
  transform: translateY(0) scale(1);
}

/* 波纹效果 */
.ripple-effect {
  position: absolute;
  top: 50%;
  left: 50%;
  width: 0;
  height: 0;
  background: rgba(255, 255, 255, 0.3);
  border-radius: 50%;
  transform: translate(-50%, -50%);
  animation: ripple 2s ease-out infinite;
}

.date-item-content {
  text-align: center;
  padding: 18px 14px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  width: 100%;
}

.date-item:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 16px rgba(33, 150, 243, 0.1);
}

.date-item.active {
  background: linear-gradient(135deg, #2196F3 0%, #1976D2 100%);
  transform: scale(1.05) translateY(-2px);
  box-shadow: 0 8px 24px rgba(33, 150, 243, 0.3);
  border: 1px solid rgba(33, 150, 243, 0.3);
}

.day-name {
  display: block;
  font-size: 12px;
  color: #64748b;
  font-weight: 600;
  margin-bottom: 4px;
  letter-spacing: 0.3px;
  text-transform: uppercase;
}

.date-item.active .day-name {
  color: rgba(255, 255, 255, 0.9);
}

.day-number {
  display: block;
  font-size: 20px;
  font-weight: 700;
  color: #0f172a;
  line-height: 1;
}

.date-item.active .day-number {
  color: #ffffff;
}

/* 健康指标卡片容器 - 2x2布局 */
.health-metrics-container {
  display: grid;
  grid-template-columns: 1fr 1fr;
  grid-template-rows: 1fr 1fr;
  gap: 16px;
  margin: 0 20px 24px;
  height: 340px;
}

/* 卡片容器动画 */
.metrics-animate {
  opacity: 0;
  transform: translateY(40px);
  transition: all 0.8s cubic-bezier(0.4, 0, 0.2, 1);
}

.metrics-animate.metrics-visible {
  opacity: 1;
  transform: translateY(0);
}

/* 卡片动画 */
.card-animate {
  opacity: 0;
  transform: translateY(50px) scale(0.9);
  transition: all 0.6s cubic-bezier(0.4, 0, 0.2, 1);
}

.card-animate.card-visible {
  opacity: 1;
  transform: translateY(0) scale(1);
}

/* 健康卡片基础样式 - 参考仪表盘设计 */
.health-card {
  border-radius: 20px;
  padding: 20px;
  position: relative;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  overflow: hidden;
  min-height: 120px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  backdrop-filter: blur(20px);
  background: rgba(255, 255, 255, 0.95);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.health-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.12);
}

.health-card:active {
  transform: translateY(0);
}

/* 卡片头部样式 */
.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.header-left-section {
  display: flex;
  align-items: center;
  gap: 12px;
}

.mini-chart {
  width: 40px;
  height: 20px;
  opacity: 0.8;
}

.heart-rate-mini-chart svg,
.steps-mini-chart svg {
  width: 100%;
  height: 100%;
}

.metric-icon {
  width: 32px;
  height: 32px;
  border-radius: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(33, 150, 243, 0.1);
  transition: all 0.3s ease;
}

.metric-icon svg {
  width: 18px;
  height: 18px;
  color: #2196F3;
}

.card-label {
  font-size: 13px;
  font-weight: 600;
  color: #64748b;
  text-transform: uppercase;
  letter-spacing: 0.8px;
}

/* 心率卡片 - 使用红色主题 */
.heart-rate-card {
  background: linear-gradient(135deg, #F44336 0%, #E53935 50%, #C62828 100%);
  color: #ffffff;
  box-shadow: 0 8px 32px rgba(244, 67, 54, 0.3);
  position: relative;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.heart-rate-card .metric-icon {
  background: rgba(255, 255, 255, 0.2);
}

.heart-rate-card .metric-icon svg {
  color: #ffffff;
}

.heart-rate-card .card-label {
  color: rgba(255, 255, 255, 0.9);
}

.heart-rate-card:hover {
  box-shadow: 0 12px 48px rgba(244, 67, 54, 0.4);
}

/* 步数卡片 - 右上角，使用绿色主题 */
.steps-card {
  background: linear-gradient(135deg, #4CAF50 0%, #388E3C 50%, #2E7D32 100%);
  color: #ffffff;
  box-shadow: 0 8px 32px rgba(76, 175, 80, 0.3);
  position: relative;
  overflow: hidden;
}

.steps-card .metric-icon {
  background: rgba(255, 255, 255, 0.2);
}

.steps-card .metric-icon svg {
  color: #ffffff;
}

.steps-card .card-label {
  color: rgba(255, 255, 255, 0.9);
}

.steps-card:hover {
  box-shadow: 0 12px 48px rgba(76, 175, 80, 0.4);
}

/* 血氧卡片 - 右下角，使用蓝色主题 */
.oxygen-card {
  background: linear-gradient(135deg, #2196F3 0%, #1976D2 50%, #0D47A1 100%);
  color: #ffffff;
  box-shadow: 0 8px 32px rgba(33, 150, 243, 0.3);
  position: relative;
  overflow: hidden;
}

.oxygen-card .metric-icon {
  background: rgba(255, 255, 255, 0.2);
}

.oxygen-card .metric-icon svg {
  color: #ffffff;
}

.oxygen-card .card-label {
  color: rgba(255, 255, 255, 0.9);
}

.oxygen-card:hover {
  box-shadow: 0 12px 48px rgba(33, 150, 243, 0.4);
}

/* 健康摘要卡片 */
.health-summary-card {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  border-radius: 20px;
  padding: 24px;
  margin: 0 20px 24px;
  border: 1px solid rgba(255, 255, 255, 0.2);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.08);
  position: relative;
  overflow: hidden;
}

.health-summary-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, #F44336 0%, #4CAF50 50%, #2196F3 100%);
  border-radius: 20px 20px 0 0;
}

.summary-header {
  display: flex;
  align-items: center;
  gap: 16px;
  margin-bottom: 24px;
}

.summary-icon {
  width: 40px;
  height: 40px;
  border-radius: 20px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 4px 16px rgba(102, 126, 234, 0.3);
}

.summary-icon svg {
  width: 20px;
  height: 20px;
  color: #ffffff;
}

.summary-title-section {
  flex: 1;
}

.summary-title {
  font-size: 18px;
  font-weight: 700;
  color: #0f172a;
  margin-bottom: 4px;
  display: block;
}

.summary-subtitle {
  font-size: 14px;
  color: #64748b;
  font-weight: 500;
}

.summary-metrics {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.summary-metric {
  display: flex;
  align-items: center;
  gap: 16px;
  padding: 16px;
  background: rgba(248, 250, 252, 0.8);
  border-radius: 16px;
  border: 1px solid rgba(0, 0, 0, 0.04);
  transition: all 0.3s ease;
}

.summary-metric:hover {
  background: rgba(248, 250, 252, 1);
  transform: translateY(-2px);
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.08);
}

.metric-icon-small {
  width: 32px;
  height: 32px;
  border-radius: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.summary-metric:nth-child(1) .metric-icon-small {
  background: rgba(244, 67, 54, 0.1);
}

.summary-metric:nth-child(1) .metric-icon-small svg {
  color: #F44336;
}

.summary-metric:nth-child(2) .metric-icon-small {
  background: rgba(76, 175, 80, 0.1);
}

.summary-metric:nth-child(2) .metric-icon-small svg {
  color: #4CAF50;
}

.summary-metric:nth-child(3) .metric-icon-small {
  background: rgba(255, 152, 0, 0.1);
}

.summary-metric:nth-child(3) .metric-icon-small svg {
  color: #FF9800;
}

.metric-icon-small svg {
  width: 16px;
  height: 16px;
}

.metric-info {
  flex: 1;
}

.metric-label {
  font-size: 14px;
  color: #64748b;
  font-weight: 500;
  margin-bottom: 4px;
  display: block;
}

.metric-value {
  font-size: 18px;
  font-weight: 700;
  color: #0f172a;
  display: block;
}

/* 卡片内容样式 */
.card-main {
  margin-top: 16px;
  z-index: 2;
  position: relative;
  flex: 1;
}

.card-content {
  margin-top: 16px;
  z-index: 2;
  position: relative;
  flex: 1;
}

/* 心率值区域 */
.heart-rate-value-section {
  display: flex;
  align-items: baseline;
  gap: 8px;
  margin-bottom: 8px;
}

.steps-value-section {
  display: flex;
  align-items: baseline;
  gap: 8px;
  margin-bottom: 12px;
}

.steps-value-section-inline,
.radiation-value-section-inline {
  display: flex;
  flex-direction: row;
  align-items: baseline;
  margin-bottom: 8px;
}

.radiation-chart {
  width: 60px;
  height: 20px;
  margin-top: 8px;
  opacity: 0.8;
}

.radiation-chart svg {
  width: 100%;
  height: 100%;
}

.radiation-progress {
  margin-top: 8px;
}

.radiation-fill {
  background: linear-gradient(90deg, rgba(255, 255, 255, 0.8) 0%, rgba(255, 255, 255, 1) 100%);
}

/* 辐射卡片白色文字样式 */
.radiation-icon-white {
  width: 20px;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.radiation-icon-white svg {
  width: 16px;
  height: 16px;
  color: #ffffff;
}

.card-label-white {
  font-size: 12px;
  font-weight: 600;
  color: #ffffff;
  margin-left: 8px;
}

.card-header-left {
  display: flex;
  align-items: center;
  justify-content: flex-start;
  margin-bottom: 8px;
}

/* 血氧卡片白色图标样式 */
.oxygen-icon-white {
  width: 20px;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.oxygen-icon-white svg {
  width: 16px;
  height: 16px;
  color: #ffffff;
}

.oxygen-value-section {
  display: flex;
  align-items: baseline;
  gap: 8px;
  margin-bottom: 8px;
}

.card-value {
  font-size: 42px;
  font-weight: 800;
  line-height: 1;
  text-shadow: 0 2px 6px rgba(0, 0, 0, 0.15);
  letter-spacing: -1.5px;
  color: #ffffff;
}

.card-value-small {
  font-size: 32px;
  font-weight: 700;
  line-height: 1;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  letter-spacing: -1px;
  color: #ffffff;
}

.card-unit {
  font-size: 14px;
  font-weight: 600;
  opacity: 0.9;
  text-transform: uppercase;
  letter-spacing: 0.8px;
  color: rgba(255, 255, 255, 0.9);
}

.card-unit-small {
  font-size: 12px;
  font-weight: 600;
  opacity: 0.85;
  text-transform: uppercase;
  letter-spacing: 0.6px;
  color: rgba(255, 255, 255, 0.85);
  margin-left: 6px;
}

/* 心率状态和趋势 */
.heart-rate-status {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 16px;
}

.status-text {
  font-size: 12px;
  font-weight: 600;
  color: rgba(255, 255, 255, 0.9);
  text-transform: uppercase;
  letter-spacing: 0.6px;
}

.status-text-small {
  font-size: 10px;
  font-weight: 600;
  color: rgba(255, 255, 255, 0.8);
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.trend-indicator {
  width: 16px;
  height: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.trend-indicator svg {
  width: 14px;
  height: 14px;
  color: rgba(255, 255, 255, 0.9);
}

.trend-indicator.up svg {
  color: #4CAF50;
}

.trend-indicator.down svg {
  color: #F44336;
}

/* 步数进度条 */
.steps-progress {
  margin-bottom: 16px;
}

.progress-bar {
  width: 100%;
  height: 4px;
  background: rgba(255, 255, 255, 0.3);
  border-radius: 2px;
  overflow: hidden;
  margin-bottom: 6px;
}

.progress-fill {
  height: 100%;
  background: rgba(255, 255, 255, 0.9);
  border-radius: 2px;
  transition: width 0.3s ease;
}

.progress-text {
  font-size: 11px;
  font-weight: 600;
  color: rgba(255, 255, 255, 0.8);
  text-transform: uppercase;
  letter-spacing: 0.6px;
}

/* 血氧状态 */
.oxygen-status {
  margin-bottom: 16px;
}

/* 心率趋势图表 */
.heart-rate-chart {
  margin-bottom: 16px;
  height: 40px;
  position: relative;
}

.heart-trend-svg {
  width: 100%;
  height: 100%;
  filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.1));
}

/* 心率波形样式 */
.heart-rate-waveform {
  margin: 8px 0;
  height: 40px;
  position: relative;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 8px;
  overflow: hidden;
}

.heart-wave-canvas {
  width: 100%;
  height: 100%;
  display: block;
}

/* 心率统计数据 */
.heart-rate-stats {
  display: flex;
  gap: 16px;
  margin-top: auto;
}

.stat-item {
  flex: 1;
  text-align: center;
}

.stat-label {
  display: block;
  font-size: 10px;
  font-weight: 600;
  color: rgba(255, 255, 255, 0.7);
  text-transform: uppercase;
  letter-spacing: 0.6px;
  margin-bottom: 4px;
}

.stat-value {
  display: block;
  font-size: 16px;
  font-weight: 700;
  color: rgba(255, 255, 255, 0.95);
}

/* 步数柱状图 */
.steps-bar-chart {
  position: absolute;
  bottom: 20px;
  right: 20px;
  width: 80px;
  height: 40px;
  z-index: 3;
}

.steps-svg {
  width: 100%;
  height: 100%;
  filter: drop-shadow(0 2px 4px rgba(255, 255, 255, 0.3));
}

/* 血氧环形图 */
.oxygen-ring-chart {
  position: absolute;
  bottom: 20px;
  right: 20px;
  width: 60px;
  height: 60px;
  z-index: 3;
}

.ring-svg {
  width: 100%;
  height: 100%;
  filter: drop-shadow(0 2px 4px rgba(255, 255, 255, 0.3));
}

.ring-center-text {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  text-align: center;
}

.ring-percentage {
  font-size: 12px;
  font-weight: 700;
  color: #ffffff;
}

/* 辐射剂量卡片样式 */
.radiation-card {
  background: linear-gradient(135deg, #FF9800 0%, #F57C00 50%, #E65100 100%);
  color: #ffffff;
  box-shadow: 0 8px 32px rgba(255, 152, 0, 0.3);
}

.radiation-icon {
  background: rgba(255, 255, 255, 0.2);
  color: #ffffff;
}

.radiation-value-section {
  display: flex;
  align-items: baseline;
  gap: 4px;
  margin-bottom: 8px;
}

.radiation-status {
  margin-bottom: 12px;
}

.radiation-indicator {
  display: flex;
  flex-direction: column;
  gap: 8px;
  margin-top: auto;
}

.radiation-bars {
  display: flex;
  gap: 4px;
  align-items: end;
  height: 20px;
}

.radiation-bar {
  flex: 1;
  height: 4px;
  background: rgba(255, 255, 255, 0.3);
  border-radius: 2px;
  transition: all 0.3s ease;
}

.radiation-bar.active {
  background: rgba(255, 255, 255, 0.9);
  height: 8px;
  box-shadow: 0 0 8px rgba(255, 255, 255, 0.5);
}

.radiation-bar:nth-child(1).active { height: 6px; }
.radiation-bar:nth-child(2).active { height: 8px; }
.radiation-bar:nth-child(3).active { height: 10px; }
.radiation-bar:nth-child(4).active { height: 12px; }
.radiation-bar:nth-child(5).active { height: 14px; }

.radiation-level-text {
  font-size: 11px;
  font-weight: 600;
  color: rgba(255, 255, 255, 0.9);
  text-align: center;
}

/* 现代化图表卡片 */
.chart-card-modern {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  border-radius: 20px;
  padding: 24px;
  margin: 0 20px 24px;
  border: 1px solid rgba(255, 255, 255, 0.2);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.08);
  position: relative;
  overflow: hidden;
}

.chart-card-modern::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, #2196F3 0%, #1976D2 50%, #2196F3 100%);
  border-radius: 20px 20px 0 0;
}

.chart-header-modern {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
}

.chart-title-section {
  display: flex;
  align-items: center;
  gap: 12px;
}

.chart-icon {
  width: 32px;
  height: 32px;
  border-radius: 16px;
  background: rgba(33, 150, 243, 0.1);
  display: flex;
  align-items: center;
  justify-content: center;
}

.chart-icon svg {
  width: 18px;
  height: 18px;
  color: #2196F3;
}

.chart-title-text {
  display: flex;
  flex-direction: column;
}

.chart-title {
  font-size: 18px;
  font-weight: 700;
  color: #0f172a;
  margin-bottom: 2px;
}

.chart-subtitle {
  font-size: 14px;
  color: #64748b;
  font-weight: 500;
}

.time-selector {
  display: flex;
  gap: 8px;
  background: rgba(33, 150, 243, 0.1);
  border-radius: 12px;
  padding: 4px;
}

.time-option {
  padding: 8px 16px;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 600;
  color: #64748b;
  cursor: pointer;
  transition: all 0.3s ease;
}

.time-option.active {
  background: #2196F3;
  color: #ffffff;
  box-shadow: 0 2px 8px rgba(33, 150, 243, 0.3);
}

.chart-container-modern {
  position: relative;
  margin: 0 4px 20px 4px;
  background: #ffffff;
  border-radius: 16px;
  padding: 20px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  border: 2px solid rgba(0, 0, 0, 0.06);
  backdrop-filter: blur(10px);
  overflow: hidden;
}

.chart-legend-modern {
  display: flex;
  gap: 20px;
  justify-content: center;
  margin-top: 16px;
  padding-top: 16px;
  border-top: 1px solid #f1f5f9;
}

/* 数据类型选择器 */
.data-type-selector {
  display: flex;
  gap: 8px;
  margin: 20px 4px 20px 4px;
  padding: 8px;
  background: #f8fafc;
  border-radius: 12px;
}

.data-type-option {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 4px;
  padding: 8px 4px;
  border-radius: 8px;
  transition: all 0.3s ease;
  cursor: pointer;
}

.data-type-option.active {
  background: #2563eb;
  color: #ffffff;
}

.data-type-icon {
  width: 20px;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.data-type-option.active .data-type-icon svg {
  color: #ffffff;
}

.data-type-option .data-type-icon svg {
  color: #64748b;
  width: 16px;
  height: 16px;
}

.data-type-text {
  font-size: 10px;
  font-weight: 600;
  color: #64748b;
}

.data-type-option.active .data-type-text {
  color: #ffffff;
}

/* 图表类型选择器 */
.chart-type-selector {
  display: flex;
  gap: 8px;
  margin: 0 4px 20px 4px;
  padding: 4px;
  background: #f1f5f9;
  border-radius: 8px;
}

.chart-type-option {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 6px;
  padding: 8px 12px;
  border-radius: 6px;
  transition: all 0.3s ease;
  cursor: pointer;
}

.chart-type-option.active {
  background: #ffffff;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.chart-type-option svg {
  width: 16px;
  height: 16px;
  color: #64748b;
}

.chart-type-option.active svg {
  color: #2563eb;
}

.chart-type-option text {
  font-size: 12px;
  font-weight: 600;
  color: #64748b;
}

.chart-type-option.active text {
  color: #2563eb;
}

.legend-item {
  display: flex;
  align-items: center;
  gap: 8px;
}

.legend-dot {
  width: 12px;
  height: 12px;
  border-radius: 6px;
}

.legend-dot.health {
  background: linear-gradient(135deg, #F44336, #D32F2F);
}

.legend-dot.exposure {
  background: linear-gradient(135deg, #2196F3, #1976D2);
}

.legend-text {
  font-size: 12px;
  color: #374151;
  font-weight: 600;
}

/* 波形图例样式 */
.waveform-legend {
  display: flex;
  justify-content: center;
  gap: 24px;
  margin-top: 16px;
}

.legend-color {
  width: 12px;
  height: 12px;
  border-radius: 50%;
}

.legend-color.heart-rate {
  background: #ef4444;
}

.legend-color.blood-oxygen {
  background: #3b82f6;
}

.legend-color.steps {
  background: #10b981;
}

/* 现代化洞察卡片 */
.insights-card-modern {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  border-radius: 20px;
  padding: 24px;
  margin: 0 20px 24px;
  border: 1px solid rgba(255, 255, 255, 0.2);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.08);
  position: relative;
  overflow: hidden;
}

.insights-header-modern {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
}

.insights-title-section {
  display: flex;
  align-items: center;
  gap: 12px;
}

.insights-icon-modern {
  width: 32px;
  height: 32px;
  border-radius: 16px;
  background: rgba(33, 150, 243, 0.1);
  display: flex;
  align-items: center;
  justify-content: center;
}

.insights-icon-modern svg {
  width: 18px;
  height: 18px;
  color: #2196F3;
}

.insights-title-text {
  display: flex;
  flex-direction: column;
}

.insights-title {
  font-size: 18px;
  font-weight: 700;
  color: #0f172a;
  margin-bottom: 2px;
}

.insights-subtitle {
  font-size: 14px;
  color: #64748b;
  font-weight: 500;
}

.ai-badge-modern {
  display: flex;
  align-items: center;
  gap: 8px;
  background: rgba(33, 150, 243, 0.1);
  border-radius: 12px;
  padding: 8px 12px;
}

.ai-icon-container {
  width: 20px;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.ai-icon-container svg {
  width: 16px;
  height: 16px;
  color: #2196F3;
}

.ai-text {
  font-size: 12px;
  font-weight: 600;
  color: #2196F3;
}

.insight-item-modern {
  display: flex;
  align-items: center;
  gap: 16px;
  padding: 16px;
  border-radius: 16px;
  background: rgba(248, 250, 252, 0.8);
  margin-bottom: 12px;
  cursor: pointer;
  transition: all 0.3s ease;
  border: 1px solid rgba(0, 0, 0, 0.04);
}

.insight-item-modern:hover {
  background: rgba(33, 150, 243, 0.05);
  transform: translateY(-2px);
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.08);
}

.insight-item-modern:last-child {
  margin-bottom: 0;
}

.insight-icon-container {
  width: 40px;
  height: 40px;
  border-radius: 20px;
  background: rgba(33, 150, 243, 0.1);
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.insight-emoji {
  font-size: 20px;
}

.insight-content {
  flex: 1;
}

.insight-title-text {
  font-size: 16px;
  font-weight: 600;
  color: #0f172a;
  margin-bottom: 4px;
  line-height: 1.3;
}

.insight-description {
  font-size: 14px;
  color: #64748b;
  line-height: 1.4;
}

.insight-arrow {
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #94a3b8;
  transition: all 0.3s ease;
}

.insight-arrow svg {
  width: 16px;
  height: 16px;
}

.insight-item-modern:hover .insight-arrow {
  color: #2196F3;
  transform: translateX(2px);
}

/* 动画效果 */
@keyframes heartbeat {
  0%, 100% {
    height: 20%;
    opacity: 0.6;
  }
  50% {
    height: 100%;
    opacity: 1;
  }
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideInLeft {
  from {
    opacity: 0;
    transform: translateX(-30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes scaleIn {
  from {
    opacity: 0;
    transform: scale(0.9);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

/* 页面动画 */
.modern-header {
  animation: slideInLeft 0.8s ease-out;
}

.date-selector-container {
  animation: fadeInUp 0.8s ease-out 0.2s both;
}

.health-metrics-container {
  animation: fadeInUp 0.8s ease-out 0.4s both;
}

.chart-card-modern {
  animation: fadeInUp 0.8s ease-out 0.6s both;
}

.insights-card-modern {
  animation: fadeInUp 0.8s ease-out 0.8s both;
}

/* 响应式设计 */
@media (min-width: 768px) {
  .health-container {
    max-width: 800px;
    margin: 0 auto;
    padding: 0 32px 120px;
  }

  .modern-header {
    padding: 60px 0 20px;
  }

  .health-metrics-container {
    grid-template-columns: repeat(3, 1fr);
    margin: 0 0 32px;
  }

  .heart-rate-card {
    grid-row: span 2;
    grid-column: span 1;
  }

  .chart-card-modern,
  .insights-card-modern {
    margin: 0 0 32px;
  }

  .date-item {
    min-width: 80px;
  }
}

@media (max-width: 375px) {
  .modern-header {
    padding: 50px 16px 16px;
  }

  .date-selector-container {
    padding: 16px 0;
  }

  .health-metrics-container {
    margin: 0 16px 20px;
    gap: 12px;
  }

  .health-card {
    padding: 16px;
    min-height: 100px;
  }

  .card-value {
    font-size: 32px;
  }

  .chart-card-modern,
  .insights-card-modern {
    margin: 0 16px 20px;
    padding: 20px;
  }
}

.oxygen-progress-ring {
  transition: stroke-dasharray 1.5s cubic-bezier(0.4, 0, 0.2, 1);
}

.ring-center-text {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  text-align: center;
  z-index: 4;
}

.ring-percentage {
  font-size: 13px;
  font-weight: 800;
  color: #ffffff;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
  letter-spacing: -0.5px;
}



/* 趋势图表卡片 */
.chart-card {
  margin: 0 20px 24px;
  background: rgba(142, 142, 147, 0.12);
  border-radius: 20px;
  padding: 20px;
  backdrop-filter: blur(10px);
}

.chart-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 20px;
}

.chart-title-section {
  flex: 1;
}

.chart-title {
  font-size: 18px;
  font-weight: 600;
  color: #000000;
  line-height: 1.2;
  margin-bottom: 4px;
}

.chart-subtitle {
  font-size: 14px;
  color: #8E8E93;
  font-weight: 500;
}

.time-selector {
  display: flex;
  background: rgba(255, 255, 255, 0.8);
  border-radius: 8px;
  padding: 2px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.time-option {
  padding: 6px 12px;
  font-size: 12px;
  font-weight: 600;
  color: #8E8E93;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.time-option.active {
  background: #ffffff;
  color: #000000;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.chart-container {
  position: relative;
  margin-bottom: 16px;
  background: rgba(255, 255, 255, 0.9);
  border-radius: 12px;
  padding: 16px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.trend-chart {
  width: 100%;
  max-width: 100%;
  height: 280px;
  border-radius: 12px;
  background: #ffffff;
  border: 1px solid rgba(0, 0, 0, 0.08);
  display: block;
}

.chart-loading {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 12px;
}

.loading-spinner {
  width: 24px;
  height: 24px;
  border: 2px solid rgba(142, 142, 147, 0.3);
  border-top: 2px solid #00D4AA;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 健康趋势SVG图表样式 */
.health-trend-svg-container {
  width: 100%;
  height: 240px;
  position: relative;
  margin-top: 10px;
  background: rgba(248, 250, 252, 0.5);
  border-radius: 12px;
  padding: 10px;
}

.health-trend-svg {
  width: 100%;
  height: 100%;
  filter: drop-shadow(0 2px 8px rgba(0, 0, 0, 0.1));
}

.health-trend-svg .grid-lines {
  opacity: 0.15;
}

.health-trend-svg .chart-axes line {
  stroke: #64748b;
  stroke-width: 2;
}

.health-trend-svg .chart-axes polygon {
  fill: #64748b;
}

.health-trend-svg text {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  font-weight: 500;
  fill: #64748b;
}

.loading-text {
  font-size: 14px;
  color: #8E8E93;
  font-weight: 500;
}

.chart-legend {
  display: flex;
  justify-content: center;
  gap: 24px;
}

.legend-item {
  display: flex;
  align-items: center;
  gap: 8px;
}

.legend-dot {
  width: 8px;
  height: 8px;
  border-radius: 4px;
}

.legend-dot.health {
  background: #F44336;
}

.legend-dot.exposure {
  background: #2196F3;
}

.legend-text {
  font-size: 12px;
  color: #8E8E93;
  font-weight: 500;
}

/* 健康洞察卡片 */
.insights-card {
  margin: 0 20px 24px;
  background: rgba(142, 142, 147, 0.12);
  border-radius: 20px;
  padding: 20px;
  backdrop-filter: blur(10px);
}

.insights-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 20px;
}

.insights-title-section {
  flex: 1;
}

.insights-title {
  font-size: 18px;
  font-weight: 600;
  color: #000000;
  line-height: 1.2;
  margin-bottom: 4px;
}

.insights-subtitle {
  font-size: 14px;
  color: #8E8E93;
  font-weight: 500;
}

.ai-badge {
  background: linear-gradient(135deg, #8B5CF6 0%, #7C3AED 100%);
  border-radius: 12px;
  padding: 6px 12px;
  display: flex;
  align-items: center;
  gap: 6px;
  box-shadow: 0 4px 12px rgba(139, 92, 246, 0.3);
}

.ai-icon {
  font-size: 14px;
}

.ai-text {
  font-size: 12px;
  color: #ffffff;
  font-weight: 600;
}

.insights-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.insight-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 16px;
  background: rgba(255, 255, 255, 0.9);
  border-radius: 16px;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.insight-item:hover {
  background: rgba(255, 255, 255, 1);
  transform: translateX(4px);
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
}

.insight-icon-container {
  width: 40px;
  height: 40px;
  border-radius: 12px;
  background: linear-gradient(135deg, #F59E0B 0%, #D97706 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 4px 12px rgba(245, 158, 11, 0.3);
}

.insight-emoji {
  font-size: 18px;
}

.insight-content {
  flex: 1;
}

.insight-title-text {
  font-size: 14px;
  font-weight: 600;
  color: #000000;
  line-height: 1.3;
  margin-bottom: 4px;
}

.insight-description {
  font-size: 12px;
  color: #8E8E93;
  line-height: 1.4;
}

.insight-arrow {
  color: #8E8E93;
  font-size: 18px;
  font-weight: 600;
}

/* 响应式设计 */
@media (max-width: 375px) {
  .date-selector {
    gap: 8px;
    padding: 0 16px;
  }

  .date-item {
    min-width: 60px;
    padding: 12px 8px;
  }

  .health-metrics-container {
    grid-template-columns: 1fr;
    gap: 12px;
    margin: 0 16px 20px;
  }

  .heart-rate-card {
    grid-row: span 1;
  }

  .chart-card,
  .insights-card {
    margin: 0 16px 20px;
  }

  .header-section {
    padding: 50px 16px 12px;
  }

  .main-title {
    font-size: 28px;
  }

  .card-value {
    font-size: 28px;
  }
}

@media (min-width: 768px) {
  .health-container {
    max-width: 800px;
    margin: 0 auto;
    padding: 0 32px 120px;
  }

  .health-metrics-container {
    grid-template-columns: repeat(3, 1fr);
    margin: 0 0 32px;
  }

  .heart-rate-card {
    grid-row: span 2;
    grid-column: span 1;
  }

  .chart-card,
  .insights-card {
    margin: 0 0 32px;
  }

  .date-item {
    min-width: 80px;
  }
}

/* 动画效果 */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideInLeft {
  from {
    opacity: 0;
    transform: translateX(-30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes scaleIn {
  from {
    opacity: 0;
    transform: scale(0.9);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

.header-section {
  animation: slideInLeft 0.8s ease-out;
}

.date-scroll-container {
  animation: fadeInUp 0.8s ease-out 0.2s both;
}

.health-metrics-container {
  animation: scaleIn 0.8s ease-out 0.4s both;
}

.health-card:nth-child(1) {
  animation-delay: 0.5s;
}

.health-card:nth-child(2) {
  animation-delay: 0.6s;
}

.health-card:nth-child(3) {
  animation-delay: 0.7s;
}

.chart-card {
  animation: fadeInUp 0.8s ease-out 0.8s both;
}

.insights-card {
  animation: fadeInUp 0.8s ease-out 0.9s both;
}

/* 脉冲动画 */
@keyframes pulse {
  0%, 100% {
    transform: scale(1);
    opacity: 1;
  }
  50% {
    transform: scale(1.05);
    opacity: 0.8;
  }
}

.heart-rate-card .card-icon {
  animation: pulse 2s ease-in-out infinite;
}

/* 卡片特效动画 */
@keyframes heartGlow {
  0%, 100% {
    transform: rotate(0deg) scale(1);
    opacity: 0.1;
  }
  50% {
    transform: rotate(180deg) scale(1.1);
    opacity: 0.2;
  }
}

@keyframes stepsShimmer {
  0%, 100% {
    transform: translateX(-100%) rotate(45deg);
    opacity: 0.15;
  }
  50% {
    transform: translateX(100%) rotate(45deg);
    opacity: 0.25;
  }
}

@keyframes oxygenPulse {
  0%, 100% {
    transform: scale(1);
    opacity: 0.12;
  }
  50% {
    transform: scale(1.2);
    opacity: 0.2;
  }
}

/* 响应式设计 */
@media (max-width: 480px) {
  .health-metrics-container {
    gap: 16px;
    padding: 0 16px;
  }

  .health-card {
    min-height: 120px;
    padding: 20px;
  }

  .card-value {
    font-size: 44px;
  }

  .heart-wave-chart,
  .steps-bar-chart,
  .oxygen-ring-chart {
    width: 60px;
    height: 30px;
    bottom: 16px;
    right: 16px;
  }

  .oxygen-ring-chart {
    height: 60px;
  }
}

/* 关键帧动画 */
@keyframes fadeOut {
  to {
    opacity: 0;
    visibility: hidden;
  }
}

@keyframes pulse {
  0%, 100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.1);
  }
}

@keyframes loading {
  0% {
    transform: translateX(-100%);
  }
  100% {
    transform: translateX(100%);
  }
}

@keyframes float {
  0%, 100% {
    transform: translateY(0px) rotate(0deg);
    opacity: 0.3;
  }
  25% {
    transform: translateY(-10px) rotate(90deg);
    opacity: 0.6;
  }
  50% {
    transform: translateY(-20px) rotate(180deg);
    opacity: 0.9;
  }
  75% {
    transform: translateY(-10px) rotate(270deg);
    opacity: 0.6;
  }
}

@keyframes ripple {
  0% {
    width: 0;
    height: 0;
    opacity: 1;
  }
  100% {
    width: 100px;
    height: 100px;
    opacity: 0;
  }
}

@keyframes heartbeat {
  0%, 100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
}

@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes bounceIn {
  0% {
    opacity: 0;
    transform: scale(0.3);
  }
  50% {
    opacity: 1;
    transform: scale(1.05);
  }
  70% {
    transform: scale(0.9);
  }
  100% {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes heartRatePulse {
  0%, 100% {
    stroke-width: 2;
    opacity: 1;
  }
  50% {
    stroke-width: 3;
    opacity: 0.8;
  }
}

@keyframes dataPointGlow {
  0%, 100% {
    filter: drop-shadow(0 0 2px rgba(255, 255, 255, 0.8));
  }
  50% {
    filter: drop-shadow(0 0 8px rgba(255, 255, 255, 1));
  }
}

@keyframes stepsBarDance {
  0%, 100% {
    transform: scaleY(1);
  }
  25% {
    transform: scaleY(1.1);
  }
  50% {
    transform: scaleY(0.9);
  }
  75% {
    transform: scaleY(1.05);
  }
}

@keyframes indicatorSpin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

@keyframes shimmer {
  0% {
    background-position: -200px 0;
  }
  100% {
    background-position: calc(200px + 100%) 0;
  }
}

@keyframes fadeInScale {
  0% {
    opacity: 0;
    transform: scale(0.8);
  }
  100% {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes wiggle {
  0%, 7% {
    transform: rotateZ(0);
  }
  15% {
    transform: rotateZ(-15deg);
  }
  20% {
    transform: rotateZ(10deg);
  }
  25% {
    transform: rotateZ(-10deg);
  }
  30% {
    transform: rotateZ(6deg);
  }
  35% {
    transform: rotateZ(-4deg);
  }
  40%, 100% {
    transform: rotateZ(0);
  }
}

/* 增强的交互效果 */
.interactive-card {
  position: relative;
  overflow: hidden;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.interactive-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
  transition: left 0.5s ease;
  pointer-events: none;
}

.interactive-card:hover::before {
  left: 100%;
}

.interactive-card:hover {
  transform: translateY(-12px) scale(1.03);
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.2);
}

.interactive-card.card-active {
  transform: translateY(-6px) scale(0.97);
  transition: all 0.1s ease;
}

/* 心率卡片特殊动画 */
.heart-rate-card:hover .heart-rate-line {
  animation: heartRatePulse 1s ease-in-out infinite;
}

.heart-rate-card:hover .data-point {
  animation: dataPointGlow 0.8s ease-in-out infinite;
}

/* 步数卡片特殊动画 */
.steps-card:hover .steps-bar {
  animation: stepsBarDance 1.5s ease-in-out infinite;
}

.steps-card:hover .current-indicator {
  animation: indicatorSpin 2s linear infinite;
}

/* 卡片内容动画 */
.card-value {
  transition: all 0.3s ease;
}

.interactive-card:hover .card-value {
  transform: scale(1.1);
  text-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
}

.mini-chart {
  transition: all 0.3s ease;
}

.interactive-card:hover .mini-chart {
  transform: scale(1.05);
}

.avatar-circle {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
}

.avatar-circle::before {
  content: '';
  position: absolute;
  top: -2px;
  left: -2px;
  right: -2px;
  bottom: -2px;
  background: linear-gradient(45deg, transparent, rgba(255, 255, 255, 0.3), transparent);
  border-radius: 50%;
  opacity: 0;
  transition: opacity 0.3s ease;
  pointer-events: none;
}

.avatar-circle:hover {
  transform: scale(1.15) rotate(10deg);
  box-shadow: 0 12px 35px rgba(255, 255, 255, 0.4);
  animation: wiggle 2s ease-in-out infinite;
}

.avatar-circle:hover::before {
  opacity: 1;
  animation: shimmer 1.5s ease-in-out infinite;
}

.avatar-circle:active {
  transform: scale(1.05) rotate(5deg);
  transition: all 0.1s ease;
}

.date-item {
  position: relative;
  overflow: hidden;
}

.date-item::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.1), rgba(255, 255, 255, 0.3));
  opacity: 0;
  transition: opacity 0.3s ease;
  border-radius: 16px;
  pointer-events: none;
}

.date-item:hover {
  transform: translateY(-6px) scale(1.08);
  box-shadow: 0 12px 35px rgba(0, 0, 0, 0.15);
  animation: fadeInScale 0.3s ease;
}

.date-item:hover::before {
  opacity: 1;
}

.date-item.active {
  animation: heartbeat 2s ease-in-out infinite;
  background: linear-gradient(135deg, rgba(37, 99, 235, 0.9), rgba(29, 78, 216, 0.9));
  color: white;
  box-shadow: 0 8px 25px rgba(37, 99, 235, 0.3);
}

.date-item.active .ripple-effect {
  animation: ripple 2s ease-out infinite;
}

.date-item:active {
  transform: translateY(-2px) scale(1.02);
  transition: all 0.1s ease;
}

/* 深色模式支持 */
@media (prefers-color-scheme: dark) {
  .health-container {
    background: #000000;
  }

  .main-title,
  .activity-title,
  .chart-title,
  .insights-title,
  .insight-title-text {
    color: #ffffff;
  }

  .date-text,
  .activity-time,
  .chart-subtitle,
  .insights-subtitle,
  .metric-label,
  .insight-description,
  .legend-text {
    color: #8E8E93;
  }

  .activity-card,
  .chart-card,
  .insights-card {
    background: rgba(255, 255, 255, 0.05);
  }

  .metric-item,
  .health-metric-item,
  .insight-item,
  .chart-container {
    background: rgba(255, 255, 255, 0.1);
  }
}
</style>
