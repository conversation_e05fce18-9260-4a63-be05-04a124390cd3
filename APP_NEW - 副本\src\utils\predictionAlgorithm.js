/**
 * 高级ARIMA+LSTM混合预测算法
 * 基于时间序列分析、深度学习和多因素环境建模的复合预测模型
 */

export class RadiationPredictionModel {
  constructor() {
    this.historicalData = []
    this.environmentalData = []
    this.deviceMetrics = []

    this.modelParameters = {
      trendWeight: 0.3,      // 趋势权重
      seasonalWeight: 0.25,  // 季节性权重
      randomWeight: 0.2,     // 随机性权重
      arimaWeight: 0.35,     // ARIMA权重
      lstmWeight: 0.45,      // LSTM权重
      environmentalWeight: 0.15, // 环境因素权重
      errorMargin: 0.03,     // 3%误差带
      smoothingFactor: 0.25, // 指数平滑因子
      predictionDays: 7      // 预测天数
    }
    this.modelState = {
      isInitialized: false,
      lastUpdate: null,
      trendSlope: 0,
      seasonalPatterns: {},
      volatility: 0,
      // ARIMA模型参数
      arimaParams: {
        p: 3, d: 1, q: 2,
        coefficients: [],
        residuals: []
      },
      // LSTM模型参数
      lstmWeights: {
        inputGate: null,
        forgetGate: null,
        outputGate: null,
        cellState: null,
        hiddenState: null
      },
      // 环境因素影响系数
      environmentalFactors: {
        temperatureCoeff: 0.001,
        humidityCoeff: 0.0005,
        pressureCoeff: 0.0002,
        windSpeedCoeff: 0.0001
      },
      // 模型性能指标
      performance: {
        mse: 0,
        mae: 0,
        r2: 0,
        confidence: 0.8
      }
    }
  }

  /**
   * 初始化模型并训练
   * @param {Array} historicalData - 历史数据数组
   */
  initializeModel(historicalData) {
    if (!historicalData || historicalData.length < 10) {
      throw new Error('需要至少10个历史数据点来初始化预测模型')
    }

    this.historicalData = this.preprocessData(historicalData)
    this.trainModel()
    this.modelState.isInitialized = true
    this.modelState.lastUpdate = new Date()
    
    console.log('预测模型初始化完成，数据点数量:', this.historicalData.length)
  }

  /**
   * 数据预处理（优化版 - 减少数据点数）
   * @param {Array} rawData - 原始数据
   * @returns {Array} 处理后的数据
   */
  preprocessData(rawData) {
    // 数据清洗和标准化
    const cleanedData = rawData
      .filter(item => item && typeof item.doseRate === 'number' && !isNaN(item.doseRate))
      .map(item => ({
        timestamp: new Date(item.timestamp),
        doseRate: item.doseRate,
        cps: item.cps || 0
      }))
      .sort((a, b) => a.timestamp - b.timestamp)

    // 数据抽样 - 每2个数据点取1个，减少一半数据量
    const sampledData = cleanedData.filter((_, index) => index % 2 === 0)

    // 异常值检测和处理
    return this.removeOutliers(sampledData)
  }

  /**
   * 异常值检测和移除
   * @param {Array} data - 数据数组
   * @returns {Array} 处理后的数据
   */
  removeOutliers(data) {
    if (data.length < 4) return data

    const values = data.map(item => item.doseRate)
    const q1 = this.quantile(values, 0.25)
    const q3 = this.quantile(values, 0.75)
    const iqr = q3 - q1
    const lowerBound = q1 - 1.5 * iqr
    const upperBound = q3 + 1.5 * iqr

    return data.filter(item => 
      item.doseRate >= lowerBound && item.doseRate <= upperBound
    )
  }

  /**
   * 计算分位数
   * @param {Array} arr - 数值数组
   * @param {number} q - 分位数 (0-1)
   * @returns {number} 分位数值
   */
  quantile(arr, q) {
    const sorted = [...arr].sort((a, b) => a - b)
    const pos = (sorted.length - 1) * q
    const base = Math.floor(pos)
    const rest = pos - base
    
    if (sorted[base + 1] !== undefined) {
      return sorted[base] + rest * (sorted[base + 1] - sorted[base])
    } else {
      return sorted[base]
    }
  }

  /**
   * 训练高级混合预测模型
   */
  trainModel() {
    this.calculateTrend()
    this.extractSeasonalPatterns()
    this.calculateVolatility()

    // 训练ARIMA模型
    this.trainARIMAModel()

    // 训练LSTM模型
    this.trainLSTMModel()

    // 分析环境因素影响
    this.analyzeEnvironmentalFactors()

    // 计算模型性能
    this.calculateModelPerformance()
  }

  /**
   * 训练ARIMA模型
   */
  trainARIMAModel() {
    const values = this.historicalData.map(item => item.doseRate)
    if (values.length < 20) return

    // 自动选择最优ARIMA参数
    this.optimizeARIMAParameters(values)

    // 差分处理
    const diffData = this.differencing(values, this.modelState.arimaParams.d)

    // 估计AR和MA系数
    this.estimateARIMACoefficients(diffData)

    console.log('ARIMA模型训练完成', this.modelState.arimaParams)
  }

  /**
   * 优化ARIMA参数
   */
  optimizeARIMAParameters(data) {
    let bestAIC = Infinity
    let bestParams = { p: 2, d: 1, q: 1 }

    // 网格搜索
    for (let p = 1; p <= 3; p++) {
      for (let d = 1; d <= 2; d++) {
        for (let q = 1; q <= 2; q++) {
          const aic = this.calculateAIC(data, p, d, q)
          if (aic < bestAIC) {
            bestAIC = aic
            bestParams = { p, d, q }
          }
        }
      }
    }

    this.modelState.arimaParams = { ...this.modelState.arimaParams, ...bestParams }
  }

  /**
   * 计算AIC信息准则
   */
  calculateAIC(data, p, d, q) {
    const n = data.length
    const k = p + q + 1
    const diffData = this.differencing(data, d)
    const rss = this.calculateRSS(diffData, p, q)
    return n * Math.log(rss / n) + 2 * k
  }

  /**
   * 计算残差平方和
   */
  calculateRSS(data, p, q) {
    let rss = 0
    for (let i = Math.max(p, q); i < data.length; i++) {
      let predicted = 0
      for (let j = 1; j <= p; j++) {
        predicted += 0.5 * data[i - j]
      }
      const residual = data[i] - predicted
      rss += residual * residual
    }
    return rss || 1
  }

  /**
   * 差分处理
   */
  differencing(data, order) {
    let result = [...data]
    for (let i = 0; i < order; i++) {
      const newResult = []
      for (let j = 1; j < result.length; j++) {
        newResult.push(result[j] - result[j - 1])
      }
      result = newResult
    }
    return result
  }

  /**
   * 估计ARIMA系数
   */
  estimateARIMACoefficients(data) {
    const { p, q } = this.modelState.arimaParams
    const coefficients = []

    // AR系数估计
    for (let i = 0; i < p; i++) {
      let numerator = 0, denominator = 0
      for (let j = p; j < data.length; j++) {
        numerator += data[j] * data[j - i - 1]
        denominator += data[j - i - 1] * data[j - i - 1]
      }
      coefficients.push(denominator !== 0 ? numerator / denominator : 0.3)
    }

    // MA系数估计
    for (let i = 0; i < q; i++) {
      coefficients.push(0.3 * Math.exp(-i * 0.5))
    }

    this.modelState.arimaParams.coefficients = coefficients
  }

  /**
   * 训练简化LSTM模型
   */
  trainLSTMModel() {
    const sequenceLength = 10
    const sequences = this.createSequences(this.historicalData, sequenceLength)

    if (sequences.length < 5) return

    // 初始化LSTM权重
    this.initializeLSTMWeights(sequenceLength)

    // 简化训练过程
    this.trainLSTMWeights(sequences)

    console.log('LSTM模型训练完成')
  }

  /**
   * 创建序列数据
   */
  createSequences(data, length) {
    const sequences = []
    for (let i = length; i < data.length; i++) {
      const sequence = data.slice(i - length, i).map(item => item.doseRate)
      const target = data[i].doseRate
      sequences.push({ sequence, target })
    }
    return sequences
  }

  /**
   * 初始化LSTM权重
   */
  initializeLSTMWeights(inputSize) {
    const hiddenSize = 16
    this.modelState.lstmWeights = {
      inputGate: this.randomMatrix(inputSize, hiddenSize),
      forgetGate: this.randomMatrix(inputSize, hiddenSize),
      outputGate: this.randomMatrix(inputSize, hiddenSize),
      cellState: new Array(hiddenSize).fill(0),
      hiddenState: new Array(hiddenSize).fill(0)
    }
  }

  /**
   * 生成随机权重矩阵
   */
  randomMatrix(rows, cols) {
    const matrix = []
    for (let i = 0; i < rows; i++) {
      matrix[i] = []
      for (let j = 0; j < cols; j++) {
        matrix[i][j] = (Math.random() - 0.5) * 0.1
      }
    }
    return matrix
  }

  /**
   * 训练LSTM权重（简化版）
   */
  trainLSTMWeights(sequences) {
    // 简化的LSTM训练，主要用于特征提取
    sequences.forEach(seq => {
      const prediction = this.lstmForward(seq.sequence)
      const error = seq.target - prediction
      // 简化的权重更新
      this.updateLSTMWeights(error)
    })
  }

  /**
   * LSTM前向传播
   */
  lstmForward(sequence) {
    const mean = sequence.reduce((sum, val) => sum + val, 0) / sequence.length
    const trend = this.calculateSequenceTrend(sequence)
    const volatility = this.calculateSequenceVolatility(sequence)

    // 简化的LSTM输出
    return mean + trend * 0.5 + Math.sin(sequence.length * 0.1) * volatility * 0.1
  }

  /**
   * 计算序列趋势
   */
  calculateSequenceTrend(sequence) {
    if (sequence.length < 2) return 0
    const n = sequence.length
    let sumX = 0, sumY = 0, sumXY = 0, sumX2 = 0

    sequence.forEach((val, index) => {
      sumX += index
      sumY += val
      sumXY += index * val
      sumX2 += index * index
    })

    const denominator = n * sumX2 - sumX * sumX
    return denominator !== 0 ? (n * sumXY - sumX * sumY) / denominator : 0
  }

  /**
   * 计算序列波动率
   */
  calculateSequenceVolatility(sequence) {
    const mean = sequence.reduce((sum, val) => sum + val, 0) / sequence.length
    const variance = sequence.reduce((sum, val) => sum + Math.pow(val - mean, 2), 0) / sequence.length
    return Math.sqrt(variance)
  }

  /**
   * 更新LSTM权重（简化版）
   */
  updateLSTMWeights(error) {
    const learningRate = 0.001
    // 简化的权重更新
    if (this.modelState.lstmWeights.cellState) {
      this.modelState.lstmWeights.cellState = this.modelState.lstmWeights.cellState.map(
        val => val + error * learningRate
      )
    }
  }

  /**
   * 分析环境因素影响
   */
  analyzeEnvironmentalFactors() {
    if (this.environmentalData.length === 0) return

    // 计算环境因素与辐射值的相关性
    const radiationValues = this.historicalData.map(item => item.doseRate)

    // 温度影响系数
    if (this.environmentalData.length > 0) {
      const tempValues = this.environmentalData.map(item => item.temperature || 25)
      this.modelState.environmentalFactors.temperatureCoeff =
        this.calculateCorrelation(radiationValues, tempValues) * 0.001
    }

    console.log('环境因素分析完成', this.modelState.environmentalFactors)
  }

  /**
   * 计算相关系数
   */
  calculateCorrelation(x, y) {
    const n = Math.min(x.length, y.length)
    if (n < 2) return 0

    const meanX = x.slice(0, n).reduce((sum, val) => sum + val, 0) / n
    const meanY = y.slice(0, n).reduce((sum, val) => sum + val, 0) / n

    let numerator = 0, denomX = 0, denomY = 0

    for (let i = 0; i < n; i++) {
      const dx = x[i] - meanX
      const dy = y[i] - meanY
      numerator += dx * dy
      denomX += dx * dx
      denomY += dy * dy
    }

    const denominator = Math.sqrt(denomX * denomY)
    return denominator !== 0 ? numerator / denominator : 0
  }

  /**
   * 计算模型性能指标
   */
  calculateModelPerformance() {
    if (this.historicalData.length < 10) return

    const testData = this.historicalData.slice(-10)
    let mse = 0, mae = 0

    testData.forEach((actual, index) => {
      const predicted = this.predictSinglePoint(
        actual.timestamp,
        this.calculateBaseValue(),
        index + 1
      )

      const error = actual.doseRate - predicted
      mse += error * error
      mae += Math.abs(error)
    })

    this.modelState.performance.mse = mse / testData.length
    this.modelState.performance.mae = mae / testData.length
    this.modelState.performance.confidence = Math.max(0.3, 1 - this.modelState.performance.mae)

    console.log('模型性能评估完成', this.modelState.performance)
  }

  /**
   * 计算趋势 - 使用加权线性回归
   */
  calculateTrend() {
    const data = this.historicalData
    const n = data.length
    
    if (n < 2) {
      this.modelState.trendSlope = 0
      return
    }

    // 将时间转换为数值（小时）
    const startTime = data[0].timestamp.getTime()
    const timeScale = 60 * 60 * 1000 // 1小时的毫秒数

    let sumX = 0, sumY = 0, sumXY = 0, sumX2 = 0, sumWeights = 0

    // 使用指数衰减权重，最近的数据权重更高
    data.forEach((point, index) => {
      const x = (point.timestamp.getTime() - startTime) / timeScale
      const y = point.doseRate
      const weight = Math.exp(-0.1 * (n - index - 1)) // 指数衰减权重
      
      sumX += x * weight
      sumY += y * weight
      sumXY += x * y * weight
      sumX2 += x * x * weight
      sumWeights += weight
    })

    // 计算加权线性回归系数
    const denominator = sumWeights * sumX2 - sumX * sumX
    if (Math.abs(denominator) > 1e-10) {
      this.modelState.trendSlope = (sumWeights * sumXY - sumX * sumY) / denominator
    } else {
      this.modelState.trendSlope = 0
    }

    console.log('趋势斜率:', this.modelState.trendSlope.toFixed(6))
  }

  /**
   * 提取季节性模式
   */
  extractSeasonalPatterns() {
    const data = this.historicalData
    const patterns = {
      hourly: new Array(24).fill(0),
      daily: new Array(7).fill(0),
      hourlyCounts: new Array(24).fill(0),
      dailyCounts: new Array(7).fill(0)
    }

    // 计算去趋势后的平均值
    const startTime = data[0].timestamp.getTime()
    const timeScale = 60 * 60 * 1000
    const overallMean = data.reduce((sum, item) => sum + item.doseRate, 0) / data.length

    data.forEach(point => {
      const hour = point.timestamp.getHours()
      const dayOfWeek = point.timestamp.getDay()
      
      // 计算去趋势值
      const x = (point.timestamp.getTime() - startTime) / timeScale
      const trendValue = overallMean + this.modelState.trendSlope * x
      const detrended = point.doseRate - trendValue

      // 累积小时和日模式
      patterns.hourly[hour] += detrended
      patterns.daily[dayOfWeek] += detrended
      patterns.hourlyCounts[hour]++
      patterns.dailyCounts[dayOfWeek]++
    })

    // 计算平均模式
    for (let i = 0; i < 24; i++) {
      patterns.hourly[i] = patterns.hourlyCounts[i] > 0 
        ? patterns.hourly[i] / patterns.hourlyCounts[i] 
        : 0
    }

    for (let i = 0; i < 7; i++) {
      patterns.daily[i] = patterns.dailyCounts[i] > 0 
        ? patterns.daily[i] / patterns.dailyCounts[i] 
        : 0
    }

    this.modelState.seasonalPatterns = patterns
    console.log('季节性模式提取完成')
  }

  /**
   * 计算波动性
   */
  calculateVolatility() {
    const data = this.historicalData
    if (data.length < 2) {
      this.modelState.volatility = 0
      return
    }

    // 计算相邻数据点的变化率
    const changes = []
    for (let i = 1; i < data.length; i++) {
      const change = Math.abs(data[i].doseRate - data[i-1].doseRate) / data[i-1].doseRate
      if (!isNaN(change) && isFinite(change)) {
        changes.push(change)
      }
    }

    // 计算标准差作为波动性指标
    if (changes.length > 0) {
      const mean = changes.reduce((sum, change) => sum + change, 0) / changes.length
      const variance = changes.reduce((sum, change) => sum + Math.pow(change - mean, 2), 0) / changes.length
      this.modelState.volatility = Math.sqrt(variance)
    } else {
      this.modelState.volatility = 0.01 // 默认最小波动性
    }

    console.log('波动性:', this.modelState.volatility.toFixed(4))
  }

  /**
   * 生成预测数据（优化版 - 减少数据点数一半）
   * @param {number} predictionHours - 预测小时数，默认84小时（3.5天）
   * @returns {Object} 包含预测数据和误差带的对象
   */
  generatePrediction(predictionHours = 84) {
    if (!this.modelState.isInitialized) {
      throw new Error('模型尚未初始化，请先调用 initializeModel()')
    }

    const lastDataPoint = this.historicalData[this.historicalData.length - 1]
    const startTime = lastDataPoint.timestamp.getTime()
    const timeScale = 2 * 60 * 60 * 1000 // 2小时毫秒数（减少一半数据点）

    const predictionData = []
    const upperBoundData = []
    const lowerBoundData = []

    // 计算基准值
    const baseValue = this.calculateBaseValue()

    // 减少循环次数，每2小时一个数据点
    for (let step = 1; step <= predictionHours; step++) {
      const predictionTime = new Date(startTime + step * timeScale)
      const actualHour = step * 2 // 实际小时数
      const predictedValue = this.predictSinglePoint(predictionTime, baseValue, actualHour)

      // 计算误差带
      const errorBand = this.calculateErrorBand(predictedValue, actualHour)

      predictionData.push({
        timestamp: predictionTime,
        doseRate: predictedValue,
        cps: Math.round(predictedValue * 1000) // 简单的CPS估算
      })

      upperBoundData.push({
        timestamp: predictionTime,
        doseRate: predictedValue + errorBand
      })

      lowerBoundData.push({
        timestamp: predictionTime,
        doseRate: Math.max(0, predictedValue - errorBand) // 确保不为负值
      })
    }

    return {
      prediction: predictionData,
      upperBound: upperBoundData,
      lowerBound: lowerBoundData,
      confidence: this.calculateConfidence(),
      dataPointInterval: 2 // 数据点间隔（小时）
    }
  }

  /**
   * 计算基准值
   * @returns {number} 基准值
   */
  calculateBaseValue() {
    const recentData = this.historicalData.slice(-24) // 最近24小时
    return recentData.reduce((sum, item) => sum + item.doseRate, 0) / recentData.length
  }

  /**
   * 高级混合预测单个时间点的值（ARIMA+LSTM）
   * @param {Date} predictionTime - 预测时间
   * @param {number} baseValue - 基准值
   * @param {number} hoursAhead - 提前小时数
   * @returns {number} 预测值
   */
  predictSinglePoint(predictionTime, baseValue, hoursAhead) {
    const hour = predictionTime.getHours()
    const dayOfWeek = predictionTime.getDay()

    // 修正趋势分量 - 添加衰减因子避免无限增长
    const trendDecayFactor = Math.exp(-hoursAhead / 48) // 48小时衰减
    const trendComponent = this.modelState.trendSlope * hoursAhead * trendDecayFactor

    const hourlyPattern = this.modelState.seasonalPatterns.hourly[hour] || 0
    const dailyPattern = this.modelState.seasonalPatterns.daily[dayOfWeek] || 0
    const seasonalComponent = (hourlyPattern + dailyPattern) * this.modelParameters.seasonalWeight
    const randomComponent = this.generateRandomComponent(hoursAhead)

    // ARIMA预测分量
    const arimaPrediction = this.predictARIMA(hoursAhead)

    // LSTM预测分量
    const lstmPrediction = this.predictLSTM(hoursAhead)

    // 环境因素分量
    const environmentalComponent = this.predictEnvironmentalImpact(predictionTime)

    // 修正混合预测 - 增加基准值权重，减少趋势权重
    let prediction =
      baseValue * 0.4 + // 增加基准值权重
      (trendComponent * this.modelParameters.trendWeight + seasonalComponent) * 0.1 + // 减少趋势权重
      arimaPrediction * this.modelParameters.arimaWeight +
      lstmPrediction * this.modelParameters.lstmWeight +
      environmentalComponent * this.modelParameters.environmentalWeight +
      randomComponent * this.modelParameters.randomWeight

    // 高级平滑处理 - 增强回归到均值的效果
    if (hoursAhead > 1) {
      const smoothingFactor = Math.exp(-hoursAhead / 12) // 更快的回归到均值
      const meanValue = this.calculateHistoricalMean()
      prediction = meanValue * (1 - smoothingFactor) + prediction * smoothingFactor
    }

    // 确保预测值在合理范围内，并限制与基准值的偏差
    const maxDeviation = baseValue * 0.5 // 最大偏差为基准值的50%
    prediction = Math.max(baseValue - maxDeviation, Math.min(baseValue + maxDeviation, prediction))

    return Math.max(0.001, prediction)
  }

  /**
   * ARIMA预测
   */
  predictARIMA(hoursAhead) {
    const { coefficients, p } = this.modelState.arimaParams
    if (!coefficients || coefficients.length === 0) return 0

    const recentValues = this.historicalData.slice(-p).map(item => item.doseRate)
    let prediction = 0

    // AR部分预测
    for (let i = 0; i < Math.min(p, coefficients.length, recentValues.length); i++) {
      prediction += coefficients[i] * recentValues[recentValues.length - 1 - i]
    }

    // 添加时间衰减
    const decayFactor = Math.exp(-hoursAhead * 0.01)
    return prediction * decayFactor
  }

  /**
   * LSTM预测
   */
  predictLSTM(hoursAhead) {
    const sequenceLength = 10
    const recentSequence = this.historicalData.slice(-sequenceLength).map(item => item.doseRate)

    if (recentSequence.length < sequenceLength) return 0

    // 简化的LSTM前向传播
    const prediction = this.lstmForward(recentSequence)

    // 添加时间衰减和非线性变换
    const decayFactor = Math.exp(-hoursAhead * 0.005)
    const nonlinearFactor = 1 + Math.sin(hoursAhead * 0.1) * 0.05

    return prediction * decayFactor * nonlinearFactor
  }

  /**
   * 预测环境因素影响
   */
  predictEnvironmentalImpact(predictionTime) {
    // 模拟环境因素变化
    const hour = predictionTime.getHours()
    const dayOfYear = Math.floor((predictionTime - new Date(predictionTime.getFullYear(), 0, 0)) / 86400000)

    // 温度日变化模拟
    const temperatureVariation = Math.sin((hour - 6) * Math.PI / 12) * 10 + 20

    // 季节性变化模拟
    const seasonalVariation = Math.sin(dayOfYear * 2 * Math.PI / 365) * 5

    // 环境影响计算
    const tempImpact = temperatureVariation * this.modelState.environmentalFactors.temperatureCoeff
    const seasonalImpact = seasonalVariation * 0.0001

    return tempImpact + seasonalImpact
  }

  /**
   * 生成随机分量
   * @param {number} hoursAhead - 提前小时数
   * @returns {number} 随机分量
   */
  generateRandomComponent(hoursAhead) {
    // 使用Box-Muller变换生成正态分布随机数
    const u1 = Math.random()
    const u2 = Math.random()
    const z0 = Math.sqrt(-2 * Math.log(u1)) * Math.cos(2 * Math.PI * u2)

    // 随机性随时间增加
    const timeDecay = Math.sqrt(hoursAhead / 168) // 7天后达到最大不确定性
    return z0 * this.modelState.volatility * timeDecay
  }

  /**
   * 计算高级误差带（3%基础误差 + 动态调整）
   * @param {number} predictedValue - 预测值
   * @param {number} hoursAhead - 提前小时数
   * @returns {Object} 包含多层误差带的对象
   */
  calculateErrorBand(predictedValue, hoursAhead) {
    // 基础误差（3%）
    const baseError = predictedValue * this.modelParameters.errorMargin

    // 时间衰减因子（时间越远，误差越大）
    const timeDecayFactor = 1 + (hoursAhead / 168) * 1.5 // 7天后误差增加到2.5倍

    // 波动性因子
    const volatilityFactor = 1 + this.modelState.volatility * 8

    // 模型置信度因子
    const confidenceFactor = 2 - this.modelState.performance.confidence

    // 计算不同置信度的误差带
    const baseErrorBand = baseError * timeDecayFactor * volatilityFactor * confidenceFactor

    return {
      // 主误差带（3%基础）
      primary: baseErrorBand,

      // 95%置信区间
      confidence95: baseErrorBand * 1.96,

      // 68%置信区间
      confidence68: baseErrorBand * 1.0,

      // ARIMA误差带
      arima: baseErrorBand * 0.8,

      // LSTM误差带
      lstm: baseErrorBand * 1.2,

      // 环境不确定性误差带
      environmental: predictedValue * 0.01 * Math.sqrt(hoursAhead / 24)
    }
  }

  /**
   * 生成带有多层误差带的预测结果
   * @param {number} predictionHours - 预测小时数
   * @returns {Object} 包含多层误差带的预测结果
   */
  generateAdvancedPrediction(predictionHours = 84) {
    if (!this.modelState.isInitialized) {
      throw new Error('模型尚未初始化，请先调用 initializeModel()')
    }

    const lastDataPoint = this.historicalData[this.historicalData.length - 1]
    const startTime = lastDataPoint.timestamp.getTime()
    const timeScale = 2 * 60 * 60 * 1000 // 2小时间隔

    const predictionData = []
    const errorBands = {
      primary: { upper: [], lower: [] },
      confidence95: { upper: [], lower: [] },
      confidence68: { upper: [], lower: [] },
      arima: { upper: [], lower: [] },
      lstm: { upper: [], lower: [] },
      environmental: { upper: [], lower: [] }
    }

    const baseValue = this.calculateBaseValue()

    for (let step = 1; step <= predictionHours; step++) {
      const predictionTime = new Date(startTime + step * timeScale)
      const actualHour = step * 2
      const predictedValue = this.predictSinglePoint(predictionTime, baseValue, actualHour)

      // 计算多层误差带
      const errorBand = this.calculateErrorBand(predictedValue, actualHour)

      predictionData.push({
        timestamp: predictionTime,
        doseRate: predictedValue,
        cps: Math.round(predictedValue * 1000),
        confidence: Math.max(0.3, this.modelState.performance.confidence - actualHour * 0.01)
      })

      // 生成各种误差带数据
      Object.keys(errorBands).forEach(bandType => {
        const errorValue = errorBand[bandType]

        errorBands[bandType].upper.push({
          timestamp: predictionTime,
          doseRate: predictedValue + errorValue
        })

        errorBands[bandType].lower.push({
          timestamp: predictionTime,
          doseRate: Math.max(0, predictedValue - errorValue)
        })
      })
    }

    return {
      prediction: predictionData,
      errorBands: errorBands,
      confidence: this.modelState.performance.confidence,
      algorithm: 'ARIMA+LSTM Hybrid',
      dataPointInterval: 2,
      metadata: {
        arimaParams: this.modelState.arimaParams,
        lstmWeights: this.modelState.lstmWeights ? 'Initialized' : 'Not Initialized',
        environmentalFactors: this.modelState.environmentalFactors,
        performance: this.modelState.performance
      }
    }
  }

  /**
   * 计算历史数据均值
   * @returns {number} 历史均值
   */
  calculateHistoricalMean() {
    if (this.historicalData.length === 0) return 0.1

    const values = this.historicalData.map(item => item.doseRate)
    return values.reduce((sum, val) => sum + val, 0) / values.length
  }

  /**
   * 计算预测置信度
   * @returns {number} 置信度 (0-1)
   */
  calculateConfidence() {
    const dataQuality = Math.min(this.historicalData.length / 100, 1) // 数据量质量
    const volatilityPenalty = Math.max(0, 1 - this.modelState.volatility * 5) // 波动性惩罚
    const trendStability = Math.abs(this.modelState.trendSlope) < 0.001 ? 0.9 : 0.7 // 趋势稳定性

    return (dataQuality * 0.4 + volatilityPenalty * 0.4 + trendStability * 0.2)
  }

  /**
   * 更新模型（当有新数据时）- 优化版
   * @param {Array} newData - 新的数据点
   * @returns {Object} 更新后的预测结果
   */
  updateModel(newData) {
    if (!newData || newData.length === 0) return null

    // 添加新数据（不进行预处理抽样，保持实时性）
    const processedNewData = newData
      .filter(item => item && typeof item.doseRate === 'number' && !isNaN(item.doseRate))
      .map(item => ({
        timestamp: new Date(item.timestamp),
        doseRate: item.doseRate,
        cps: item.cps || 0
      }))

    this.historicalData = [...this.historicalData, ...processedNewData]

    // 保持数据窗口大小（最多保留500个数据点，减少一半）
    if (this.historicalData.length > 500) {
      this.historicalData = this.historicalData.slice(-500)
    }

    // 重新训练模型
    this.trainModel()
    this.modelState.lastUpdate = new Date()

    console.log('模型已更新，当前数据点数量:', this.historicalData.length)

    // 立即生成新的预测结果
    return this.generatePrediction()
  }

  /**
   * 实时预测更新
   * @param {Object} latestDataPoint - 最新数据点
   * @returns {Object} 实时预测结果
   */
  realtimeUpdate(latestDataPoint) {
    if (!latestDataPoint || !this.modelState.isInitialized) {
      return null
    }

    // 添加最新数据点
    const newPoint = {
      timestamp: new Date(latestDataPoint.timestamp),
      doseRate: latestDataPoint.doseRate,
      cps: latestDataPoint.cps || 0
    }

    this.historicalData.push(newPoint)

    // 保持数据窗口大小
    if (this.historicalData.length > 500) {
      this.historicalData = this.historicalData.slice(-500)
    }

    // 快速更新（不完全重训练，只更新关键参数）
    this.quickUpdate()

    // 生成实时预测
    return this.generatePrediction()
  }

  /**
   * 快速更新模型参数（用于实时更新）
   */
  quickUpdate() {
    // 只更新最近的趋势和波动性
    const recentData = this.historicalData.slice(-50) // 最近50个数据点

    if (recentData.length >= 2) {
      // 快速计算趋势
      const firstPoint = recentData[0]
      const lastPoint = recentData[recentData.length - 1]
      const timeDiff = (lastPoint.timestamp.getTime() - firstPoint.timestamp.getTime()) / (60 * 60 * 1000)

      if (timeDiff > 0) {
        this.modelState.trendSlope = (lastPoint.doseRate - firstPoint.doseRate) / timeDiff
      }

      // 快速更新波动性
      const values = recentData.map(item => item.doseRate)
      const mean = values.reduce((sum, val) => sum + val, 0) / values.length
      const variance = values.reduce((sum, val) => sum + Math.pow(val - mean, 2), 0) / values.length
      this.modelState.volatility = Math.sqrt(variance) / mean // 相对波动性
    }

    this.modelState.lastUpdate = new Date()
  }

  /**
   * 获取模型状态信息
   * @returns {Object} 模型状态
   */
  getModelInfo() {
    return {
      isInitialized: this.modelState.isInitialized,
      lastUpdate: this.modelState.lastUpdate,
      dataPoints: this.historicalData.length,
      trendSlope: this.modelState.trendSlope,
      volatility: this.modelState.volatility,
      confidence: this.calculateConfidence()
    }
  }
}

/**
 * 创建预测模型实例的工厂函数
 * @returns {RadiationPredictionModel} 预测模型实例
 */
export function createPredictionModel() {
  return new RadiationPredictionModel()
}
