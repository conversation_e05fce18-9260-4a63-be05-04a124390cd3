<template>
  <view class="settings-container">
    <!-- Toast容器 -->
    <ToastContainer />

    <!-- 用户信息卡片 -->
    <view class="user-info-card">
      <view class="user-avatar">
        <text class="avatar-text">{{ userProfile.name.charAt(0) }}</text>
      </view>
      <view class="user-details">
        <text class="user-name">{{ userProfile.name }}</text>
        <text class="user-email">{{ userProfile.email }}</text>
        <view class="user-stats">
          <view class="stat-item">
            <text class="stat-value">{{ userProfile.monitoringDays }}</text>
            <text class="stat-label">监测天数</text>
          </view>
          <view class="stat-item">
            <text class="stat-value">{{ userProfile.healthScore }}</text>
            <text class="stat-label">健康分数</text>
          </view>
          <view class="stat-item">
            <text class="stat-value">{{ userProfile.deviceCount }}</text>
            <text class="stat-label">设备数量</text>
          </view>
        </view>
      </view>
      <view class="user-actions">
        <view class="action-btn" @tap="editProfile">
          <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
            <path d="M11 4H4a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7"></path>
            <path d="M18.5 2.5a2.121 2.121 0 0 1 3 3L12 15l-4 1 1-4 9.5-9.5z"></path>
          </svg>
        </view>
      </view>
    </view>

    <!-- 设置分组 -->
    <view class="settings-sections">
      <!-- 监测设置 -->
      <view class="settings-section">
        <view class="section-header">
          <text class="section-title">监测设置</text>
          <text class="section-subtitle">配置辐射监测参数</text>
        </view>
        <view class="settings-items">
          <view class="setting-item">
            <view class="setting-info">
              <view class="setting-icon dose-rate">
                <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                  <path d="M22 12h-4l-3 9L9 3l-3 9H2"></path>
                </svg>
              </view>
              <view class="setting-details">
                <text class="setting-title">剂量率报警阈值</text>
                <text class="setting-desc">设置触发报警的剂量率水平</text>
              </view>
            </view>
            <view class="setting-control">
              <text class="control-value">{{ settings.maxDoseRate.toFixed(2) }} μSv/h</text>
              <view class="control-arrow" @tap="adjustDoseRateThreshold">
                <text>></text>
              </view>
            </view>
          </view>

          <view class="setting-item">
            <view class="setting-info">
              <view class="setting-icon interval">
                <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                  <circle cx="12" cy="12" r="10"></circle>
                  <path d="M12 6v6l4 2"></path>
                </svg>
              </view>
              <view class="setting-details">
                <text class="setting-title">监测间隔</text>
                <text class="setting-desc">数据采集的时间间隔</text>
              </view>
            </view>
            <view class="setting-control">
              <text class="control-value">{{ settings.monitoringInterval }}秒</text>
              <view class="control-arrow" @tap="adjustMonitoringInterval">
                <text>></text>
              </view>
            </view>
          </view>

          <view class="setting-item">
            <view class="setting-info">
              <view class="setting-icon upload">
                <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                  <path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4"></path>
                  <path d="M7 10l5-5 5 5"></path>
                  <path d="M12 15V5"></path>
                </svg>
              </view>
              <view class="setting-details">
                <text class="setting-title">自动上传数据</text>
                <text class="setting-desc">自动备份监测数据到云端</text>
              </view>
            </view>
            <view class="setting-control">
              <switch :checked="settings.autoUpload" @change="toggleAutoUpload" color="#3b82f6" />
            </view>
          </view>
        </view>
      </view>

      <!-- 通知设置 -->
      <view class="settings-section">
        <view class="section-header">
          <text class="section-title">通知设置</text>
          <text class="section-subtitle">管理提醒和通知方式</text>
        </view>
        <view class="settings-items">
          <view class="setting-item">
            <view class="setting-info">
              <view class="setting-icon sound">
                <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                  <path d="M11 5L6 9H2v6h4l5 4V5z"></path>
                  <path d="M19.07 4.93a10 10 0 0 1 0 14.14M15.54 8.46a5 5 0 0 1 0 7.07"></path>
                </svg>
              </view>
              <view class="setting-details">
                <text class="setting-title">声音提醒</text>
                <text class="setting-desc">超出阈值时播放提示音</text>
              </view>
            </view>
            <view class="setting-control">
              <switch :checked="settings.soundAlert" @change="toggleSoundAlert" color="#3b82f6" />
            </view>
          </view>

          <view class="setting-item">
            <view class="setting-info">
              <view class="setting-icon vibration">
                <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                  <path d="M7 3h10v18H7z"></path>
                  <path d="M3 8h2"></path>
                  <path d="M3 12h2"></path>
                  <path d="M3 16h2"></path>
                  <path d="M19 8h2"></path>
                  <path d="M19 12h2"></path>
                  <path d="M19 16h2"></path>
                </svg>
              </view>
              <view class="setting-details">
                <text class="setting-title">震动提醒</text>
                <text class="setting-desc">设备震动提醒功能</text>
              </view>
            </view>
            <view class="setting-control">
              <switch :checked="settings.vibrationAlert" @change="toggleVibrationAlert" color="#3b82f6" />
            </view>
          </view>

          <view class="setting-item">
            <view class="setting-info">
              <view class="setting-icon push">
                <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                  <path d="M18 8A6 6 0 0 0 6 8c0 7-3 9-3 9h18s-3-2-3-9"></path>
                  <path d="M13.73 21a2 2 0 0 1-3.46 0"></path>
                </svg>
              </view>
              <view class="setting-details">
                <text class="setting-title">推送通知</text>
                <text class="setting-desc">接收重要事件推送消息</text>
              </view>
            </view>
            <view class="setting-control">
              <switch :checked="settings.pushNotification" @change="togglePushNotification" color="#3b82f6" />
            </view>
          </view>
        </view>
      </view>

      <!-- 显示设置 -->
      <view class="settings-section">
        <view class="section-header">
          <text class="section-title">显示设置</text>
          <text class="section-subtitle">界面和显示偏好设置</text>
        </view>
        <view class="settings-items">
          <view class="setting-item">
            <view class="setting-info">
              <view class="setting-icon theme">
                <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                  <circle cx="12" cy="12" r="5"></circle>
                  <path d="M12 1v2"></path>
                  <path d="M12 21v2"></path>
                  <path d="M4.22 4.22l1.42 1.42"></path>
                  <path d="M18.36 18.36l1.42 1.42"></path>
                  <path d="M1 12h2"></path>
                  <path d="M21 12h2"></path>
                  <path d="M4.22 19.78l1.42-1.42"></path>
                  <path d="M18.36 5.64l1.42-1.42"></path>
                </svg>
              </view>
              <view class="setting-details">
                <text class="setting-title">主题模式</text>
                <text class="setting-desc">选择浅色或深色主题</text>
              </view>
            </view>
            <view class="setting-control">
              <text class="control-value">{{ settings.theme === 'light' ? '浅色' : '深色' }}</text>
              <view class="control-arrow" @tap="toggleTheme">
                <text>></text>
              </view>
            </view>
          </view>

          <view class="setting-item">
            <view class="setting-info">
              <view class="setting-icon language">
                <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                  <path d="M5 8l6 6"></path>
                  <path d="M4 14l6-6 2-3"></path>
                  <path d="M2 5h12"></path>
                  <path d="M7 2h1"></path>
                  <path d="M22 22l-5-10-5 10"></path>
                  <path d="M14 18h6"></path>
                </svg>
              </view>
              <view class="setting-details">
                <text class="setting-title">语言</text>
                <text class="setting-desc">选择应用程序语言</text>
              </view>
            </view>
            <view class="setting-control">
              <text class="control-value">简体中文</text>
              <view class="control-arrow" @tap="changeLanguage">
                <text>></text>
              </view>
            </view>
          </view>

          <view class="setting-item">
            <view class="setting-info">
              <view class="setting-icon units">
                <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                  <path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z"></path>
                  <path d="M14 2v6h6"></path>
                  <path d="M16 13H8"></path>
                  <path d="M16 17H8"></path>
                  <path d="M10 9H8"></path>
                </svg>
              </view>
              <view class="setting-details">
                <text class="setting-title">测量单位</text>
                <text class="setting-desc">选择数据显示单位</text>
              </view>
            </view>
            <view class="setting-control">
              <text class="control-value">{{ settings.units }}</text>
              <view class="control-arrow" @tap="changeUnits">
                <text>></text>
              </view>
            </view>
          </view>
        </view>
      </view>

      <!-- 数据管理 -->
      <view class="settings-section">
        <view class="section-header">
          <text class="section-title">数据管理</text>
          <text class="section-subtitle">备份、导出和清理数据</text>
        </view>
        <view class="settings-items">
          <view class="setting-item" @tap="exportData">
            <view class="setting-info">
              <view class="setting-icon export">
                <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                  <path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z"></path>
                  <path d="M14 2v6h6"></path>
                  <path d="M16 13H8"></path>
                  <path d="M16 17H8"></path>
                  <path d="M10 9H8"></path>
                </svg>
              </view>
              <view class="setting-details">
                <text class="setting-title">导出数据</text>
                <text class="setting-desc">导出所有监测数据</text>
              </view>
            </view>
            <view class="setting-control">
              <view class="control-arrow">
                <text>></text>
              </view>
            </view>
          </view>

          <view class="setting-item" @tap="backupData">
            <view class="setting-info">
              <view class="setting-icon backup">
                <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                  <path d="M22 11.08V12a10 10 0 1 1-5.93-9.14"></path>
                  <path d="M22 4L12 14.01l-3-3"></path>
                </svg>
              </view>
              <view class="setting-details">
                <text class="setting-title">数据备份</text>
                <text class="setting-desc">备份数据到云端</text>
              </view>
            </view>
            <view class="setting-control">
              <view class="control-arrow">
                <text>></text>
              </view>
            </view>
          </view>

          <view class="setting-item" @tap="clearData">
            <view class="setting-info">
              <view class="setting-icon clear">
                <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                  <path d="M3 6h18"></path>
                  <path d="M8 6V4a2 2 0 0 1 2-2h4a2 2 0 0 1 2 2v2"></path>
                  <path d="M19 6v14a2 2 0 0 1-2 2H7a2 2 0 0 1-2-2V6"></path>
                  <path d="M10 11v6"></path>
                  <path d="M14 11v6"></path>
                </svg>
              </view>
              <view class="setting-details">
                <text class="setting-title">清理数据</text>
                <text class="setting-desc">删除历史监测数据</text>
              </view>
            </view>
            <view class="setting-control">
              <view class="control-arrow">
                <text>></text>
              </view>
            </view>
          </view>
        </view>
      </view>

      <!-- 关于 -->
      <view class="settings-section">
        <view class="section-header">
          <text class="section-title">关于</text>
          <text class="section-subtitle">应用信息和帮助</text>
        </view>
        <view class="settings-items">
          <view class="setting-item" @tap="showAbout">
            <view class="setting-info">
              <view class="setting-icon info">
                <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                  <circle cx="12" cy="12" r="10"></circle>
                  <path d="M12 16v-4"></path>
                  <path d="M12 8h.01"></path>
                </svg>
              </view>
              <view class="setting-details">
                <text class="setting-title">应用信息</text>
                <text class="setting-desc">版本号、开发者信息</text>
              </view>
            </view>
            <view class="setting-control">
              <text class="control-value">v1.0.0</text>
              <view class="control-arrow">
                <text>></text>
              </view>
            </view>
          </view>

          <view class="setting-item" @tap="showHelp">
            <view class="setting-info">
              <view class="setting-icon help">
                <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                  <circle cx="12" cy="12" r="10"></circle>
                  <path d="M9.09 9a3 3 0 0 1 5.83 1c0 2-3 3-3 3"></path>
                  <path d="M12 17h.01"></path>
                </svg>
              </view>
              <view class="setting-details">
                <text class="setting-title">帮助与支持</text>
                <text class="setting-desc">使用指南和技术支持</text>
              </view>
            </view>
            <view class="setting-control">
              <view class="control-arrow">
                <text>></text>
              </view>
            </view>
          </view>
        </view>
      </view>
    </view>


    <!-- 底部导航栏 -->
    <BottomNavigation currentPage="settings" />

    <!-- 弹窗组件 -->
    <SettingsSelector
      v-model="showDoseRateModal"
      title="剂量率报警阈值"
      icon-text="⚡"
      icon-class="warning"
      description="设置触发报警的剂量率水平，当检测到的辐射剂量率超过此阈值时，系统将发出警报。"
      :options="doseRateOptions"
      :default-index="getDoseRateIndex()"
      :allow-custom="true"
      custom-placeholder="请输入自定义阈值"
      input-type="number"
      @confirm="onDoseRateConfirm"
    />

    <SettingsSelector
      v-model="showIntervalModal"
      title="监测间隔"
      icon-text="⏱️"
      icon-class="default"
      description="设置数据采集的时间间隔，较短的间隔可以提供更实时的监测，但会消耗更多电量。"
      :options="intervalOptions"
      :default-index="getIntervalIndex()"
      @confirm="onIntervalConfirm"
    />

    <SettingsSelector
      v-model="showThemeModal"
      title="主题模式"
      icon-text="🎨"
      icon-class="default"
      description="选择您喜欢的界面主题，浅色主题适合白天使用，深色主题适合夜间使用。"
      :options="themeOptions"
      :default-index="getThemeIndex()"
      @confirm="onThemeConfirm"
    />

    <SettingsSelector
      v-model="showLanguageModal"
      title="语言设置"
      icon-text="🌐"
      icon-class="default"
      description="选择应用程序的显示语言，重启应用后生效。"
      :options="languageOptions"
      :default-index="0"
      @confirm="onLanguageConfirm"
    />

    <SettingsSelector
      v-model="showUnitsModal"
      title="测量单位"
      icon-text="📏"
      icon-class="default"
      description="选择辐射剂量率的显示单位，不同单位适用于不同的测量场景。"
      :options="unitsOptions"
      :default-index="getUnitsIndex()"
      @confirm="onUnitsConfirm"
    />

    <SettingsSelector
      v-model="showExportModal"
      title="导出数据"
      icon-text="📤"
      icon-class="success"
      description="选择数据导出格式，导出的数据可用于进一步分析或备份。"
      :options="exportOptions"
      :default-index="0"
      @confirm="onExportConfirm"
    />

    <SettingsSelector
      v-model="showHelpModal"
      title="帮助与支持"
      icon-text="❓"
      icon-class="default"
      description="获取使用帮助和技术支持，解决您在使用过程中遇到的问题。"
      :options="helpOptions"
      :default-index="0"
      @confirm="onHelpConfirm"
    />

    <!-- 确认弹窗 -->
    <ConfirmModal
      v-model="showClearDataModal"
      title="确认清理"
      message="此操作将删除所有历史监测数据，且无法恢复。确定要继续吗？"
      icon-text="⚠️"
      icon-class="warning"
      cancel-text="取消"
      confirm-text="确定清理"
      confirm-class="danger"
      @confirm="onClearDataConfirm"
      @cancel="onClearDataCancel"
    />

    <!-- 应用信息弹窗 -->
    <InfoModal
      v-model="showAboutModal"
      title="关于应用"
      icon-text="ℹ️"
      :info-items="aboutInfo"
      description="本应用致力于提供专业的辐射监测服务，帮助用户实时监控环境辐射水平，保障健康安全。"
      confirm-text="确定"
    />
  </view>
</template>

<script>
import { ref, onMounted, onUnmounted } from 'vue'
import BottomNavigation from '../../components/BottomNavigation.vue'
import ToastContainer from '../../components/ToastContainer.vue'
import SettingsSelector from '../../components/SettingsSelector.vue'
import ConfirmModal from '../../components/ConfirmModal.vue'
import InfoModal from '../../components/InfoModal.vue'
import toastManager from '../../utils/toastManager.js'
import languageManager from '../../utils/languageManager.js'
import themeManager from '../../utils/themeManager.js'

// 用户配置数据
const userProfile = ref({
  name: '张三',
  email: '<EMAIL>',
  monitoringDays: 127,
  healthScore: 85,
  deviceCount: 2
})

// 设置数据
const settings = ref({
  maxDoseRate: 1.0,
  monitoringInterval: 10,
  autoUpload: true,
  soundAlert: true,
  vibrationAlert: true,
  pushNotification: true,
  theme: 'light',
  language: 'zh-CN',
  units: 'μSv/h'
})

// 弹窗状态
const showDoseRateModal = ref(false)
const showIntervalModal = ref(false)
const showThemeModal = ref(false)
const showLanguageModal = ref(false)
const showUnitsModal = ref(false)
const showExportModal = ref(false)
const showHelpModal = ref(false)
const showClearDataModal = ref(false)
const showAboutModal = ref(false)

// 选项数据
const doseRateOptions = ref([
  { icon: '🟢', label: '0.5 μSv/h', desc: '低风险阈值', value: 0.5 },
  { icon: '🟡', label: '1.0 μSv/h', desc: '标准阈值', value: 1.0 },
  { icon: '🟠', label: '2.0 μSv/h', desc: '中等风险阈值', value: 2.0 },
  { icon: '🔴', label: '5.0 μSv/h', desc: '高风险阈值', value: 5.0 },
  { icon: '⚙️', label: '自定义', desc: '输入自定义阈值', custom: true }
])

const intervalOptions = ref([
  { icon: '⚡', label: '1秒', desc: '实时监测', value: 1 },
  { icon: '🔄', label: '5秒', desc: '快速更新', value: 5 },
  { icon: '⏱️', label: '10秒', desc: '标准间隔', value: 10 },
  { icon: '⏰', label: '30秒', desc: '节能模式', value: 30 },
  { icon: '🕐', label: '60秒', desc: '省电模式', value: 60 }
])

const themeOptions = ref(themeManager.getSupportedThemes().map(theme => ({
  icon: theme.key === 'light' ? '☀️' : '🌙',
  label: theme.name,
  desc: theme.key === 'light' ? '适合白天使用' : '适合夜间使用',
  value: theme.key
})))

const languageOptions = ref(languageManager.getSupportedLanguages().map(lang => ({
  icon: lang.icon,
  label: lang.name,
  desc: lang.name,
  value: lang.code
})))

const unitsOptions = ref([
  { icon: '📏', label: 'μSv/h', desc: '微西弗每小时', value: 'μSv/h' },
  { icon: '📐', label: 'mSv/h', desc: '毫西弗每小时', value: 'mSv/h' },
  { icon: '📊', label: 'R/h', desc: '伦琴每小时', value: 'R/h' },
  { icon: '📈', label: 'mR/h', desc: '毫伦琴每小时', value: 'mR/h' }
])

const exportOptions = ref([
  { icon: '📊', label: 'Excel格式', desc: '适合数据分析', value: 'excel' },
  { icon: '📋', label: 'CSV格式', desc: '通用数据格式', value: 'csv' },
  { icon: '📄', label: 'JSON格式', desc: '程序数据格式', value: 'json' },
  { icon: '📧', label: '发送到邮箱', desc: '邮件发送数据', value: 'email' }
])

const helpOptions = ref([
  { icon: '📖', label: '使用指南', desc: '详细操作说明', value: 'guide' },
  { icon: '❓', label: '常见问题', desc: 'FAQ解答', value: 'faq' },
  { icon: '🎥', label: '视频教程', desc: '视频演示', value: 'video' },
  { icon: '👨‍💼', label: '联系客服', desc: '在线技术支持', value: 'support' }
])

// 统一的导航函数
const navigateTo = (page) => {
  const routes = {
    'dashboard': '/pages/dashboard/dashboard',
    'charts': '/pages/charts/charts',
    'health': '/pages/health/health',
    'map': '/pages/map/map',
    'settings': '/pages/settings/settings',
    'notification': '/pages/notification/notification'
  }

  if (routes[page]) {
    uni.navigateTo({
      url: routes[page],
      fail: (err) => {
        console.error('导航失败:', err)
        uni.showToast({
          title: '页面跳转失败',
          icon: 'none'
        })
      }
    })
  }
}

// 初始化设置
const initializeSettings = () => {
  try {
    // 加载用户资料
    const savedProfile = uni.getStorageSync('userProfile')
    if (savedProfile) {
      Object.assign(userProfile.value, savedProfile)
    }

    // 加载设置
    const savedSettings = uni.getStorageSync('appSettings')
    if (savedSettings) {
      Object.assign(settings.value, savedSettings)
    }

    // 同步当前语言和主题设置
    settings.value.language = languageManager.getCurrentLanguage()
    settings.value.theme = themeManager.getCurrentTheme()

    // 确保主题管理器已初始化，然后应用当前主题
    themeManager.init()
    themeManager.applyTheme(settings.value.theme)

  } catch (error) {
    console.error('加载设置失败:', error)
  }
}

// 保存设置到本地存储
const saveSettings = () => {
  try {
    uni.setStorageSync('appSettings', settings.value)
    uni.setStorageSync('userProfile', userProfile.value)
  } catch (error) {
    console.error('保存设置失败:', error)
  }
}

// 编辑用户资料
const editProfile = () => {
  uni.showModal({
    title: '编辑用户名',
    content: '请输入新的用户名',
    editable: true,
    placeholderText: userProfile.value.name,
    confirmColor: '#3b82f6',
    success: (res) => {
      if (res.confirm && res.content && res.content.trim()) {
        userProfile.value.name = res.content.trim()
        toastManager.success(`👤 用户名已更新为：${res.content.trim()}`, {
          duration: 2500,
          showCountdown: true
        })

        // 模拟保存到本地存储
        uni.setStorageSync('userProfile', userProfile.value)
      }
    }
  })
}

// 调整剂量率阈值
const adjustDoseRateThreshold = () => {
  showDoseRateModal.value = true
}

// 调整监测间隔
const adjustMonitoringInterval = () => {
  showIntervalModal.value = true
}

// 切换开关函数
const toggleAutoUpload = (e) => {
  settings.value.autoUpload = e.detail.value
  saveSettings() // 保存设置

  // 模拟实际效果
  if (settings.value.autoUpload) {
    // 可以在这里添加启动自动上传的逻辑
    console.log('启动自动上传服务')
  } else {
    console.log('停止自动上传服务')
  }

  toastManager.success(
    settings.value.autoUpload ? '✅ 已开启自动上传' : '❌ 已关闭自动上传',
    {
      duration: 2000,
      showCountdown: false
    }
  )
}

const toggleSoundAlert = (e) => {
  settings.value.soundAlert = e.detail.value
  saveSettings() // 保存设置

  // 模拟声音测试
  if (settings.value.soundAlert) {
    // 播放测试音效
    uni.vibrateShort()
  }

  toastManager.success(
    settings.value.soundAlert ? '🔊 已开启声音提醒' : '🔇 已关闭声音提醒',
    {
      duration: 2000,
      showCountdown: false
    }
  )
}

const toggleVibrationAlert = (e) => {
  settings.value.vibrationAlert = e.detail.value
  saveSettings() // 保存设置

  // 模拟震动测试
  if (settings.value.vibrationAlert) {
    uni.vibrateLong()
  }

  toastManager.success(
    settings.value.vibrationAlert ? '📳 已开启震动提醒' : '📴 已关闭震动提醒',
    {
      duration: 2000,
      showCountdown: false
    }
  )
}

const togglePushNotification = (e) => {
  settings.value.pushNotification = e.detail.value
  saveSettings() // 保存设置

  // 模拟推送通知测试
  if (settings.value.pushNotification) {
    toastManager.info('📱 推送通知已启用，您将收到重要提醒', {
      duration: 3000,
      showCountdown: true
    })
  }

  toastManager.success(
    settings.value.pushNotification ? '🔔 已开启推送通知' : '🔕 已关闭推送通知',
    {
      duration: 2000,
      showCountdown: false
    }
  )
}

// 主题切换
const toggleTheme = () => {
  showThemeModal.value = true
}

// 语言设置
const changeLanguage = () => {
  showLanguageModal.value = true
}

// 单位设置
const changeUnits = () => {
  showUnitsModal.value = true
}

// 数据管理函数
const exportData = () => {
  showExportModal.value = true
}

const backupData = () => {
  toastManager.info('☁️ 正在备份数据到云端...', {
    duration: 2000,
    showCountdown: true
  })

  // 模拟备份过程
  setTimeout(() => {
    try {
      // 收集所有需要备份的数据
      const allData = {
        userProfile: userProfile.value,
        settings: settings.value,
        monitoringData: uni.getStorageSync('monitoringData') || [],
        healthData: uni.getStorageSync('healthData') || [],
        chartData: uni.getStorageSync('chartData') || {},
        deviceHistory: uni.getStorageSync('deviceHistory') || [],
        alertHistory: uni.getStorageSync('alertHistory') || []
      }

      // 创建备份数据
      const backupData = {
        data: allData,
        timestamp: new Date().toISOString(),
        version: '1.0.0',
        deviceInfo: {
          platform: uni.getSystemInfoSync().platform,
          version: uni.getSystemInfoSync().version
        }
      }

      // 计算备份数据大小
      const backupSize = JSON.stringify(backupData).length
      const sizeInKB = (backupSize / 1024).toFixed(2)

      // 保存到本地存储作为备份
      uni.setStorageSync('dataBackup', backupData)
      uni.setStorageSync('lastBackupTime', new Date().toISOString())

      toastManager.success(`☁️ 数据备份成功 (${sizeInKB}KB)`, {
        duration: 3000,
        showCountdown: true
      })

      // 显示备份详情
      setTimeout(() => {
        const itemCount = Object.keys(allData).length
        toastManager.info(`📦 已备份 ${itemCount} 类数据，备份时间: ${new Date().toLocaleString()}`, {
          duration: 4000,
          showCountdown: true
        })
      }, 3500)

    } catch (error) {
      console.error('备份失败:', error)
      toastManager.error('❌ 备份失败，请重试', {
        duration: 3000,
        showCountdown: true
      })
    }
  }, 2000)
}

const clearData = () => {
  showClearDataModal.value = true
}

const onClearDataConfirm = () => {
  toastManager.warning('🗑️ 正在清理数据...', {
    duration: 1500,
    showCountdown: true
  })

  setTimeout(() => {
    try {
      // 清理相关数据（保留用户设置）
      const keysToRemove = [
        'monitoringData',
        'healthData',
        'chartData',
        'dataBackup',
        'export_csv',
        'export_json',
        'export_excel',
        'deviceHistory',
        'alertHistory',
        'reportHistory'
      ]

      let clearedCount = 0
      keysToRemove.forEach(key => {
        try {
          uni.removeStorageSync(key)
          clearedCount++
        } catch (e) {
          console.warn(`清理 ${key} 失败:`, e)
        }
      })

      // 重置用户统计数据
      const oldDays = userProfile.value.monitoringDays
      const oldScore = userProfile.value.healthScore

      userProfile.value.monitoringDays = 0
      userProfile.value.healthScore = 100
      saveSettings()

      toastManager.success(`🗑️ 数据清理完成，已清理 ${clearedCount} 项数据`, {
        duration: 3000,
        showCountdown: true
      })

      // 显示清理详情
      setTimeout(() => {
        toastManager.info(`📊 监测天数: ${oldDays} → 0，健康分数: ${oldScore} → 100`, {
          duration: 4000,
          showCountdown: true
        })
      }, 3500)

    } catch (error) {
      console.error('清理失败:', error)
      toastManager.error('❌ 清理失败，请重试', {
        duration: 3000,
        showCountdown: true
      })
    }
  }, 1500)
}

const onClearDataCancel = () => {
  toastManager.info('❌ 已取消清理操作', {
    duration: 2000,
    showCountdown: false
  })
}

// 关于和帮助函数
const showAbout = () => {
  showAboutModal.value = true
}

// 应用信息数据
const aboutInfo = ref([
  { icon: '🔬', label: '应用名称', value: '智能辐射监测系统' },
  { icon: '🏷️', label: '版本号', value: 'v1.0.0' },
  { icon: '👨‍💻', label: '开发者', value: '科技有限公司' },
  { icon: '📅', label: '发布日期', value: '2024年' }
])

const showHelp = () => {
  showHelpModal.value = true
}

// 获取当前设置对应的索引
const getDoseRateIndex = () => {
  const index = doseRateOptions.value.findIndex(option => option.value === settings.value.maxDoseRate)
  return index >= 0 ? index : 1 // 默认1.0 μSv/h
}

const getIntervalIndex = () => {
  const index = intervalOptions.value.findIndex(option => option.value === settings.value.monitoringInterval)
  return index >= 0 ? index : 2 // 默认10秒
}

const getThemeIndex = () => {
  const index = themeOptions.value.findIndex(option => option.value === settings.value.theme)
  return index >= 0 ? index : 0 // 默认浅色
}

const getUnitsIndex = () => {
  const index = unitsOptions.value.findIndex(option => option.value === settings.value.units)
  return index >= 0 ? index : 0 // 默认μSv/h
}

// 弹窗确认处理函数
const onDoseRateConfirm = (result) => {
  let value = result.option.value
  if (result.option.custom && result.customValue) {
    value = parseFloat(result.customValue)
    if (isNaN(value) || value <= 0) {
      toastManager.error('请输入有效的数值', {
        duration: 3000,
        showCountdown: true
      })
      return
    }
  }

  settings.value.maxDoseRate = value
  saveSettings() // 保存设置
  toastManager.success(`⚡ 剂量率阈值已设置为 ${value} μSv/h`, {
    duration: 2500,
    showCountdown: true
  })
}

const onIntervalConfirm = (result) => {
  settings.value.monitoringInterval = result.option.value
  saveSettings() // 保存设置
  toastManager.success(`⏱️ 监测间隔已设置为 ${result.option.value}秒`, {
    duration: 2500,
    showCountdown: true
  })
}

const onThemeConfirm = (result) => {
  settings.value.theme = result.option.value
  saveSettings() // 保存设置

  // 使用主题管理器切换主题
  themeManager.setTheme(result.option.value)

  const themeIcon = result.option.value === 'light' ? '☀️' : '🌙'
  const themeName = result.option.value === 'light' ? '浅色' : '深色'

  toastManager.success(`${themeIcon} ${languageManager.t('theme_switched')}为${themeName}主题`, {
    duration: 2500,
    showCountdown: true
  })
}

const onLanguageConfirm = (result) => {
  // 使用语言管理器切换语言
  languageManager.setLanguage(result.option.value)

  // 更新设置
  settings.value.language = result.option.value
  saveSettings()

  // 重新加载语言选项（因为语言已切换）
  languageOptions.value = languageManager.getSupportedLanguages().map(lang => ({
    icon: lang.icon,
    label: lang.name,
    desc: lang.name,
    value: lang.code
  }))

  toastManager.success(`🌐 ${languageManager.t('language_switched')} ${result.option.label}`, {
    duration: 2500,
    showCountdown: true
  })

  // 提示用户重启应用以完全生效
  setTimeout(() => {
    toastManager.info('💡 重启应用以完全应用语言设置', {
      duration: 3000,
      showCountdown: true
    })
  }, 3000)
}

const onUnitsConfirm = (result) => {
  settings.value.units = result.option.value
  saveSettings() // 保存设置
  toastManager.success(`📏 测量单位已设置为 ${result.option.value}`, {
    duration: 2500,
    showCountdown: true
  })
}

const onExportConfirm = (result) => {
  toastManager.info(`📤 正在准备${result.option.label}...`, {
    duration: 1500,
    showCountdown: true
  })

  setTimeout(() => {
    try {
      // 创建导出数据
      const exportData = {
        userProfile: userProfile.value,
        settings: settings.value,
        exportTime: new Date().toISOString(),
        format: result.option.value,
        version: '1.0.0'
      }

      // 根据不同格式处理数据
      let dataString = ''
      let fileName = ''
      let mimeType = ''

      switch (result.option.value) {
        case 'excel':
        case 'csv':
          dataString = `用户名,${exportData.userProfile.name}\n邮箱,${exportData.userProfile.email}\n监测天数,${exportData.userProfile.monitoringDays}\n健康分数,${exportData.userProfile.healthScore}\n设备数量,${exportData.userProfile.deviceCount}\n导出时间,${exportData.exportTime}`
          fileName = `health_data_${new Date().toISOString().split('T')[0]}.csv`
          mimeType = 'text/csv'
          break
        case 'json':
          dataString = JSON.stringify(exportData, null, 2)
          fileName = `health_data_${new Date().toISOString().split('T')[0]}.json`
          mimeType = 'application/json'
          break
        case 'email':
          // 模拟邮件发送
          uni.showModal({
            title: '邮件发送',
            content: `数据已准备完成，将发送到 ${userProfile.value.email}`,
            confirmText: '确定',
            cancelText: '取消',
            success: (res) => {
              if (res.confirm) {
                toastManager.success('📧 邮件发送成功', {
                  duration: 3000,
                  showCountdown: true
                })
              }
            }
          })
          return
      }

      // 保存导出数据到本地存储
      uni.setStorageSync(`export_${result.option.value}`, dataString)

      // 模拟文件下载（在实际应用中可以使用文件系统API）
      if (typeof window !== 'undefined' && window.document) {
        const blob = new Blob([dataString], { type: mimeType })
        const url = URL.createObjectURL(blob)
        const a = document.createElement('a')
        a.href = url
        a.download = fileName
        document.body.appendChild(a)
        a.click()
        document.body.removeChild(a)
        URL.revokeObjectURL(url)
      }

      toastManager.success(`${result.option.icon} ${result.option.label}导出成功`, {
        duration: 3000,
        showCountdown: true
      })
    } catch (error) {
      console.error('导出失败:', error)
      toastManager.error('❌ 导出失败，请重试', {
        duration: 3000,
        showCountdown: true
      })
    }
  }, 1500)
}

const onHelpConfirm = (result) => {
  toastManager.info(`正在打开${result.option.label}`, {
    duration: 2500,
    showCountdown: true
  })
}

export default {
  name: 'Settings',
  components: {
    BottomNavigation,
    ToastContainer,
    SettingsSelector,
    ConfirmModal,
    InfoModal
  },
  setup() {
    // 初始化设置
    initializeSettings()

    // 监听语言变化事件
    const handleLanguageChange = (language) => {
      console.log('语言已切换到:', language)
      // 这里可以添加更多语言切换后的处理逻辑
    }

    // 监听主题变化事件
    const handleThemeChange = (theme) => {
      console.log('主题已切换到:', theme)
      // 这里可以添加更多主题切换后的处理逻辑
    }

    // 注册事件监听器
    onMounted(() => {
      uni.$on('languageChanged', handleLanguageChange)
      uni.$on('themeChanged', handleThemeChange)
    })

    // 清理事件监听器
    onUnmounted(() => {
      uni.$off('languageChanged', handleLanguageChange)
      uni.$off('themeChanged', handleThemeChange)
    })

    return {
      userProfile,
      settings,
      navigateTo,
      initializeSettings,
      saveSettings,
      adjustDoseRateThreshold,
      languageManager,
      themeManager,
      adjustMonitoringInterval,
      toggleAutoUpload,
      toggleSoundAlert,
      toggleVibrationAlert,
      togglePushNotification,
      toggleTheme,
      changeLanguage,
      changeUnits,
      exportData,
      backupData,
      clearData,
      showAbout,
      showHelp,
      // 弹窗状态
      showDoseRateModal,
      showIntervalModal,
      showThemeModal,
      showLanguageModal,
      showUnitsModal,
      showExportModal,
      showHelpModal,
      showClearDataModal,
      showAboutModal,
      // 选项数据
      doseRateOptions,
      intervalOptions,
      themeOptions,
      languageOptions,
      unitsOptions,
      exportOptions,
      helpOptions,
      // 索引获取函数
      getDoseRateIndex,
      getIntervalIndex,
      getThemeIndex,
      getUnitsIndex,
      // 确认处理函数
      onDoseRateConfirm,
      onIntervalConfirm,
      onThemeConfirm,
      onLanguageConfirm,
      onUnitsConfirm,
      onExportConfirm,
      onHelpConfirm,
      onClearDataConfirm,
      onClearDataCancel,
      // 应用信息数据
      aboutInfo
    }
  }
}
</script>

<style scoped>
/* 全局容器样式 */
.settings-container {
  min-height: 100vh;
  background: #ffffff;
  padding-bottom: 120px;
  position: relative;
  font-family: -apple-system, BlinkMacSystemFont, 'SF Pro Display', sans-serif;
  overflow-x: hidden;
}

/* 背景装饰元素 */
.settings-container::before {
  content: '';
  position: fixed;
  top: -50%;
  right: -50%;
  width: 100%;
  height: 100%;
  background: radial-gradient(circle, rgba(59, 130, 246, 0.03) 0%, transparent 70%);
  animation: backgroundFloat 20s ease-in-out infinite;
  pointer-events: none;
  z-index: 0;
}

.settings-container::after {
  content: '';
  position: fixed;
  bottom: -50%;
  left: -50%;
  width: 100%;
  height: 100%;
  background: radial-gradient(circle, rgba(16, 185, 129, 0.02) 0%, transparent 70%);
  animation: backgroundFloat 25s ease-in-out infinite reverse;
  pointer-events: none;
  z-index: 0;
}

@keyframes backgroundFloat {
  0%, 100% {
    transform: translate(0, 0) rotate(0deg);
  }
  33% {
    transform: translate(30px, -30px) rotate(120deg);
  }
  66% {
    transform: translate(-20px, 20px) rotate(240deg);
  }
}





/* 用户信息卡片 */
.user-info-card {
  margin: 44px 16px 20px;
  padding: 24px;
  background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
  border-radius: 20px;
  box-shadow:
    0 8px 32px rgba(59, 130, 246, 0.3),
    0 4px 16px rgba(59, 130, 246, 0.2),
    inset 0 1px 0 rgba(255, 255, 255, 0.2);
  display: flex;
  align-items: center;
  gap: 16px;
  border: 1px solid rgba(255, 255, 255, 0.2);
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
  animation: cardSlideIn 0.6s ease-out;
  z-index: 10;
}

.user-info-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.6s;
}

.user-info-card:hover::before {
  left: 100%;
}

@keyframes cardSlideIn {
  from {
    opacity: 0;
    transform: translateY(30px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

.user-info-card:active {
  transform: scale(0.98);
  box-shadow: 0 4px 20px rgba(59, 130, 246, 0.4);
}

.user-avatar {
  width: 64px;
  height: 64px;
  border-radius: 20px;
  background: rgba(255, 255, 255, 0.2);
  backdrop-filter: blur(10px);
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 4px 16px rgba(255, 255, 255, 0.1);
  flex-shrink: 0;
  border: 1px solid rgba(255, 255, 255, 0.3);
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
  animation: avatarFloat 3s ease-in-out infinite;
}

.user-avatar::before {
  content: '';
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background: conic-gradient(from 0deg, transparent, rgba(255, 255, 255, 0.3), transparent);
  animation: avatarRotate 4s linear infinite;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.user-avatar:hover::before {
  opacity: 1;
}

.user-avatar:hover {
  transform: scale(1.05);
  box-shadow: 0 6px 24px rgba(255, 255, 255, 0.2);
}

@keyframes avatarFloat {
  0%, 100% { transform: translateY(0px); }
  50% { transform: translateY(-3px); }
}

@keyframes avatarRotate {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

.avatar-text {
  font-size: 24px;
  font-weight: 700;
  color: white;
  position: relative;
  z-index: 1;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.user-details {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.user-name {
  font-size: 20px;
  font-weight: 700;
  color: #ffffff;
  margin-bottom: 2px;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.user-email {
  font-size: 14px;
  color: rgba(255, 255, 255, 0.8);
  margin-bottom: 12px;
}

.user-stats {
  display: flex;
  gap: 12px;
  align-items: flex-start;
  margin-top: 4px;
}

.stat-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 6px;
  padding: 12px 8px;
  border-radius: 16px;
  background: rgba(255, 255, 255, 0.15);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.25);
  transition: all 0.3s ease;
  animation: statFadeIn 0.8s ease-out;
  width: 64px;
  height: 64px;
  position: relative;
  overflow: hidden;
}

.stat-item:nth-child(1) { animation-delay: 0.2s; }
.stat-item:nth-child(2) { animation-delay: 0.4s; }
.stat-item:nth-child(3) { animation-delay: 0.6s; }

.stat-item:hover {
  transform: translateY(-2px);
  background: rgba(255, 255, 255, 0.2);
  box-shadow: 0 4px 12px rgba(255, 255, 255, 0.1);
}

@keyframes statFadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.stat-value {
  font-size: 20px;
  font-weight: 800;
  color: #ffffff;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
  line-height: 1;
  text-align: center;
  white-space: nowrap;
  animation: numberPulse 3s ease-in-out infinite;
}

@keyframes numberPulse {
  0%, 100% {
    transform: scale(1);
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
  }
  50% {
    transform: scale(1.05);
    text-shadow: 0 3px 6px rgba(0, 0, 0, 0.3);
  }
}

.stat-label {
  font-size: 10px;
  color: rgba(255, 255, 255, 0.8);
  font-weight: 600;
  text-align: center;
  line-height: 1.2;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.user-actions {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.action-btn {
  width: 44px;
  height: 44px;
  border-radius: 14px;
  background: rgba(255, 255, 255, 0.2);
  backdrop-filter: blur(10px);
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
  box-shadow: 0 4px 12px rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.3);
}

.action-btn:active {
  transform: scale(0.95);
  background: rgba(255, 255, 255, 0.3);
  box-shadow: 0 2px 8px rgba(255, 255, 255, 0.2);
}

.action-btn svg {
  width: 20px;
  height: 20px;
  color: white;
}

/* 设置分组 */
.settings-sections {
  display: flex;
  flex-direction: column;
  gap: 20px;
  margin: 0 16px;
  position: relative;
  z-index: 10;
}

.settings-section {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 20px;
  padding: 20px;
  box-shadow:
    0 8px 32px rgba(0, 0, 0, 0.1),
    0 4px 16px rgba(0, 0, 0, 0.05);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(226, 232, 240, 0.6);
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  animation: sectionSlideUp 0.6s ease-out;
  position: relative;
  overflow: hidden;
}

.settings-section::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 2px;
  background: linear-gradient(90deg, transparent, #3b82f6, transparent);
  transition: left 0.6s ease;
}

.settings-section:hover::before {
  left: 100%;
}

.settings-section:hover {
  transform: translateY(-4px);
  box-shadow:
    0 12px 48px rgba(0, 0, 0, 0.15),
    0 8px 24px rgba(0, 0, 0, 0.08);
}

@keyframes sectionSlideUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.section-header {
  margin-bottom: 20px;
}

.section-title {
  display: block;
  font-size: 18px;
  font-weight: 700;
  color: #0f172a;
  margin-bottom: 4px;
}

.section-subtitle {
  font-size: 14px;
  color: #64748b;
}

.settings-items {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.setting-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 18px;
  background: rgba(255, 255, 255, 0.8);
  border-radius: 16px;
  border: 1px solid rgba(226, 232, 240, 0.6);
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
  position: relative;
  overflow: hidden;
  animation: itemFadeIn 0.6s ease-out;
}

.setting-item::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(59, 130, 246, 0.1), transparent);
  transition: left 0.5s ease;
}

.setting-item:hover::before {
  left: 100%;
}

.setting-item:hover {
  background: rgba(248, 250, 252, 0.95);
  transform: translateX(4px);
  box-shadow:
    0 8px 24px rgba(0, 0, 0, 0.1),
    0 4px 12px rgba(59, 130, 246, 0.1);
  border-color: rgba(59, 130, 246, 0.3);
}

.setting-item:active {
  transform: translateX(4px) scale(0.98);
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.08);
}

@keyframes itemFadeIn {
  from {
    opacity: 0;
    transform: translateX(-20px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

.setting-info {
  display: flex;
  align-items: center;
  gap: 12px;
  flex: 1;
}

.setting-icon {
  width: 40px;
  height: 40px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  animation: iconFloat 3s ease-in-out infinite;
}

.setting-icon::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 0;
  height: 0;
  background: rgba(255, 255, 255, 0.3);
  border-radius: 50%;
  transform: translate(-50%, -50%);
  transition: all 0.3s ease;
}

.setting-icon:hover::before {
  width: 100%;
  height: 100%;
}

.setting-icon:hover {
  transform: scale(1.1) rotate(5deg);
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
}

.setting-icon:active {
  transform: scale(0.95);
}

@keyframes iconFloat {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-2px);
  }
}

.setting-icon text {
  font-size: 18px;
  color: #ffffff;
  position: relative;
  z-index: 1;
  transition: all 0.3s ease;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
}

.setting-icon:hover text {
  transform: scale(1.1);
}

.setting-icon.dose-rate {
  background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
}

.setting-icon.interval {
  background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
}

.setting-icon.upload {
  background: linear-gradient(135deg, #10b981 0%, #059669 100%);
}

.setting-icon.sound {
  background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
}

.setting-icon.vibration {
  background: linear-gradient(135deg, #8b5cf6 0%, #7c3aed 100%);
}

.setting-icon.push {
  background: linear-gradient(135deg, #06b6d4 0%, #0891b2 100%);
}

.setting-icon.theme {
  background: linear-gradient(135deg, #6366f1 0%, #4f46e5 100%);
}

.setting-icon.language {
  background: linear-gradient(135deg, #ec4899 0%, #db2777 100%);
}

.setting-icon.units {
  background: linear-gradient(135deg, #84cc16 0%, #65a30d 100%);
}

.setting-icon.export {
  background: linear-gradient(135deg, #0ea5e9 0%, #0284c7 100%);
}

.setting-icon.backup {
  background: linear-gradient(135deg, #22c55e 0%, #16a34a 100%);
}

.setting-icon.clear {
  background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
}

.setting-icon.info {
  background: linear-gradient(135deg, #64748b 0%, #475569 100%);
}

.setting-icon.help {
  background: linear-gradient(135deg, #f97316 0%, #ea580c 100%);
}

.setting-icon svg {
  width: 20px;
  height: 20px;
  color: #ffffff;
}

.setting-details {
  flex: 1;
}

.setting-title {
  display: block;
  font-size: 16px;
  font-weight: 600;
  color: #0f172a;
  margin-bottom: 4px;
}

.setting-desc {
  font-size: 14px;
  color: #64748b;
  line-height: 1.4;
}

.setting-control {
  display: flex;
  align-items: center;
  gap: 8px;
}

.control-value {
  font-size: 14px;
  color: #64748b;
  font-weight: 500;
}

.control-arrow {
  width: 28px;
  height: 28px;
  border-radius: 14px;
  background: linear-gradient(135deg, rgba(59, 130, 246, 0.1) 0%, rgba(59, 130, 246, 0.15) 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  border: 1px solid rgba(59, 130, 246, 0.2);
  position: relative;
  overflow: hidden;
  animation: arrowPulse 2s ease-in-out infinite;
}

.control-arrow::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(59, 130, 246, 0.3), transparent);
  transition: left 0.5s ease;
}

.control-arrow:hover::before {
  left: 100%;
}

.control-arrow:hover {
  background: linear-gradient(135deg, rgba(59, 130, 246, 0.2) 0%, rgba(59, 130, 246, 0.3) 100%);
  transform: translateX(3px) scale(1.1);
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
  border-color: rgba(59, 130, 246, 0.4);
}

.control-arrow:active {
  transform: translateX(3px) scale(0.95);
  box-shadow: 0 2px 8px rgba(59, 130, 246, 0.3);
}

@keyframes arrowPulse {
  0%, 100% {
    box-shadow: 0 2px 8px rgba(59, 130, 246, 0.2);
  }
  50% {
    box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
  }
}

.control-arrow text {
  font-size: 16px;
  color: #3b82f6;
  font-weight: 700;
  position: relative;
  z-index: 1;
  transition: all 0.3s ease;
  animation: arrowBounce 2s ease-in-out infinite;
}

@keyframes arrowBounce {
  0%, 100% {
    transform: translateX(0px);
  }
  50% {
    transform: translateX(2px);
  }
}

.control-arrow:hover text {
  color: #1d4ed8;
  transform: translateX(2px);
}



/* 响应式优化 */
@media (max-width: 375px) {
  .settings-sections {
    margin: 0 12px;
  }
}

/* 页面动画 */
.settings-container > * {
  animation: fadeInUp 0.6s ease forwards;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.user-info-card { animation-delay: 0.1s; }
.settings-sections { animation-delay: 0.2s; }

/* 设置分组动画延迟 */
.settings-section:nth-child(1) { animation-delay: 0.3s; }
.settings-section:nth-child(2) { animation-delay: 0.4s; }
.settings-section:nth-child(3) { animation-delay: 0.5s; }
.settings-section:nth-child(4) { animation-delay: 0.6s; }
.settings-section:nth-child(5) { animation-delay: 0.7s; }

/* 设置项动画延迟 */
.setting-item:nth-child(1) { animation-delay: 0.1s; }
.setting-item:nth-child(2) { animation-delay: 0.2s; }
.setting-item:nth-child(3) { animation-delay: 0.3s; }
.setting-item:nth-child(4) { animation-delay: 0.4s; }
.setting-item:nth-child(5) { animation-delay: 0.5s; }

/* 图标动画延迟 */
.setting-icon:nth-child(1) { animation-delay: 0.2s; }
.setting-icon:nth-child(2) { animation-delay: 0.4s; }
.setting-icon:nth-child(3) { animation-delay: 0.6s; }
.setting-icon:nth-child(4) { animation-delay: 0.8s; }
.setting-icon:nth-child(5) { animation-delay: 1.0s; }
</style>